# Edit User Feature Test Guide

This document provides a comprehensive test guide for the newly implemented "edit subscription user" feature.

## Backend API Testing

### 1. Edit User Endpoint
- **URL**: `PUT /edit_user`
- **Authentication**: Admin token required
- **Content-Type**: `application/json`

### 2. Request Payload Example
```json
{
  "user_id": 1,
  "address": "<EMAIL>",
  "bandwidth": 200,
  "traffic": 2000,
  "activated": true,
  "max_ports_per_server": 10,
  "bill_type": {
    "Cycle": {
      "days": 60,
      "price": 150
    }
  },
  "total_days": 60,
  "lines": [1, 2, 3],
  "allow_forward_endpoint": true
}
```

### 3. Expected Response
- **Success**: `200 OK` with "Ok" response body
- **Error**: `400/500` with error details

## Frontend Testing

### 1. Access Edit Feature
1. Navigate to Subscription Management page
2. Locate a subscription in the table
3. Click the edit button (info-colored circular button with edit icon)

### 2. Edit Dialog Verification
- Dialog should open with title "Edit Subscription"
- All form fields should be pre-populated with existing user data
- Form validation should work (required fields, email format, etc.)

### 3. Form Fields to Test
- **Email Address**: Should be pre-filled and editable
- **Bandwidth**: Should show current value, allow null for unlimited
- **Traffic (GB)**: Should convert from bytes to GB for display
- **Max Ports per Server**: Should show current value
- **Billing Type**: Should reflect current billing type (Cycle/One-time)
- **Cycle/One-time specific fields**: Should show based on billing type
- **Lines**: Should show currently selected lines
- **Activation Status**: Should reflect current status
- **Forward Endpoint Permission**: Should reflect current setting

### 4. Test Scenarios

#### Scenario 1: Basic Edit
1. Change email address
2. Modify traffic limit
3. Update max ports per server
4. Click "Update"
5. Verify success message
6. Confirm changes in subscription list

#### Scenario 2: Billing Type Change
1. Change from Cycle to One-time billing (or vice versa)
2. Update pricing and days accordingly
3. Save changes
4. Verify billing type updated correctly

#### Scenario 3: Lines Management
1. Add/remove lines from selection
2. Save changes
3. Verify line assignments updated

#### Scenario 4: Validation Testing
1. Clear required fields (email, lines)
2. Enter invalid email format
3. Verify validation errors appear
4. Confirm form cannot be submitted with invalid data

#### Scenario 5: Error Handling
1. Test with invalid user_id (if possible)
2. Test network error scenarios
3. Verify appropriate error messages

## Data Integrity Verification

### Immutable Fields (Should NOT be editable)
- User ID
- Token ID
- Created timestamp
- Updated timestamp (should auto-update)

### Editable Fields (Should be modifiable)
- Email address
- Bandwidth limit
- Traffic limit
- Activation status
- Max ports per server
- Billing type and pricing
- Line assignments
- Forward endpoint permissions
- Valid until date (recalculated based on total_days)

## Integration Testing

### Complete Flow Test
1. **Create a test user** via add user functionality
2. **Edit the user** using the new edit feature
3. **Verify changes** in the subscription list
4. **Test user functionality** (if applicable) to ensure edit didn't break anything
5. **Check database** to confirm data persistence

### API Integration
1. Frontend form submission should call `PUT /edit_user`
2. Backend should validate and update database
3. Response should trigger appropriate UI feedback
4. Subscription list should refresh with updated data

## Performance Considerations

- Edit dialog should load quickly with pre-populated data
- Form submission should provide loading indicators
- Large line lists should be handled efficiently
- Database updates should be atomic

## Security Testing

- Verify admin authentication is required
- Test that users cannot edit other users' data
- Ensure input validation prevents injection attacks
- Confirm immutable fields cannot be modified via API

## Browser Compatibility

Test the edit functionality across:
- Chrome/Chromium
- Firefox
- Safari
- Edge

## Success Criteria

✅ Edit button appears in subscription management table
✅ Edit dialog opens with pre-populated data
✅ Form validation works correctly
✅ Data updates successfully in backend
✅ UI provides appropriate feedback
✅ Changes persist after page refresh
✅ No regression in existing functionality
✅ All tests pass
