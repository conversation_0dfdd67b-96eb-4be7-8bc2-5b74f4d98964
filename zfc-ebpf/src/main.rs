#![no_std]
#![no_main]

use aya_ebpf::{
    bindings::{TC_ACT_PIPE, TC_ACT_SHOT},
    macros::{classifier, map},
    maps::HashMap,
    programs::TcContext,
};
use aya_log_ebpf::debug;
use core::{mem, ops::Add};
use network_types::{
    eth::{EthHdr, EtherType},
    ip::{IpProto, Ipv4Hdr, Ipv6Hdr},
    tcp::TcpHdr,
    udp::UdpHdr,
};

#[map]
static PORT_TO_FLOW_MAP: HashMap<u16, u32> = HashMap::with_max_entries(32640, 0);
#[map]
static LOCAL_ADDR: HashMap<u32, u8> = HashMap::with_max_entries(254, 0);
#[map]
static LOCAL_ADDR_V6: HashMap<[u8; 16], u8> = HashMap::with_max_entries(254, 0);

#[inline(always)]
fn ptr_at<T>(ctx: &TcContext, offset: usize) -> Result<*const T, ()> {
    let start = ctx.data();
    let end = ctx.data_end();
    let len = mem::size_of::<T>();

    if start + offset + len > end {
        return Err(());
    }

    Ok((start + offset) as *const T)
}

#[classifier]
pub fn zfc(ctx: TcContext) -> i32 {
    match try_zfc(ctx) {
        Ok(ret) => ret,
        Err(()) => TC_ACT_PIPE,
    }
}

fn try_zfc(mut ctx: TcContext) -> Result<i32, ()> {
    let ethhdr: *const EthHdr = ptr_at(&ctx, 0)?;
    match unsafe { (*ethhdr).ether_type } {
        EtherType::Ipv4 => {
            let ipv4hdr: *const Ipv4Hdr = ptr_at(&ctx, EthHdr::LEN)?;

            let source_addr = u32::from_be(unsafe { (*ipv4hdr).src_addr });
            let dest_addr = u32::from_be(unsafe { (*ipv4hdr).dst_addr });

            let (source_port, dest_port) = match unsafe { (*ipv4hdr).proto } {
                IpProto::Tcp => {
                    let tcphdr: *const TcpHdr = ptr_at(&ctx, EthHdr::LEN + Ipv4Hdr::LEN)?;
                    (
                        u16::from_be(unsafe { (*tcphdr).source }),
                        u16::from_be(unsafe { (*tcphdr).dest }),
                    )
                }
                IpProto::Udp => {
                    let udphdr: *const UdpHdr = ptr_at(&ctx, EthHdr::LEN + Ipv4Hdr::LEN)?;
                    (
                        u16::from_be(unsafe { (*udphdr).source }),
                        u16::from_be(unsafe { (*udphdr).dest }),
                    )
                }
                _ => return Ok(TC_ACT_PIPE),
            };
            if let Some(subscr_id) = unsafe { PORT_TO_FLOW_MAP.get(&dest_port) } {
                if unsafe { LOCAL_ADDR.get(&dest_addr).is_some() } {
                    ctx.set_mark(*subscr_id);
                    // let _ = traffic_incr(subscr_id, ctx.len().try_into().unwrap());
                }
            } else if let Some(subscr_id) = unsafe { PORT_TO_FLOW_MAP.get(&source_port) } {
                if unsafe { LOCAL_ADDR.get(&source_addr).is_some() } {
                    ctx.set_mark(*subscr_id);
                    // let _ = traffic_incr(subscr_id, ctx.len().try_into().unwrap());
                }
            }
        }
        EtherType::Ipv6 => {
            let ipv6hdr: *const Ipv6Hdr = ptr_at(&ctx, EthHdr::LEN)?;
            let source_addr = unsafe { (*ipv6hdr).src_addr.in6_u.u6_addr8 };
            let dest_addr = unsafe { (*ipv6hdr).dst_addr.in6_u.u6_addr8 };

            let (source_port, dest_port) = match unsafe { (*ipv6hdr).next_hdr } {
                IpProto::Tcp => {
                    let tcphdr: *const TcpHdr = ptr_at(&ctx, EthHdr::LEN + Ipv6Hdr::LEN)?;
                    (
                        u16::from_be(unsafe { (*tcphdr).source }),
                        u16::from_be(unsafe { (*tcphdr).dest }),
                    )
                }
                IpProto::Udp => {
                    let udphdr: *const UdpHdr = ptr_at(&ctx, EthHdr::LEN + Ipv6Hdr::LEN)?;
                    (
                        u16::from_be(unsafe { (*udphdr).source }),
                        u16::from_be(unsafe { (*udphdr).dest }),
                    )
                }
                _ => return Ok(TC_ACT_PIPE),
            };

            // if let Some(subscr_id) = unsafe { PORT_TO_FLOW_MAP.get(&dest_port) } {
            //     if unsafe { LOCAL_ADDR_V6.get(&dest_addr).is_some() } {
            //         ctx.set_mark(*subscr_id);
            //         // let _ = traffic_incr(subscr_id, ctx.len().try_into().unwrap());
            //     }
            // } else if let Some(subscr_id) = unsafe { PORT_TO_FLOW_MAP.get(&source_port) } {
            //     if unsafe { LOCAL_ADDR_V6.get(&source_addr).is_some() } {
            //         ctx.set_mark(*subscr_id);
            //         // let _ = traffic_incr(subscr_id, ctx.len().try_into().unwrap());
            //     }
            // }
        }
        _ => {}
    }
    Ok(TC_ACT_PIPE)
}


#[panic_handler]
fn panic(_info: &core::panic::PanicInfo) -> ! {
    unsafe { core::hint::unreachable_unchecked() }
}
