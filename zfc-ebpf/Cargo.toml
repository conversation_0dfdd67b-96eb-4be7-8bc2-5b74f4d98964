[package]
name = "zfc-ebpf"
version = "0.1.0"
edition = "2021"

[dependencies]
aya-ebpf = { git = "https://github.com/zfc-core/aya-rs" }
aya-log-ebpf = { git = "https://github.com/zfc-core/aya-rs" }
network-types = "0.0.4"

[[bin]]
name = "zfc"
path = "src/main.rs"

[profile.dev]
opt-level = 3
debug = false
debug-assertions = false
overflow-checks = false
lto = true
panic = "abort"
incremental = false
codegen-units = 1
rpath = false

[profile.release]
lto = true
panic = "abort"
codegen-units = 1

[workspace]
members = []
