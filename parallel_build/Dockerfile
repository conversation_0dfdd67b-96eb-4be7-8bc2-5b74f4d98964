FROM rust:latest

# 安装 wget、unzip 和 jq（用于解析 cargo metadata 的 JSON 输出）
RUN apt-get update && apt-get install -y wget unzip jq

# 下载并安装 Zig（此处示例使用 0.11.0 版本，请根据需要修改）
RUN wget https://ziglang.org/download/0.13.0/zig-linux-x86_64-0.13.0.tar.xz && \
    tar -xf zig-linux-x86_64-0.13.0.tar.xz && \
    mv zig-linux-x86_64-0.13.0 /opt/zig && \
    ln -s /opt/zig/zig /usr/local/bin/zig && \
    rm zig-linux-x86_64-0.13.0.tar.xz

# 安装 cargo-zigbuild，用于使用 Zig 进行 Rust 构建
RUN cargo install cargo-zigbuild
RUN cargo install bpf-linker
RUN rustup install nightly
RUN rustup target add x86_64-unknown-linux-musl --toolchain nightly

# 设置工作目录，并复制项目文件
WORKDIR /app
COPY . /app

# 确保构建脚本具有执行权限
RUN chmod +x build.sh

CMD ["/bin/bash", "build.sh"]