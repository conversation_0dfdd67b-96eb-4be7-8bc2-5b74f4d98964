#!/bin/bash
set -e

echo "Fetching binary targets..."

# 定义要跳过的二进制目标名称列表，以空格分隔
SKIP_BINS="xtask zfc-teloxide zfc-admin zfc-util prisma-cli"

# 将跳过列表转换为正则表达式，例如 "xtask|skip_bin"
SKIP_PATTERN=$(echo "$SKIP_BINS" | sed 's/ /|/g')

# 遍历所有 workspace 内的包，收集所有 "bin" 类型的目标，然后过滤掉跳过的目标
bins=$(cargo metadata --format-version 1 --no-deps | \
  jq -r '.packages[] 
         | .targets[]
         | select(.kind | index("bin"))
         | .name' | grep -v -E "$SKIP_PATTERN" | sort | uniq)

if [ -z "$bins" ]; then
  echo "No binary targets found."
  exit 1
fi

echo "Found binaries: $bins"
cargo prisma generate schema prisma/schema.prisma
# 为每个二进制目标分配独立的 target 目录进行并行构建
cargo xtask build-ebpf --release
for bin in $bins; do
  echo "Building binary: $bin"
  cargo zigbuild --bin "$bin" --release --target-dir "target/$bin"  --target x86_64-unknown-linux-musl &
done

# 等待所有后台构建完成
wait

echo "All binaries have been built."