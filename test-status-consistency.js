/**
 * Test script to verify status consistency across all pages
 * This test ensures that Subscription Dashboard, Subscription Management, 
 * and Server Management pages all show consistent online/offline status
 */

const { chromium } = require('playwright');

const DEV_TOKEN = '945d1c4f-406b-41f1-9ece-e15ddfb048f0';
const BASE_URL = 'http://localhost:5173';

async function login(page) {
  console.log('🔐 Logging in...');
  await page.goto(`${BASE_URL}/login`);
  await page.fill('input[placeholder="Enter your token"]', DEV_TOKEN);
  await page.click('button:has-text("Login")');
  await page.waitForURL(`${BASE_URL}/subscription`);
  console.log('✅ Login successful');
}

async function getServerStatusFromSubscriptionDashboard(page) {
  console.log('📊 Getting server status from Subscription Dashboard...');
  await page.goto(`${BASE_URL}/subscription`);
  await page.waitForSelector('.server-status-card', { timeout: 10000 });
  
  // Wait for data to load
  await page.waitForTimeout(2000);
  
  const serverStatuses = await page.evaluate(() => {
    const cards = document.querySelectorAll('.server-status-card');
    const statuses = {};
    
    cards.forEach(card => {
      const nameElement = card.querySelector('.server-name');
      const isOffline = card.classList.contains('offline');
      
      if (nameElement) {
        const serverName = nameElement.textContent.trim();
        statuses[serverName] = !isOffline; // true if online, false if offline
      }
    });
    
    return statuses;
  });
  
  console.log('📊 Subscription Dashboard statuses:', serverStatuses);
  return serverStatuses;
}

async function getServerStatusFromSubscriptionManagement(page) {
  console.log('📋 Getting server status from Subscription Management...');
  await page.goto(`${BASE_URL}/subscription-management`);
  await page.waitForSelector('.el-table', { timeout: 10000 });
  
  // Wait for data to load
  await page.waitForTimeout(2000);
  
  const serverStatuses = {};
  
  // Click on line details popovers to get server statuses
  const lineCountElements = await page.locator('.line-count').all();
  
  for (let i = 0; i < lineCountElements.length; i++) {
    try {
      // Hover over the line count to show popover
      await lineCountElements[i].hover();
      await page.waitForTimeout(500);
      
      // Get server statuses from the popover
      const popoverStatuses = await page.evaluate(() => {
        const popover = document.querySelector('.line-popover .line-list');
        if (!popover) return {};
        
        const lineItems = popover.querySelectorAll('.line-item');
        const statuses = {};
        
        lineItems.forEach(item => {
          const nameElement = item.querySelector('.line-name');
          const tagElement = item.querySelector('.el-tag');
          
          if (nameElement && tagElement) {
            const serverName = nameElement.textContent.trim();
            const statusText = tagElement.textContent.trim();
            statuses[serverName] = statusText === 'Active';
          }
        });
        
        return statuses;
      });
      
      Object.assign(serverStatuses, popoverStatuses);
      
      // Move mouse away to hide popover
      await page.mouse.move(0, 0);
      await page.waitForTimeout(300);
    } catch (error) {
      console.warn(`⚠️ Could not get status from line ${i + 1}:`, error.message);
    }
  }
  
  console.log('📋 Subscription Management statuses:', serverStatuses);
  return serverStatuses;
}

async function getServerStatusFromServerManagement(page) {
  console.log('🖥️ Getting server status from Server Management...');
  await page.goto(`${BASE_URL}/server-management`);
  await page.waitForSelector('.el-table', { timeout: 10000 });
  
  // Wait for data to load
  await page.waitForTimeout(2000);
  
  const serverStatuses = await page.evaluate(() => {
    const rows = document.querySelectorAll('.el-table__body tr');
    const statuses = {};
    
    rows.forEach(row => {
      const cells = row.querySelectorAll('td');
      if (cells.length >= 2) {
        // Assuming server name is in the second column (index 1) and status in the appropriate column
        const nameCell = cells[1]; // Adjust index based on actual table structure
        const statusCell = row.querySelector('td .el-tag'); // Find status tag
        
        if (nameCell && statusCell) {
          const serverName = nameCell.textContent.trim();
          const statusText = statusCell.textContent.trim();
          statuses[serverName] = statusText === 'Online';
        }
      }
    });
    
    return statuses;
  });
  
  console.log('🖥️ Server Management statuses:', serverStatuses);
  return serverStatuses;
}

function compareStatuses(dashboardStatuses, managementStatuses, serverStatuses) {
  console.log('\n🔍 Comparing server statuses across all pages...\n');
  
  // Get all unique server names
  const allServerNames = new Set([
    ...Object.keys(dashboardStatuses),
    ...Object.keys(managementStatuses),
    ...Object.keys(serverStatuses)
  ]);
  
  let inconsistencies = 0;
  let totalServers = 0;
  
  allServerNames.forEach(serverName => {
    totalServers++;
    const dashboardStatus = dashboardStatuses[serverName];
    const managementStatus = managementStatuses[serverName];
    const serverMgmtStatus = serverStatuses[serverName];
    
    const statusString = `Dashboard: ${dashboardStatus !== undefined ? (dashboardStatus ? 'Online' : 'Offline') : 'N/A'}, ` +
                        `Management: ${managementStatus !== undefined ? (managementStatus ? 'Active' : 'Inactive') : 'N/A'}, ` +
                        `Server Mgmt: ${serverMgmtStatus !== undefined ? (serverMgmtStatus ? 'Online' : 'Offline') : 'N/A'}`;
    
    // Check for inconsistencies
    const statuses = [dashboardStatus, managementStatus, serverMgmtStatus].filter(s => s !== undefined);
    const isConsistent = statuses.length === 0 || statuses.every(s => s === statuses[0]);
    
    if (isConsistent) {
      console.log(`✅ ${serverName}: ${statusString}`);
    } else {
      console.log(`❌ ${serverName}: ${statusString} - INCONSISTENT!`);
      inconsistencies++;
    }
  });
  
  console.log(`\n📊 Summary: ${totalServers - inconsistencies}/${totalServers} servers have consistent status`);
  
  if (inconsistencies === 0) {
    console.log('🎉 All servers show consistent status across all pages!');
    return true;
  } else {
    console.log(`⚠️ Found ${inconsistencies} inconsistencies that need to be addressed.`);
    return false;
  }
}

async function runStatusConsistencyTest() {
  console.log('🚀 Starting status consistency test...\n');
  
  const browser = await chromium.launch({ headless: false });
  const context = await browser.newContext();
  const page = await context.newPage();
  
  try {
    // Login first
    await login(page);
    
    // Get statuses from all three pages
    const dashboardStatuses = await getServerStatusFromSubscriptionDashboard(page);
    const managementStatuses = await getServerStatusFromSubscriptionManagement(page);
    const serverStatuses = await getServerStatusFromServerManagement(page);
    
    // Compare and report results
    const isConsistent = compareStatuses(dashboardStatuses, managementStatuses, serverStatuses);
    
    if (isConsistent) {
      console.log('\n✅ Status consistency test PASSED!');
      process.exit(0);
    } else {
      console.log('\n❌ Status consistency test FAILED!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Test failed with error:', error);
    process.exit(1);
  } finally {
    await browser.close();
  }
}

// Run the test
runStatusConsistencyTest();
