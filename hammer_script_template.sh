#!/bin/bash
HOST={HOST}

# generate a random
RAND_NUM=$(date +%s)

dir=/opt/fwd_server_${RAND_NUM}

mkdir -p ${dir}

curl -o ${dir}/server $HOST/server

chmod +x ${dir}/server

cat <<EOF > ${dir}/config_${RAND_NUM}.json
{CONFIG_CONTENT}
EOF

cat <<EOF > /etc/systemd/system/fwd_server_${RAND_NUM}.service
[Unit]
Description=fwd_server_${RAND_NUM}
[Service]
User=root
WorkingDirectory=${dir}
ExecStart=${dir}/server -s ${dir}/config_${RAND_NUM}.json
Restart=always
LimitNOFILE=524288
[Install]
WantedBy=multi-user.target
EOF
systemctl daemon-reload
systemctl enable fwd_server_${RAND_NUM}
systemctl start fwd_server_${RAND_NUM}

if systemctl is-active --quiet fwd_server_${RAND_NUM}
then
  echo "Success"
else
  echo "Failed to start fwd_server_${RAND_NUM}. Check the system logs for more details."
fi