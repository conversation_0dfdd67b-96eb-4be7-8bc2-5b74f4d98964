# Manual Test Plan: Server Management Interface Name Fix

## Overview
This test plan verifies that the interface_name field is properly handled in the modify server functionality.

## Test Environment Setup
1. Start the backend server (`zf-web`)
2. Start the frontend server (`zfc-web-ui`)
3. Login with admin credentials
4. Navigate to Server Management page

## Test Cases

### Test Case 1: Verify Interface Name Field in Edit Form
**Objective**: Confirm that the interface_name field is present and editable in the modify server form

**Steps**:
1. Navigate to Server Management page
2. Click "Edit" button on any server
3. Verify that "Interface Name" field is visible
4. Verify that the field shows current interface_name value (may be empty)
5. Verify that the field accepts text input

**Expected Result**: Interface Name field is present, editable, and shows current value

### Test Case 2: Verify Interface Name Validation
**Objective**: Confirm that interface_name is validated as required

**Steps**:
1. Open edit server dialog
2. Clear the Interface Name field (leave it empty)
3. Try to submit the form
4. Verify validation error appears

**Expected Result**: Form validation prevents submission with error "Please enter interface name"

### Test Case 3: Verify Interface Name is Included in API Request
**Objective**: Confirm that interface_name is sent to the backend in modify requests

**Steps**:
1. Open browser developer tools (Network tab)
2. Open edit server dialog
3. Fill in Interface Name field with "eth0-test"
4. Submit the form
5. Check the `/mod_server` POST request in Network tab
6. Examine the request payload

**Expected Result**: Request payload includes `"interface_name": "eth0-test"`

### Test Case 4: Verify Backend Processes Interface Name
**Objective**: Confirm that the backend updates the interface_name in the database

**Steps**:
1. Edit a server and change interface_name to "eth1-test"
2. Submit the form
3. Verify success message appears
4. Refresh the page or reload server list
5. Edit the same server again
6. Verify that interface_name field shows "eth1-test"

**Expected Result**: Interface name is persisted and displayed correctly after modification

### Test Case 5: Verify All Fields Are Included
**Objective**: Ensure no other fields were broken by the interface_name fix

**Steps**:
1. Open edit server dialog
2. Modify multiple fields:
   - Display Name: "Test Server Updated"
   - IP Address: "*************"
   - Interface Name: "eth2"
   - Port Start: 31000
   - Port End: 32000
   - Traffic Scale: 1.5
   - Toggle some checkboxes (Allow Forward, Allow IPv6, etc.)
3. Submit the form
4. Verify all changes are saved correctly

**Expected Result**: All modified fields are properly updated and persisted

## Backend Verification

### Check Request Structure
Use curl or Postman to test the API directly:

```bash
curl -X POST http://localhost:8080/mod_server \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "server_id": 1,
    "display_name": "Test Server",
    "ip_addr": "*************",
    "interface_name": "eth0",
    "port_start": 30000,
    "port_end": 31000,
    "traffic_scale": 1.0,
    "allow_forward": false,
    "allow_latency_test": true,
    "allow_ipv6": false
  }'
```

**Expected Result**: Request succeeds (200 OK) and interface_name is updated

### Check Database
If you have database access, verify the interface_name field in the outbound_endpoint table:

```sql
SELECT id, display_name, ingress_ipv_4, interface_name 
FROM OutboundEndpoint 
WHERE id = 1;
```

**Expected Result**: interface_name column shows the updated value

## Regression Testing

### Verify Add Server Still Works
1. Navigate to Server Management
2. Click "Add Server"
3. Fill all required fields including interface_name
4. Submit the form
5. Verify server is created successfully

**Expected Result**: Add server functionality remains unaffected

### Verify Other Management Pages
1. Test Port Management modify functionality
2. Test Forward Endpoint Management modify functionality
3. Verify no unintended side effects

**Expected Result**: Other management pages work normally

## Success Criteria
- ✅ Interface name field is visible and editable in modify server form
- ✅ Interface name validation works correctly (required field)
- ✅ Interface name is included in modify server API requests
- ✅ Backend processes and persists interface name updates
- ✅ All other form fields continue to work correctly
- ✅ No regression in add server functionality
- ✅ No impact on other management pages

## Troubleshooting

### If interface_name is not in API request:
- Check browser console for JavaScript errors
- Verify the handleEditSubmit function includes interface_name logic
- Check if form validation is preventing submission

### If backend returns error:
- Check backend logs for detailed error messages
- Verify ModifyServerRequest struct includes interface_name field
- Verify handle_modify_server function processes interface_name

### If changes are not persisted:
- Check database schema for interface_name column
- Verify database update query includes interface_name
- Check for database constraint violations
