[package]
name = "zf-auth-client"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = { version = "4.1", features = ["derive", "env"] }
anyhow = "1"
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0.4"
tokio = { version = "1.25", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time", "process", "sync"] }
futures-util = "0.3.28"
reqwest = { version = "0.12", default-features=false, features=["json","rustls-tls"]}
ring = "0"
x509-parser = "0.14"
hex = "0.4"