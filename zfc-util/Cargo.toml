[package]
name = "zfc-util"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = { version = "4.1", features = ["derive", "env"] }
common = { path = "../common" }
anyhow = "1"
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0.4"
tokio = { workspace = true}
futures-util = "0.3.28"
hex = "0.4.3"
