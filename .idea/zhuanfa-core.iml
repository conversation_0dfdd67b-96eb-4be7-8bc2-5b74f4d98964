<?xml version="1.0" encoding="UTF-8"?>
<module type="EMPTY_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/vendor/tobaru/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/xtask/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/zfc-arranger/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/zfc-common/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/zfc-std-common/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/zfc-worker/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/prisma-cli/src" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/zfc-util/src" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>