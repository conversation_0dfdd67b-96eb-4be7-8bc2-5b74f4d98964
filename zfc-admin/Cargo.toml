[package]
name = "zfc-admin"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
anyhow = { version = "1.0.75", features = ["backtrace"] }
dotenv = "0.15.0"
log = "0.4.22"
env_logger = "0.11"
serde = "1.0.188"
serde_json = "1.0.107"
clap = { version = "4.1", features = ["derive", "env"] }
tokio = { workspace = true}
prisma-client-rust = { git = "https://github.com/<PERSON><PERSON><PERSON><PERSON>/prisma-client-rust", tag = "0.6.11", no-default-features = true, features = ["postgresql"] }
sql-query-connector = { git = "https://github.com/<PERSON><PERSON><PERSON>ovich/prisma-engines", tag = "pcr-0.6.10", features = ["vendored-openssl"] }
common = { path = "../common" }
uuid = { version = "1.8.0", features = ["v4", "fast-rng"] }
chrono = "0.4.31"
byte-unit = {version = "5", features = ["byte"]}
bb8-redis = "0.13.1"
redis = { version = "0.23.3", features = ["tokio-comp", "tls", "serde_json", "serde", "tokio-native-tls-comp", "json"] }
