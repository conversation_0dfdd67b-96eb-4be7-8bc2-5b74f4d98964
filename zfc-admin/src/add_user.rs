use std::sync::Arc;

use anyhow::anyhow;
// use chrono::{Days, Utc};

use crate::prisma;
#[derive(Debug, serde::Serialize, serde::Deserialize)]
struct UserInfo {
    address: String,
    tg_user: Option<String>,
    tg_chat_id: Option<String>,
    bandwidth: Option<u32>, //mbps
    traffic: u64,           // Bytes
    activated: bool,
    // reset_days: Option<usize>,
    ports: Vec<Port>,
    max_ports_per_server: u32,
    bill_type: BillingType,
    total_dyas: u32,
    lines: Vec<i32>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
enum BillingType {
    Cycle { days: usize, price: usize },
    OneTime { price: usize, days: usize },
}

impl BillingType {
    fn to_int(&self) -> u32 {
        match self {
            BillingType::Cycle { days, price } => 0,
            BillingType::OneTime { price, .. } => 1,
        }
    }
    fn price(&self) -> usize {
        match self {
            BillingType::Cycle { days, price } => *price,
            BillingType::OneTime { price, .. } => *price,
        }
    }
    fn cycle_days(&self) -> Option<usize> {
        match self {
            BillingType::Cycle { days, price } => Some(*days),
            BillingType::OneTime { price, days } => None,
        }
    }
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
struct Port {
    target_address: String,
    target_port: u16,
    port: Option<u16>,
    display_name: Option<String>,
    traffic_in: Option<u128>,
    traffic_out: Option<u128>,
}

#[derive(Debug, serde::Serialize, serde::Deserialize)]
struct UserJsonStruct {
    users: Vec<UserInfo>,
}
pub async fn add_user_proc(file_path: &str, db: &Arc<prisma::PrismaClient>) -> anyhow::Result<()> {
    let file = tokio::fs::read_to_string(file_path).await?;
    let users: UserJsonStruct = serde_json::from_str(&file)?;
    // let mut all_paras = vec![];
    for u in users.users.iter() {
        let token_id = uuid::Uuid::new_v4().to_string();
        let owner_address = u.address.clone();
        let billing_type = u.bill_type.to_int();
        let base_price = u.bill_type.price();
        let recurring_price = u.bill_type.price();
        let bandwidth = u.bandwidth;
        let traffic = u.traffic;
        let activated = u.activated;
        let params = vec![
            prisma::subscription::tg_user::set(u.tg_user.clone()),
            prisma::subscription::tg_chat_id::set(u.tg_chat_id.clone()),
            prisma::subscription::reset_days::set(u.bill_type.cycle_days().map(|x| x as i32)),
            prisma::subscription::last_reset::set(Some(
                prisma_client_rust::chrono::Utc::now().fixed_offset(),
            )),
            prisma::subscription::bandwidth::set(bandwidth.map(|x| x as i32)),
            prisma::subscription::lines::set(u.lines.iter().map(Clone::clone).collect()),
            prisma::subscription::is_admin::set(true),
            prisma::subscription::allow_forward_endpoint::set(true),
        ];
        let valid_until = prisma_client_rust::chrono::Utc::now()
            .checked_add_days(prisma_client_rust::chrono::Days::new(u.total_dyas as u64))
            .ok_or(anyhow!("can't add days"))?
            .fixed_offset();
        println!("add user: {} token: {}", u.address, token_id);
        // all_paras.push((
        // token_id,
        // owner_address,
        // billing_type as i32,
        // base_price as i32,
        // recurring_price as i32,
        // traffic as i64,
        // valid_until,
        // activated,
        // params,
        // ));
        db.subscription()
            .create(
                token_id,
                owner_address,
                billing_type as i32,
                base_price as i32,
                recurring_price as i32,
                traffic as i64,
                valid_until,
                activated,
                u.max_ports_per_server as i32,
                params,
            )
            .exec()
            .await?;
    }
    // db.subscription().create_many(all_paras).exec().await?;
    Ok(())
}
