use futures::future::BoxFuture;
use std::{collections::HashMap, hash::Hash};
use tokio::sync::RwLock;

pub struct Wrapper<V> {
    data: V,
    update_time: std::time::Instant,
}
unsafe impl<K: Send, V: Send> Send for Cache<K, V> {}
unsafe impl<K: Send, V: Send> Sync for Cache<K, V> {}
pub struct Cache<K, V> {
    data: RwLock<HashMap<K, Wrapper<V>>>,
    updator: Box<dyn Fn(&K) -> BoxFuture<'static, Result<V, anyhow::Error>>>,
    ttl: std::time::Duration,
}

impl<K: Eq + Hash + Clone, V: Clone> Cache<K, V> {
    pub fn new(
        updator: Box<dyn Fn(&K) -> BoxFuture<'static, Result<V, anyhow::Error>>>,
        ttl: std::time::Duration,
    ) -> Self {
        Self {
            data: RwLock::new(HashMap::new()),
            updator,
            ttl,
        }
    }

    pub async fn get(&self, key: &K) -> Result<V, anyhow::Error> {
        // get from cache if not expired
        // if expired, update and return
        // if not exist, update and return
        let data = match self.data.read().await.get(key) {
            Some(wrapper) => {
                if wrapper.update_time.elapsed() > self.ttl {
                    None
                } else {
                    Some(wrapper.data.clone())
                }
            }
            None => None,
        };
        if let Some(data) = data {
            return Ok(data);
        }
        let data = (self.updator)(&key).await?;
        self.data.write().await.insert(
            key.clone(),
            Wrapper {
                data: data.clone(),
                update_time: std::time::Instant::now(),
            },
        );
        Ok(data)
    }
}
