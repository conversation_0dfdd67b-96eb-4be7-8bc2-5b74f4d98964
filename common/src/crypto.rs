use anyhow::Result;
pub use chacha20poly1305;
pub use k256;
use k256::ecdh::SharedSecret;
pub use k256::{<PERSON>K<PERSON>, <PERSON>Key};
use rand_core::OsRng;

pub fn random_key() -> SecretKey {
    SecretKey::random(&mut OsRng)
}

pub fn stringify_secret_key(key: &SecretKey) -> String {
    let scalar = key.as_scalar_primitive();
    scalar.to_string().to_lowercase()
}

pub fn stringify_public_key_from_secret_key(key: &SecretKey) -> String {
    let pubkey = key.public_key();
    let pubkey = pubkey.to_sec1_bytes();
    hex::encode(&pubkey)
}

pub fn stringify_public_key(key: &PublicKey) -> String {
    let pubkey = key.to_sec1_bytes();
    hex::encode(&pubkey)
}

pub fn parse_secret_key<T: Into<String>>(key_str: T) -> Result<SecretKey> {
    let bytes = hex::decode(key_str.into())?;
    let bytes = bytes.as_slice();
    Ok(SecretKey::from_slice(bytes)?)
}

pub fn parse_public_key<T: Into<String>>(key_str: T) -> Result<PublicKey> {
    let bytes = hex::decode(key_str.into())?;
    let bytes = bytes.as_slice();
    Ok(PublicKey::from_sec1_bytes(bytes)?)
}

pub fn clone_shared_secret(k: &SharedSecret) -> SharedSecret {
    let k = k.raw_secret_bytes().clone();
    SharedSecret::from(k)
}
