use std::sync::Arc;

use bb8::Pool;
use bb8_redis::RedisConnectionManager;
use redis::{AsyncCommands, SetOptions};
use uuid::Uuid;

/// 分布式锁结构体
pub struct DistributedLock {
    pool: Pool<RedisConnectionManager>,
    key: String,
    value: String,     // 唯一标识符
    expiration: usize, // 过期时间（毫秒）
}

impl DistributedLock {
    /// 创建一个新的锁实例
    pub fn new(pool: Pool<RedisConnectionManager>, key: &str, expiration: usize) -> Self {
        DistributedLock {
            pool,
            key: key.to_string(),
            value: Uuid::new_v4().to_string(),
            expiration,
        }
    }

    /// 尝试获取锁
    pub async fn acquire(self: Arc<Self>) -> anyhow::Result<RedisLockGuard> {
        // 从连接池中获取一个连接
        let mut conn = self.pool.get().await?;

        // 创建 SetOptions，使用 NX 和 PX 选项
        let opts = SetOptions::default()
            .conditional_set(redis::ExistenceCheck::NX)
            .with_expiration(redis::SetExpiry::PX(self.expiration));

        // 执行 SET 命令
        let result: Option<String> = conn.set_options(&self.key, &self.value, opts).await?;

        if result.is_some() {
            Ok(RedisLockGuard {
                lock: self.clone(),
            })
        } else {
            Err(anyhow::anyhow!("Failed to acquire lock"))
        }
    }

    /// 释放锁（使用 Lua 脚本确保原子性）
    pub async fn release(&self) -> anyhow::Result<()> {
        // Lua 脚本确保释放锁的原子性
        let lua_script = r#"
            if redis.call("GET", KEYS[1]) == ARGV[1] then
                return redis.call("DEL", KEYS[1])
            else
                return 0
            end
        "#;

        // 从连接池中获取一个连接
        let mut conn = self.pool.get().await?;

        // 执行 Lua 脚本
        let _: i32 = redis::cmd("EVAL")
            .arg(lua_script)
            .arg(1)
            .arg(&self.key)
            .arg(&self.value)
            .query_async(&mut *conn)
            .await?;

        Ok(())
    }
}

pub struct RedisLockGuard {
    lock: Arc<DistributedLock>,
}

impl Drop for RedisLockGuard {
    fn drop(&mut self) {
        let lock = self.lock.clone();
        tokio::spawn(async move {
            let _ = lock.release().await;
        });
    }
}
