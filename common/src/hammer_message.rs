use private_tun::{address::Address, snell_impl_ver::server_plugin::MessageCodec};
use tokio::io::{AsyncRead, AsyncReadExt, AsyncWrite, AsyncWriteExt};

use crate::app_message::{LatencyTestMethod, Mode};
pub const SETUP_TOT_SERVER_REQ: u8 = 0x11;
pub const SETUP_TOT_SERVER_RST: u8 = 0x12;
pub const STOP_TOT_SERVER_REQ: u8 = 0x13;
pub const STOP_TOT_SERVER_RST: u8 = 0x14;

#[derive(Debug)]
pub struct SetupTotServerReq {
    pub remote_list: Vec<Address>,
    pub mode: Mode,
    pub latency_test_method: LatencyTestMethod,
}

#[derive(Debug)]
pub struct SetupTotServerRst {
    pub server_bind: u16,
}


impl MessageCodec for SetupTotServerReq {
    type Message = SetupTotServerReq;
    async fn encode<T: AsyncWrite + Send + Sync + 'static + Unpin>(
        io: &mut T,
        msg: &SetupTotServerReq,
    ) -> anyhow::Result<()> {
        io.write_u8(SETUP_TOT_SERVER_REQ).await?;
        io.write_u8(msg.remote_list.len() as u8).await?;
        for addr in msg.remote_list.iter() {
            Address::encode_into_io(addr, io).await?;
        }
        io.write_u32(msg.mode.into()).await?;
        io.write_u32(msg.latency_test_method.into()).await?;
        io.flush().await?;
        Ok(())
    }
    async fn decode<T: AsyncRead + Send + Sync + 'static + Unpin>(
        io: &mut T,
    ) -> anyhow::Result<Self> {
        let addr_len = io.read_u8().await?;
        let mut remote_list = Vec::with_capacity(addr_len as usize);
        for _ in 0..addr_len {
            let addr = Address::decode_async(io).await?;
            remote_list.push(addr);
        }
        let mode = Mode::try_from(io.read_u32().await?)?;
        let latency_test_method = LatencyTestMethod::try_from(io.read_u32().await?)?;
        Ok(Self {
            remote_list,
            mode,
            latency_test_method,
        })
    }
}


impl MessageCodec for SetupTotServerRst {
    type Message = SetupTotServerRst;
    async fn encode<T: AsyncWrite + Send + Sync + 'static + Unpin>(
        io: &mut T,
        msg: &SetupTotServerRst,
    ) -> anyhow::Result<()> {
        io.write_u8(SETUP_TOT_SERVER_RST).await?;
        io.write_u16(msg.server_bind).await?;
        io.flush().await?;
        Ok(())
    }
    async fn decode<T: AsyncRead + Send + Sync + 'static + Unpin>(
        io: &mut T,
    ) -> anyhow::Result<Self> {
        let server_bind = io.read_u16().await?;
        Ok(Self { server_bind })
    }
}

#[derive(Debug)]
pub struct StopTotServerReq {
    pub remote_list: Vec<Address>,
    pub mode: Mode,
    pub latency_test_method: LatencyTestMethod,
    pub server_bind: u16,
}

#[derive(Debug)]
pub struct StopTotServerRst {
    pub error_code: u8,
}


impl MessageCodec for StopTotServerReq {
    type Message = StopTotServerReq;
    async fn encode<T: AsyncWrite + Send + Sync + 'static + Unpin>(
        io: &mut T,
        msg: &StopTotServerReq,
    ) -> anyhow::Result<()> {
        io.write_u8(STOP_TOT_SERVER_REQ).await?;
        io.write_u8(msg.remote_list.len() as u8).await?;
        for addr in msg.remote_list.iter() {
            Address::encode_into_io(addr, io).await?;
        }
        io.write_u32(msg.mode.into()).await?;
        io.write_u32(msg.latency_test_method.into()).await?;
        io.write_u16(msg.server_bind).await?;
        io.flush().await?;
        Ok(())
    }
    async fn decode<T: AsyncRead + Send + Sync + 'static + Unpin>(
        io: &mut T,
    ) -> anyhow::Result<Self> {
        let addr_len = io.read_u8().await?;
        let mut remote_list = Vec::with_capacity(addr_len as usize);
        for _ in 0..addr_len {
            let addr = Address::decode_async(io).await?;
            remote_list.push(addr);
        }
        let mode = Mode::try_from(io.read_u32().await?)?;
        let latency_test_method = LatencyTestMethod::try_from(io.read_u32().await?)?;
        let server_bind = io.read_u16().await?;
        Ok(Self {
            remote_list,
            mode,
            latency_test_method,
            server_bind,
        })
    }
}


impl MessageCodec for StopTotServerRst {
    type Message = StopTotServerRst;
    async fn encode<T: AsyncWrite + Send + Sync + 'static + Unpin>(
        io: &mut T,
        msg: &StopTotServerRst,
    ) -> anyhow::Result<()> {
        io.write_u8(STOP_TOT_SERVER_RST).await?;
        io.write_u8(msg.error_code).await?;
        io.flush().await?;
        Ok(())
    }
    async fn decode<T: AsyncRead + Send + Sync + 'static + Unpin>(
        io: &mut T,
    ) -> anyhow::Result<Self> {
        let error_code = io.read_u8().await?;
        Ok(Self { error_code })
    }
}
