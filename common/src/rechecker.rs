use std::{future::Future, sync::Arc};

use tokio::sync::Mute<PERSON>;

#[derive(Clone)]
pub struct Rechecker<T> {
    checker: T,
    last_recheck_time: Arc<Mutex<Option<std::time::Instant>>>,
    min_interval: std::time::Duration,
}

impl<T> Rechecker<T> {
    pub fn new(checker: T, min_interval: std::time::Duration) -> Self {
        Self {
            checker,
            last_recheck_time: Arc::new(Mutex::new(None)),
            min_interval,
        }
    }
}

impl<T: Checker> Rechecker<T> {
    /// attention this function may wait a long time
    pub async fn do_recheck(&self) {
        let mut last_recheck_time = self.last_recheck_time.lock().await;
        if last_recheck_time.map_or(true, |t| t.elapsed() > self.min_interval) {
            self.checker.recheck().await;
            *last_recheck_time = Some(std::time::Instant::now());
        }
    }
}


pub trait Checker {
    fn recheck(&self) -> impl Future<Output = ()>;
}

impl Checker for tokio::sync::mpsc::Sender<()> {
    async fn recheck(&self) {
        let _ = self.send(()).await;
    }
}
