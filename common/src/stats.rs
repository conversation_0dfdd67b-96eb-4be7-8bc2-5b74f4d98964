use chrono::{DateTime, Utc};
use influxdb::InfluxDbWriteable;
use serde::Deserialize;
// /// 定义数据写入时使用的结构体，
// /// 通过 #[influxdb(measurement = "netcard_speed")] 指定 measurement 名称，
// /// #[influxdb(tag)] 标记字段表示 InfluxDB Tag，
// /// 如果 time 字段设置为 Some(Timestamp::Now)，则由 InfluxDB 自动设定当前时间。
#[derive(InfluxDbWriteable, Deserialize)]
#[influxdb(measurement = "netcard_speed")]
pub struct NetcardSpeedMeasurement {
    pub time: DateTime<Utc>,
    #[influxdb(tag)]
    pub interface: String,
    #[influxdb(tag)]
    pub agent_id: i32,
    pub tx: f64,       // unit: Byte/s
    pub rx: f64,       // unit: Byte/s
    pub total_tx: u64, // unit: Byte
    pub total_rx: u64, // unit: Byte
}

#[derive(InfluxDbWriteable, Deserialize)]
#[influxdb(measurement = "system_stats")]
pub struct SystemStatsMeasurement {
    pub time: DateTime<Utc>,
    #[influxdb(tag)]
    pub agent_id: i32,
    pub cpu_usage: f32,    // percentage
    pub memory_total: u64, // unit: byte
    pub memory_used: u64,  // unit: byte
    pub uptime: u64,       // unit: second
}
