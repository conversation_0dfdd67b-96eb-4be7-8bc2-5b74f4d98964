use std::sync::{
    atomic::{AtomicBool, Ordering},
    Arc,
};

use parking_lot::RwLock;
use private_tun::weight_selector::WeightList;

use super::ServerInfoProvider;

pub struct AvailableServersList<T: ServerInfoProvider>(
    pub Arc<RwLock<Vec<Arc<T>>>>,
    Arc<AtomicBool>,
);

impl<T: ServerInfoProvider> Clone for AvailableServersList<T> {
    fn clone(&self) -> Self {
        Self(self.0.clone(), self.1.clone())
    }
}

impl<T: ServerInfoProvider> AvailableServersList<T> {
    pub fn new(clients: Vec<Arc<T>>) -> Self {
        Self(
            Arc::new(RwLock::new(clients)),
            Arc::new(AtomicBool::new(true)),
        )
    }
    pub fn set_need_rebuild(&self, value: bool) {
        self.1.store(value, Ordering::Relaxed);
    }
    fn need_update(&self, others: &Vec<Arc<T>>) -> bool {
        if self.0.read().len() != others.len() {
            return true;
        }
        for (i, client) in self.0.read().iter().enumerate() {
            if client.name() != others[i].name() {
                return true;
            }
        }
        false
    }
    pub fn update_clients(&self, others: &Vec<Arc<T>>) {
        if self.need_update(others) {
            let mut lock = self.0.write();
            // log::info!("update clients into from {:?} to {:?}", lock, others);
            *lock = others.clone();
            lock.shrink_to_fit();
            self.set_need_rebuild(true);
        }
    }
}

impl<T: ServerInfoProvider> WeightList for AvailableServersList<T> {
    type Item = Arc<T>;
    fn get_weight_list(&self) -> Vec<u64> {
        let lock = self.0.read();
        log::info!("get_weight_list len: {}", lock.len());
        lock.iter()
            .map(|c| c.select_weight().unwrap_or(1))
            .collect()
    }
    fn need_rebuild(&self) -> bool {
        self.1.load(Ordering::Relaxed)
    }
    fn set_rebuilded(&self) {
        self.set_need_rebuild(false);
    }
    fn get_item(&self, idx: usize) -> Option<Self::Item> {
        let lock = self.0.read();
        if idx >= lock.len() {
            self.set_need_rebuild(true);
            return None;
        }
        Some(lock[idx].clone())
    }
}
