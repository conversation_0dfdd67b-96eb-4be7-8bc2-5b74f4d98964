# Manual Status Consistency Test

This document provides step-by-step instructions to manually verify that server status is now consistent across all pages.

## Prerequisites

1. Start the backend server
2. Start the frontend development server
3. Have access to the dev environment login token: `945d1c4f-406b-41f1-9ece-e15ddfb048f0`

## Test Steps

### 1. Login to the Application

1. Navigate to `http://localhost:5173/login`
2. Enter the dev token: `945d1c4f-406b-41f1-9ece-e15ddfb048f0`
3. Click "Login"
4. Verify you're redirected to the subscription dashboard

### 2. Record Server Status from Subscription Dashboard

1. On the subscription dashboard page (`/subscription`)
2. Wait for server cards to load (should show server status cards)
3. For each server card, note:
   - Server name (displayed at the top of each card)
   - Online status (cards with `offline` class will appear dimmed/different styling)
4. Record the status of each server:

```
Server Status from Subscription Dashboard:
- Server 1: [Online/Offline]
- Server 2: [Online/Offline]
- Server N: [Online/Offline]
```

### 3. Record Server Status from Subscription Management

1. Navigate to Subscription Management page (`/subscription-management`)
2. Wait for the subscription table to load
3. For each subscription row, hover over the "X Lines" tag to show the popover
4. In the popover, note the server names and their status tags (Active/Inactive)
5. Record the status of each server:

```
Server Status from Subscription Management:
- Server 1: [Active/Inactive]
- Server 2: [Active/Inactive]
- Server N: [Active/Inactive]
```

### 4. Record Server Status from Server Management

1. Navigate to Server Management page (`/server-management`)
2. Wait for the server table to load
3. Look at the "Status" column for each server
4. Note the server names and their status (Online/Offline)
5. Record the status of each server:

```
Server Status from Server Management:
- Server 1: [Online/Offline]
- Server 2: [Online/Offline]
- Server N: [Online/Offline]
```

### 5. Compare Results

Compare the status of each server across all three pages:

- **Subscription Dashboard**: Uses `line.is_online` from backend LineInfo
- **Subscription Management**: Uses `line.is_online` from backend LineInfo  
- **Server Management**: Uses `is_online` from backend server list

**Expected Result**: All three pages should show the same status for each server.

- If a server shows "Online" in Server Management, it should show as "Online" (not dimmed) in Subscription Dashboard and "Active" in Subscription Management
- If a server shows "Offline" in Server Management, it should show as "Offline" (dimmed) in Subscription Dashboard and "Inactive" in Subscription Management

### 6. Test Status Updates

To verify that status updates are reflected consistently:

1. Wait for a few minutes and refresh each page
2. Check if status changes are reflected consistently across all pages
3. The status should be based on the backend's Redis `server:status:{id}` keys, not on the presence of performance metrics

## What Was Fixed

### Before the Fix:
- **Subscription Dashboard**: Determined status based on presence of `netcard_speed` data from `/line_stats` endpoint
- **Subscription Management**: Used `is_online` from backend LineInfo (Redis-based)
- **Server Management**: Used `is_online` from backend server list (Redis-based)

This caused inconsistencies because InfluxDB-based status (presence of metrics) could differ from Redis-based status (actual server connectivity).

### After the Fix:
- **All pages now use the same source**: Backend's `is_online` property from LineInfo, which is based on Redis `server:status:{id}` keys
- **Subscription Dashboard**: No longer infers status from `netcard_speed` data presence
- **Status determination is centralized**: All status decisions are made by the backend's authoritative Redis-based tracking

## Troubleshooting

If you still see inconsistencies:

1. **Check browser cache**: Hard refresh (Ctrl+F5) all pages
2. **Verify backend is running**: Ensure the backend server is properly connected to Redis
3. **Check Redis status**: Verify that `server:status:{id}` keys are being set correctly in Redis
4. **Check network**: Ensure there are no network issues affecting API calls

## Success Criteria

✅ **Test passes if**: All three pages show identical status for each server
❌ **Test fails if**: Any server shows different status across the three pages
