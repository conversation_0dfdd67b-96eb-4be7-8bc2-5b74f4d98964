# Subscription Search API Testing Guide

This document provides examples and test cases for the new subscription search functionality.

## API Endpoint

```
GET /api/subscription_list
```

## Search Parameters

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `page` | integer | Page number (default: 1) | `1` |
| `page_size` | integer | Items per page (20, 50, 100, 200) | `20` |
| `id` | integer | Exact subscription ID search | `123` |
| `token_id` | string | Exact token ID search | `abc123def456` |
| `email` | string | Fuzzy email search (case-insensitive) | `<EMAIL>` |
| `valid_until_start` | string | Valid until start date (YYYY-MM-DD) | `2024-01-01` |
| `valid_until_end` | string | Valid until end date (YYYY-MM-DD) | `2024-12-31` |
| `next_reset_start` | string | Next reset start date (YYYY-MM-DD) | `2024-01-01` |
| `next_reset_end` | string | Next reset end date (YYYY-MM-DD) | `2024-12-31` |
| `lines` | array | Line IDs (ANY logic) | `lines=1&lines=2&lines=3` |

## Test Cases

### 1. Basic Pagination
```bash
curl "http://localhost:3030/api/subscription_list?page=1&page_size=20"
```

### 2. Exact ID Search (Highest Priority)
```bash
curl "http://localhost:3030/api/subscription_list?id=123"
```

### 3. Exact Token ID Search
```bash
curl "http://localhost:3030/api/subscription_list?token_id=abc123def456"
```

### 4. Fuzzy Email Search (Case-Insensitive)
```bash
curl "http://localhost:3030/api/subscription_list?email=user@example"
curl "http://localhost:3030/api/subscription_list?email=USER@EXAMPLE"  # Should work the same
```

### 5. Date Range Filtering - Valid Until
```bash
curl "http://localhost:3030/api/subscription_list?valid_until_start=2024-01-01&valid_until_end=2024-12-31"
```

### 6. Date Range Filtering - Next Reset
```bash
curl "http://localhost:3030/api/subscription_list?next_reset_start=2024-01-01&next_reset_end=2024-12-31"
```

### 7. Lines Multi-Select Filtering (ANY Logic)
```bash
curl "http://localhost:3030/api/subscription_list?lines=1&lines=2&lines=3"
```

### 8. Combined Search (AND Logic)
```bash
curl "http://localhost:3030/api/subscription_list?email=user&valid_until_start=2024-01-01&lines=1&lines=2"
```

### 9. Search with Pagination
```bash
curl "http://localhost:3030/api/subscription_list?email=user&page=2&page_size=50"
```

## Expected Response Format

```json
{
  "subscriptions": [
    {
      "id": 123,
      "email_address": "<EMAIL>",
      "token_id": "abc123def456",
      "valid_until": "2024-12-31T23:59:59Z",
      "next_reset": "2024-02-01T00:00:00Z",
      "traffic_used": 1073741824,
      "traffic_total": 107374182400,
      "lines": [
        {
          "id": 1,
          "display_name": "Server 1",
          "ip_addr": "***********",
          "is_online": true,
          "port_start": 30000,
          "port_end": 31000,
          "allow_forward": true
        }
      ],
      "activated": true,
      "allow_forward_endpoint": false,
      "max_ports_per_server": 5
    }
  ],
  "pagination": {
    "current_page": 1,
    "page_size": 20,
    "total_items": 1,
    "total_pages": 1
  }
}
```

## Search Priority and Logic

1. **Exact ID Search**: Highest priority, returns exact matches first
2. **Token ID Search**: Exact string matching
3. **Email Search**: Case-insensitive partial matching
4. **Date Ranges**: Inclusive boundaries
5. **Lines Filtering**: ANY logic (subscription contains any of the selected lines)
6. **Multiple Criteria**: AND logic (all criteria must match)

## Performance Notes

- ID and Token ID searches are optimized with database indexes
- Email searches use case-insensitive indexing
- Lines filtering uses GIN indexes for array operations
- Date range queries use specialized date indexes
- Next reset filtering is post-processed for accuracy

## Error Handling

- Invalid page numbers default to 1
- Invalid page sizes default to 20
- Invalid date formats are ignored
- Invalid line IDs are filtered out
- Empty search parameters return all results with pagination
