[package]
name = "zf-auth-server"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
clap = { version = "4.1", features = ["derive", "env"] }
anyhow = "1"
env_logger = {version = "0", default-features=false, features=["humantime"]}
log = "0.4"
tokio = { version = "1.25", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time", "process", "sync"] }
futures-util = "0.3.28"
