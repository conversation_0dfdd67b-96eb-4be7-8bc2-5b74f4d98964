// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  // Corresponds to the cargo alias created earlier
  provider = "cargo prisma"
  // The location to generate the client. Is relative to the position of the schema
  output   = "../common/src/prisma.generated.rs"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  // directUrl = env("DIRECT_URL")
}

model Subscription {
  id                  Int               @id @default(autoincrement())
  tokenId             String            @unique
  ownerAddress        String
  tgUser              String?
  tgChatId            String?
  billingType         Int
  basePrice           Int
  recurringPrice      Int
  bandwidth           Int?
  traffic             BigInt
  validUntil          DateTime
  resetDays           Int?
  lastReset           DateTime?
  activated           Boolean
  maxPortNumPerServer Int
  maxForwardEndpointNum Int             @default(5)
  lines               Int[]
  allowForwardEndpoint        Boolean           @default(false)
  note                String            @default("")
  ports               Port[]
  allowIpNum          Int?
  allowConnNum        Int?
  createdAt           DateTime          @default(now())
  updatedAt           DateTime          @updatedAt
  ForwardEndpoint     ForwardEndpoint[]
  isAd<PERSON>           @default(false)
  @@index([tokenId])
  @@index([ownerAddress])
  @@index([billingType])
  @@index([tokenId, ownerAddress])
  @@index([bandwidth])
  @@index([validUntil(sort: Desc)])
}

// model OutboundEndpointGroup {
//   id               Int                @id @default(autoincrement())
//   name             String             @unique
//   displayName      String
//   hidden           String
//   OutboundEndpoint OutboundEndpoint[]

//   @@index([name])
//   @@index([hidden])
// }

model OutboundEndpoint {
  id           Int      @id @default(autoincrement())
  name         String   @unique
  pubkey       String   @unique
  displayName  String
  hidden       Boolean  @default(true)
  ingressIpv4  String
  portStart    Int?
  portEnd      Int?
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  ports        Port[]
  trafficScale Float?
  allowForward Boolean? @default(true)
  allowLatencyTest Boolean? @default(false)
  allowIpNum          Int?
  allowConnNum        Int?
  privateKey String?
  interfaceName String?
  version String?
  proxyConfig String?
  relatedForwardEndpointIds Int[]
  allowIpV6      Boolean       @default(false)
  useForwardAsTun Boolean? @default(false)

  @@index([hidden])
  @@index([pubkey])
}

model ForwardEndpoint {
  id             Int           @id @default(autoincrement())
  name           String        @unique
  displayName    String
  ingressAddress String
  servePort      Int
  protocol       String // server protocol socks5 or hammer, current only support hammer
  protocolConfig String // protocol config json
  privKey        String // ws private key
  wsPort         Int? // ws port
  isPublic       Boolean       @default(false) // is public for all users
  subscriptionId Int? // belong to which subscription user
  subscription   Subscription? @relation(fields: [subscriptionId], references: [id])
  setupScript    String?
  tokenId        String        @unique
  createdAt      DateTime      @default(now())
  updatedAt      DateTime      @updatedAt
  trafficScale   Float?
  allowIpV6      Boolean       @default(false)
  @@index([tokenId])
}

model Port {
  id                        Int               @id @default(autoincrement())
  displayName               String
  portV4                    Int
  targetAddressV4           String
  targetPortV4              Int
  traffic_in                BigInt
  traffic_out               BigInt
  createdAt                 DateTime          @default(now())
  updatedAt                 DateTime          @updatedAt
  subscription              Subscription?     @relation(fields: [subscriptionId], references: [id])
  subscriptionId            Int?
  outboundEndpoint          OutboundEndpoint? @relation(fields: [outboundEndpointId], references: [id])
  outboundEndpointId        Int?
  forwardProtocol           String?
  forwardConfig             String?
  relatedForwardEndpointIds Int[]
  targetAddrList          String[]
  selectMode               Int?
  testMethod               Int?
  isSuspended              Boolean           @default(false)

  relatedTotServerIds Int[]

  @@index([portV4])
  @@index([subscriptionId])
  @@index([outboundEndpointId])
  @@index([outboundEndpointId, subscriptionId])
  @@index([outboundEndpointId, portV4])
  @@index([outboundEndpointId, subscriptionId, portV4])
  @@index([isSuspended])
}

model MgmtAuthorization {
  id        Int      @id @default(autoincrement())
  name      String
  pubkey    String   @unique
  blocked   Boolean  @default(false)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([pubkey])
  @@index([blocked])
}

model TgData {
  id      Int    @id @default(autoincrement())
  chat_id BigInt @unique
  data    String
}
