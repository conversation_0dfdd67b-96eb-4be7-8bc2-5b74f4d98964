-- CreateTable
CREATE TABLE "Subscription" (
    "id" SERIAL NOT NULL,
    "tokenId" TEXT NOT NULL,
    "ownerAddress" TEXT NOT NULL,
    "tgUser" TEXT,
    "tgChatId" TEXT,
    "billingType" INTEGER NOT NULL,
    "basePrice" INTEGER NOT NULL,
    "recurringPrice" INTEGER NOT NULL,
    "bandwidth" INTEGER,
    "traffic" BIGINT NOT NULL,
    "validUntil" TIMESTAMP(3) NOT NULL,
    "resetDays" INTEGER,
    "lastReset" TIMESTAMP(3),
    "activated" BOOLEAN NOT NULL,
    "maxPortNumPerServer" INTEGER NOT NULL,
    "maxForwardEndpointNum" INTEGER NOT NULL DEFAULT 5,
    "lines" INTEGER[],
    "allowForwardEndpoint" BOOLEAN NOT NULL DEFAULT false,
    "note" TEXT NOT NULL DEFAULT '',
    "allowIpNum" INTEGER,
    "allowConnNum" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "isAdmin" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "Subscription_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "OutboundEndpoint" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "pubkey" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "hidden" BOOLEAN NOT NULL DEFAULT true,
    "ingressIpv4" TEXT NOT NULL,
    "portStart" INTEGER,
    "portEnd" INTEGER,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "trafficScale" DOUBLE PRECISION,
    "allowForward" BOOLEAN DEFAULT true,
    "allowLatencyTest" BOOLEAN DEFAULT false,
    "allowIpNum" INTEGER,
    "allowConnNum" INTEGER,
    "privateKey" TEXT,
    "interfaceName" TEXT,
    "version" TEXT,
    "proxyConfig" TEXT,
    "relatedForwardEndpointIds" INTEGER[],
    "allowIpV6" BOOLEAN NOT NULL DEFAULT false,
    "useForwardAsTun" BOOLEAN DEFAULT false,

    CONSTRAINT "OutboundEndpoint_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "ForwardEndpoint" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "displayName" TEXT NOT NULL,
    "ingressAddress" TEXT NOT NULL,
    "servePort" INTEGER NOT NULL,
    "protocol" TEXT NOT NULL,
    "protocolConfig" TEXT NOT NULL,
    "privKey" TEXT NOT NULL,
    "wsPort" INTEGER,
    "isPublic" BOOLEAN NOT NULL DEFAULT false,
    "subscriptionId" INTEGER,
    "setupScript" TEXT,
    "tokenId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "trafficScale" DOUBLE PRECISION,
    "allowIpV6" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "ForwardEndpoint_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "Port" (
    "id" SERIAL NOT NULL,
    "displayName" TEXT NOT NULL,
    "portV4" INTEGER NOT NULL,
    "targetAddressV4" TEXT NOT NULL,
    "targetPortV4" INTEGER NOT NULL,
    "traffic_in" BIGINT NOT NULL,
    "traffic_out" BIGINT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "subscriptionId" INTEGER,
    "outboundEndpointId" INTEGER,
    "forwardProtocol" TEXT,
    "forwardConfig" TEXT,
    "relatedForwardEndpointIds" INTEGER[],
    "targetAddrList" TEXT[],
    "selectMode" INTEGER,
    "testMethod" INTEGER,
    "isSuspended" BOOLEAN NOT NULL DEFAULT false,
    "relatedTotServerIds" INTEGER[],

    CONSTRAINT "Port_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "MgmtAuthorization" (
    "id" SERIAL NOT NULL,
    "name" TEXT NOT NULL,
    "pubkey" TEXT NOT NULL,
    "blocked" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MgmtAuthorization_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TgData" (
    "id" SERIAL NOT NULL,
    "chat_id" BIGINT NOT NULL,
    "data" TEXT NOT NULL,

    CONSTRAINT "TgData_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "Subscription_tokenId_key" ON "Subscription"("tokenId");

-- CreateIndex
CREATE INDEX "Subscription_tokenId_idx" ON "Subscription"("tokenId");

-- CreateIndex
CREATE INDEX "Subscription_ownerAddress_idx" ON "Subscription"("ownerAddress");

-- CreateIndex
CREATE INDEX "Subscription_billingType_idx" ON "Subscription"("billingType");

-- CreateIndex
CREATE INDEX "Subscription_tokenId_ownerAddress_idx" ON "Subscription"("tokenId", "ownerAddress");

-- CreateIndex
CREATE INDEX "Subscription_bandwidth_idx" ON "Subscription"("bandwidth");

-- CreateIndex
CREATE INDEX "Subscription_validUntil_idx" ON "Subscription"("validUntil" DESC);

-- CreateIndex
CREATE UNIQUE INDEX "OutboundEndpoint_name_key" ON "OutboundEndpoint"("name");

-- CreateIndex
CREATE UNIQUE INDEX "OutboundEndpoint_pubkey_key" ON "OutboundEndpoint"("pubkey");

-- CreateIndex
CREATE INDEX "OutboundEndpoint_hidden_idx" ON "OutboundEndpoint"("hidden");

-- CreateIndex
CREATE INDEX "OutboundEndpoint_pubkey_idx" ON "OutboundEndpoint"("pubkey");

-- CreateIndex
CREATE UNIQUE INDEX "ForwardEndpoint_name_key" ON "ForwardEndpoint"("name");

-- CreateIndex
CREATE UNIQUE INDEX "ForwardEndpoint_tokenId_key" ON "ForwardEndpoint"("tokenId");

-- CreateIndex
CREATE INDEX "ForwardEndpoint_tokenId_idx" ON "ForwardEndpoint"("tokenId");

-- CreateIndex
CREATE INDEX "Port_portV4_idx" ON "Port"("portV4");

-- CreateIndex
CREATE INDEX "Port_subscriptionId_idx" ON "Port"("subscriptionId");

-- CreateIndex
CREATE INDEX "Port_outboundEndpointId_idx" ON "Port"("outboundEndpointId");

-- CreateIndex
CREATE INDEX "Port_outboundEndpointId_subscriptionId_idx" ON "Port"("outboundEndpointId", "subscriptionId");

-- CreateIndex
CREATE INDEX "Port_outboundEndpointId_portV4_idx" ON "Port"("outboundEndpointId", "portV4");

-- CreateIndex
CREATE INDEX "Port_outboundEndpointId_subscriptionId_portV4_idx" ON "Port"("outboundEndpointId", "subscriptionId", "portV4");

-- CreateIndex
CREATE INDEX "Port_isSuspended_idx" ON "Port"("isSuspended");

-- CreateIndex
CREATE UNIQUE INDEX "MgmtAuthorization_pubkey_key" ON "MgmtAuthorization"("pubkey");

-- CreateIndex
CREATE INDEX "MgmtAuthorization_pubkey_idx" ON "MgmtAuthorization"("pubkey");

-- CreateIndex
CREATE INDEX "MgmtAuthorization_blocked_idx" ON "MgmtAuthorization"("blocked");

-- CreateIndex
CREATE UNIQUE INDEX "TgData_chat_id_key" ON "TgData"("chat_id");

-- AddForeignKey
ALTER TABLE "ForwardEndpoint" ADD CONSTRAINT "ForwardEndpoint_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Port" ADD CONSTRAINT "Port_subscriptionId_fkey" FOREIGN KEY ("subscriptionId") REFERENCES "Subscription"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Port" ADD CONSTRAINT "Port_outboundEndpointId_fkey" FOREIGN KEY ("outboundEndpointId") REFERENCES "OutboundEndpoint"("id") ON DELETE SET NULL ON UPDATE CASCADE;
