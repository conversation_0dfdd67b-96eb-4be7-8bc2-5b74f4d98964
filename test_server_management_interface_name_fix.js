/**
 * Test script to verify the Server Management interface_name fix
 * 
 * This test verifies that:
 * 1. Servers with empty/null interface_name can be loaded
 * 2. The modify server form validates interface_name as required
 * 3. When interface_name is filled and submitted, it's included in the API request
 * 4. The backend properly processes and updates the interface_name field
 */

const { test, expect } = require('@playwright/test');

test.describe('Server Management Interface Name Fix', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the server management page
    await page.goto('/server-management');
    
    // Wait for the page to load
    await page.waitForSelector('[data-testid="server-table"]', { timeout: 10000 });
  });

  test('should handle servers with empty interface_name and allow modification', async ({ page }) => {
    // Step 1: Find a server with empty interface_name or create a test scenario
    const serverRows = await page.locator('tbody tr').count();
    
    if (serverRows === 0) {
      console.log('No servers found, skipping test');
      return;
    }

    // Step 2: Click edit on the first server
    await page.locator('tbody tr').first().locator('[data-testid="edit-button"]').click();
    
    // Step 3: Wait for edit dialog to open
    await page.waitForSelector('[data-testid="edit-server-dialog"]', { timeout: 5000 });
    
    // Step 4: Check if interface_name field exists and is editable
    const interfaceNameField = page.locator('input[placeholder="Enter interface name"]');
    await expect(interfaceNameField).toBeVisible();
    
    // Step 5: Clear and fill the interface_name field
    await interfaceNameField.clear();
    await interfaceNameField.fill('eth0-test');
    
    // Step 6: Set up network request interception to verify API payload
    let modifyServerRequest = null;
    
    page.on('request', request => {
      if (request.url().includes('/mod_server') && request.method() === 'POST') {
        modifyServerRequest = request.postDataJSON();
      }
    });
    
    // Step 7: Submit the form
    await page.locator('[data-testid="save-changes-button"]').click();
    
    // Step 8: Wait for the request to be made
    await page.waitForTimeout(2000);
    
    // Step 9: Verify that interface_name was included in the request
    expect(modifyServerRequest).not.toBeNull();
    expect(modifyServerRequest).toHaveProperty('interface_name');
    expect(modifyServerRequest.interface_name).toBe('eth0-test');
    
    // Step 10: Verify success message
    await expect(page.locator('.el-message--success')).toBeVisible({ timeout: 5000 });
  });

  test('should validate interface_name as required field', async ({ page }) => {
    // Step 1: Click edit on the first server
    const serverRows = await page.locator('tbody tr').count();
    if (serverRows === 0) return;
    
    await page.locator('tbody tr').first().locator('[data-testid="edit-button"]').click();
    await page.waitForSelector('[data-testid="edit-server-dialog"]', { timeout: 5000 });
    
    // Step 2: Clear the interface_name field
    const interfaceNameField = page.locator('input[placeholder="Enter interface name"]');
    await interfaceNameField.clear();
    
    // Step 3: Try to submit the form
    await page.locator('[data-testid="save-changes-button"]').click();
    
    // Step 4: Verify validation error appears
    await expect(page.locator('.el-form-item__error')).toContainText('Please enter interface name');
  });

  test('should include all required fields in modify server request', async ({ page }) => {
    const serverRows = await page.locator('tbody tr').count();
    if (serverRows === 0) return;
    
    // Open edit dialog
    await page.locator('tbody tr').first().locator('[data-testid="edit-button"]').click();
    await page.waitForSelector('[data-testid="edit-server-dialog"]', { timeout: 5000 });
    
    // Fill all form fields
    await page.locator('input[placeholder="Please enter display name"]').fill('Test Server');
    await page.locator('input[placeholder="Enter IP address"]').fill('*************');
    await page.locator('input[placeholder="Enter interface name"]').fill('eth0');
    
    // Set up request interception
    let modifyServerRequest = null;
    page.on('request', request => {
      if (request.url().includes('/mod_server') && request.method() === 'POST') {
        modifyServerRequest = request.postDataJSON();
      }
    });
    
    // Submit form
    await page.locator('[data-testid="save-changes-button"]').click();
    await page.waitForTimeout(2000);
    
    // Verify all expected fields are present
    expect(modifyServerRequest).not.toBeNull();
    expect(modifyServerRequest).toHaveProperty('server_id');
    expect(modifyServerRequest).toHaveProperty('display_name', 'Test Server');
    expect(modifyServerRequest).toHaveProperty('ip_addr', '*************');
    expect(modifyServerRequest).toHaveProperty('interface_name', 'eth0');
  });
});

// Helper function to run manual API test
async function testModifyServerAPI() {
  const testPayload = {
    server_id: 1,
    display_name: "Test Server",
    ip_addr: "*************", 
    interface_name: "eth0",
    port_start: 30000,
    port_end: 31000,
    traffic_scale: 1.0,
    allow_forward: false,
    allow_latency_test: true,
    allow_ipv6: false,
    balance_strategy: 0,
    use_forward_as_tun: false,
    forward_endpoints: []
  };
  
  try {
    const response = await fetch('/mod_server', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify(testPayload)
    });
    
    console.log('API Response Status:', response.status);
    console.log('API Response:', await response.text());
    
    return response.ok;
  } catch (error) {
    console.error('API Test Error:', error);
    return false;
  }
}

module.exports = { testModifyServerAPI };
