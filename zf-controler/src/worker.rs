use std::{
    collections::{HashMap, HashSet},
    net::{IpAddr, SocketAddr},
};

use crate::{
    app::{FutureQueueItem, WorkerInfo},
    online_tracker::OnlineTracker,
    preludes::*,
    prisma,
    stats::{write_netcard_stats, write_system_stats},
};
use app_message::*;
use common::{
    retry_queue::{Job, RetryError},
    tokio_util::sync::CancellationToken,
};
use futures_util::future::join_all;
use prisma_client_rust::operator::*;
use private_tun::snell_impl_ver::config::ClientConfig;
use tokio::sync::{oneshot, Mutex};

pub struct WorkerSessionGuard {
    pub worker_id: i32,
    pub ctx: WrappedAppContext,
}

pub struct WorkerSession {
    pub worker_info: WorkerInfo::Data,
    pub ctx: WrappedAppContext,
    pub config_map: Arc<Mutex<HashMap<u16, PortConfig>>>,
    pub pending_latency_test:
        Arc<Mutex<HashMap<u64, oneshot::Sender<Result<WorkerTestLatencyResponse>>>>>,
}

macro_rules! get_number {
    ($conn:expr, $k:expr, $d:expr) => {
        match $conn.get($k).await {
            Ok(v) => v,
            Err(e) => match e.kind() {
                redis::ErrorKind::TypeError => $d,
                _ => return Err(e.into()),
            },
        }
    };
}

async fn get_one_socket_addr(
    worker_id: i32,
    target_addr: &str,
    port: u16,
) -> anyhow::Result<smallvec::SmallVec<[SocketAddr; 3]>> {
    // ipv6
    if target_addr.starts_with('[') && target_addr.ends_with(']') {
        let v = target_addr[1..target_addr.len() - 1].parse::<IpAddr>()?;
        return Ok(smallvec::smallvec![(v, port).into()]);
    }
    match target_addr.parse::<IpAddr>() {
        Ok(v) => Ok(smallvec::smallvec![(v, port).into()]),
        Err(_e) => {
            // try lookup dns
            let s = match tokio::net::lookup_host((target_addr, port)).await {
                Ok(v) => v,
                Err(e) => {
                    return Err(anyhow::anyhow!(
                        "worker:{}  target_addr:{} port:{} dns lookup failed: {}",
                        worker_id,
                        target_addr,
                        port,
                        e
                    ));
                }
            };
            let mut rst = s.collect::<smallvec::SmallVec<_>>();
            if rst.is_empty() {
                return Err(anyhow::anyhow!(
                    "worker:{}  target_addr:{} port:{} dns lookup result empty",
                    worker_id,
                    target_addr,
                    port
                ));
            }
            if rst.len() > 1 {
                rst.sort();
            }
            Ok(rst)
        }
    }
}

fn split_target_addr(worker_id: i32, target_addr: &str) -> anyhow::Result<(&str, u16)> {
    let (addr, _comment) = match target_addr.split_once('#') {
        Some((addr, comment)) => (addr, comment),
        None => (target_addr, ""),
    };
    let (domain, port) = addr.rsplit_once(':').ok_or(anyhow::anyhow!(
        "worker:{}  target_addr:{} dns lookup result empty",
        worker_id,
        target_addr
    ))?;
    Ok((domain, port.parse::<u16>()?))
}

async fn get_target_addr_list(
    worker_id: i32,
    p: &UpdatePortInfo::Data,
) -> anyhow::Result<Vec<SocketAddr>> {
    if p.target_addr_list.is_empty() {
        // just only one target addr
        Ok(Vec::from_iter(
            get_one_socket_addr(
                worker_id,
                p.target_address_v_4.as_str(),
                p.target_port_v_4 as u16,
            )
            .await?
            .into_iter(),
        ))
    } else {
        let socket_str_list = p
            .target_addr_list
            .iter()
            .map(|x| (x, split_target_addr(worker_id, x)))
            .collect::<Vec<_>>();
        let tasks = socket_str_list
            .into_iter()
            .filter_map(|x| x.1.ok())
            .map(|(socket_str, port)| async move {
                get_one_socket_addr(worker_id, socket_str, port).await
            })
            .collect::<Vec<_>>();
        let len = tasks.len();
        let results = join_all(tasks)
            .await
            .into_iter()
            .filter_map(|x| x.ok())
            .fold(Vec::with_capacity(len), |mut acc, x| {
                acc.extend(x);
                acc
            });
        Ok(results)
    }
}

prisma::port::select!( UpdatePortInfo {
    port_v_4
    target_port_v_4
    target_address_v_4
    target_addr_list
    forward_protocol
    forward_config
    select_mode
    test_method
    is_suspended
    outbound_endpoint: select {
        allow_forward
        allow_ip_num
        allow_conn_num
    }
    subscription: select {
        id
        traffic
        valid_until
        bandwidth
        allow_ip_num
        allow_conn_num
    }
});
fn two_option_get_min<'a, T>(a: &'a Option<T>, b: &'a Option<T>) -> Option<&'a T>
where
    T: Ord,
{
    match (a, b) {
        (Some(a), Some(b)) => Some(a.min(b)),
        (Some(a), None) => Some(a),
        (None, Some(b)) => Some(b),
        (None, None) => None,
    }
}
impl WorkerSession {
    pub fn create(worker_info: WorkerInfo::Data, ctx: WrappedAppContext) -> Arc<Self> {
        let ret = Self {
            worker_info,
            ctx,
            config_map: Arc::new(Mutex::new(HashMap::new())),
            pending_latency_test: Arc::new(Mutex::new(HashMap::new())),
        };
        Arc::new(ret)
    }
    pub async fn update_config_map(self: &Arc<Self>) -> Result<()> {
        let redis = self.ctx.redis.clone();
        let db = self.ctx.db.clone();
        let current_time = chrono::Utc::now().fixed_offset();
        let port_list_data = match db
            .port()
            .find_many(vec![and(vec![
                port::outbound_endpoint_id::equals(Some(self.worker_info.id)),
                port::subscription::is(vec![and(vec![
                    subscription::activated::equals(true),
                    subscription::valid_until::gte(current_time),
                ])]),
            ])])
            .select(UpdatePortInfo::select())
            .exec()
            .await
        {
            Ok(v) => v,
            Err(e) => {
                error!(
                    "worker:{} session_loop: find db ports error: {}",
                    self.worker_info.pubkey, e
                );
                return Err(e.into());
            }
        };
        log::debug!(
            "worker:{}, ports: {:?}",
            self.worker_info.pubkey,
            port_list_data
                .iter()
                .map(|x| x.port_v_4)
                .collect::<Vec<_>>()
        );
        // performance optimization
        if port_list_data.is_empty() {
            *self.config_map.lock().await = HashMap::new();
            return Ok(());
        }
        let mut conn = redis.get().await?;
        let mut config_map = HashMap::new();
        for p in port_list_data.iter() {
            if let Some(s) = &p.subscription {
                if current_time >= s.valid_until {
                    log::debug!(
                            "worker:{} session_loop: find db ports success, port: {}, subscription: {} expired",
                            self.worker_info.pubkey,
                            p.port_v_4,
                            s.id
                        );
                    continue;
                }

                // Skip suspended ports
                if p.is_suspended {
                    log::debug!(
                        "worker:{} session_loop: skipping suspended port: {}, subscription: {}",
                        self.worker_info.pubkey,
                        p.port_v_4,
                        s.id
                    );
                    continue;
                }

                let port = p.port_v_4 as u16;

                let used: u128 = get_number!(conn, format!("sub:used:{}", s.id), 0);

                let avail_traffics = s.traffic;
                let avail_traffics = (avail_traffics as u128) * 1024 * 1024 * 1024;
                let avail_traffics = if used >= avail_traffics {
                    log::debug!(
                        "worker:{}  port: {}, subscription: {} used: {}, avail_traffics: {}",
                        self.worker_info.pubkey,
                        p.port_v_4,
                        s.id,
                        used,
                        avail_traffics
                    );
                    continue;
                } else {
                    avail_traffics - used
                };

                let target_addr_list = match get_target_addr_list(self.worker_info.id, p).await {
                    Ok(v) => {
                        if v.is_empty() {
                            warn!(
                                "worker:{} session_loop: find db ports success, port: {}, subscription: {} get_target_addr_list result empty",
                                self.worker_info.pubkey,
                                p.port_v_4,
                                s.id
                            );
                            continue;
                        }
                        v
                    }
                    Err(e) => {
                        error!(
                            "worker:{} session_loop: find db ports success, port: {}, subscription: {} get_target_addr_list failed, error: {}",
                            self.worker_info.pubkey,
                            p.port_v_4,
                            s.id,
                            e
                            );
                        continue;
                    }
                };
                let protocol = if p
                    .outbound_endpoint
                    .as_ref()
                    .map(|x| x.allow_forward)
                    .flatten()
                    .unwrap_or_default()
                {
                    if let (Some(forward_protocol), Some(forward_config)) =
                        (p.forward_protocol.as_ref(), p.forward_config.as_ref())
                    {
                        // current only support hammer
                        if forward_protocol.to_lowercase() == "hammer" {
                            Some(Protocol::Hammer {
                                config: forward_config.clone(),
                            })
                        } else if forward_protocol.to_lowercase() == "tot" {
                            Some(Protocol::Tot {
                                config: forward_config.clone(),
                            })
                        } else {
                            None
                        }
                    } else {
                        None
                    }
                } else {
                    None
                };
                log::debug!(
                    "worker:{} session_loop: update config map, port: {}",
                    self.worker_info.pubkey,
                    port
                );
                let mode = {
                    // 如果只有一个目标地址，则使用protocol的mode
                    let run_mode = if target_addr_list.len() == 1 {
                        // 使用protocol的mode
                        match protocol.as_ref() {
                            Some(Protocol::Hammer { config }) => {
                                if let Ok(config) = serde_json::from_str::<ClientConfig>(config) {
                                    if let Some(mode) =
                                        config.multi_server_config.as_ref().map(|x| x.mode)
                                    {
                                        mode.try_into().ok()
                                    } else {
                                        None
                                    }
                                } else {
                                    None
                                }
                            }
                            _ => None,
                        }
                    } else {
                        None
                    };
                    match run_mode {
                        Some(m) => Some(m),
                        None => match p.select_mode.as_ref().and_then(|m| {
                            TryInto::<common::app_message::Mode>::try_into(*m as u32).ok()
                        }) {
                            Some(m) => Some(m),
                            None => None,
                        },
                    }
                };
                let allow_ip_num = two_option_get_min(
                    &s.allow_ip_num,
                    &p.outbound_endpoint
                        .as_ref()
                        .map(|x| x.allow_ip_num)
                        .flatten(),
                )
                .copied();
                let allow_conn_num = two_option_get_min(
                    &s.allow_conn_num,
                    &p.outbound_endpoint
                        .as_ref()
                        .map(|x| x.allow_conn_num)
                        .flatten(),
                )
                .copied();
                let _ = config_map.insert(
                    port,
                    PortConfig {
                        port,
                        subscription_id: s.id,
                        bandwidth: s.bandwidth.map(|x| x as u16),
                        avail_traffics,
                        target_addr_list,
                        protocol,
                        mode,
                        latency_test_method: p
                            .test_method
                            .as_ref()
                            .and_then(|m| TryInto::<LatencyTestMethod>::try_into(*m as u32).ok()),
                        allow_ip_num,
                        allow_conn_num,
                    },
                );
            }
        }
        *self.config_map.lock().await = config_map;
        Ok(())
    }
    pub async fn session_loop(self: Arc<Self>, cancel_token: CancellationToken) -> Result<()> {
        loop {
            if cancel_token.is_cancelled() {
                info!(
                    "worker:{} session_loop exited by cancel token",
                    self.worker_info.pubkey
                );
                return Ok(());
            }
            match self.update_config_map().await {
                Ok(_) => {}
                Err(e) => {
                    error!(
                        "worker:{} session_loop find db ports error: {}",
                        self.worker_info.pubkey, e
                    );
                }
            }
            tokio::time::sleep(std::time::Duration::from_secs(30)).await;
        }
    }

    pub async fn handle_ws_message(
        self: Arc<Self>,
        tx: mpsc::UnboundedSender<AppWsResponse>,
        msg: AppWsRequest,
        q_tx: mpsc::UnboundedSender<FutureQueueItem>,
        online_tracker: Arc<OnlineTracker>,
        worker_id: usize,
    ) -> Result<()> {
        match msg {
            AppWsRequest::Encrypted(_) => {
                bail!("Unexpected encrypted message!");
            }
            AppWsRequest::Error(e) => {
                error!(
                    "Error from worker {}: {}",
                    self.worker_info.pubkey.as_str(),
                    e
                );
            }
            AppWsRequest::Pong => {
                online_tracker.tick(worker_id).await;
            }
            AppWsRequest::PortTrafficUsage(u) => {
                //                self.clone()
                //                    .handle_port_traffic_usage(tx.clone(), u)
                //                    .await?;
                let (shot_tx, rx) = oneshot::channel();
                let f = self.clone().handle_port_traffic_usage(tx.clone(), u);
                match q_tx.send((Some(shot_tx), Box::pin(f))) {
                    Ok(_) => {
                        rx.await??;
                    }
                    _ => bail!("handle_ws_message PortTrafficUsage send failed"),
                }
            }
            AppWsRequest::ForwardEndpointTrafficReport(report) => {
                let f = self
                    .clone()
                    .handle_forward_endpoint_traffic_usage(tx.clone(), report);
                match q_tx.send((None, Box::pin(f))) {
                    Ok(_) => {}
                    _ => bail!("handle_ws_message ForwardEndpointTrafficReport send failed"),
                }
            }
            AppWsRequest::ShouldDumpPortMap => {
                //                self.clone().send_port_map(tx.clone()).await?;

                let (shot_tx, rx) = oneshot::channel();
                let f = self.clone().send_port_map(tx.clone());
                match q_tx.send((Some(shot_tx), Box::pin(f))) {
                    Ok(_) => {
                        rx.await??;
                    }
                    _ => bail!("handle_ws_message ShouldDumpPortMap send failed"),
                }
            }
            AppWsRequest::StatsReport(stats_report) => {
                let f = self.clone().handle_stats_report(stats_report);
                match q_tx.send((None, Box::pin(f))) {
                    Ok(_) => {}
                    _ => bail!("handle_ws_message StatsReport send failed"),
                }
            }
            AppWsRequest::TestLatencyResp {
                request_id,
                latencies,
            } => {
                let mut map = self.pending_latency_test.lock().await;
                if let Some(tx) = map.remove(&request_id) {
                    log::info!(
                        "test latency response: {:?} req_id: {}",
                        latencies,
                        request_id
                    );
                    let _ = tx.send(Ok(WorkerTestLatencyResponse { latencies }));
                }
            }
        }
        Ok(())
    }

    async fn handle_port_traffic_usage(
        self: Arc<Self>,
        _tx: mpsc::UnboundedSender<AppWsResponse>,
        u: Vec<PortTrafficUsage>,
    ) -> Result<()>
    where
        Self: Send + Sync + 'static,
    {
        let factory = UpdatePortTrafficUsageJob::new(self.clone(), u);
        self.ctx.traffic_retry_queue.send(Box::new(factory));
        Ok(())
    }

    async fn handle_forward_endpoint_traffic_usage(
        self: Arc<Self>,
        _tx: mpsc::UnboundedSender<AppWsResponse>,
        u: Vec<StatData>,
    ) -> Result<()> {
        let job = UpdateForwardEndpointTrafficUsageJob::new(self.clone(), u);
        self.ctx.traffic_retry_queue.send(Box::new(job));
        Ok(())
    }

    async fn send_port_map(
        self: Arc<Self>,
        tx: mpsc::UnboundedSender<AppWsResponse>,
    ) -> Result<()> {
        let m = self.config_map.lock().await.clone();
        let _ = tx.send(AppWsResponse::PortMapUpdate(m));
        Ok(())
    }

    async fn handle_stats_report(self: Arc<Self>, stats_report: StatsReport) -> Result<()> {
        let worker_id = self.worker_info.id;
        if let Err(e) = write_netcard_stats(&self.ctx.influxdb, worker_id, &stats_report).await {
            error!("handle_stats_report: write_netcard_stats error: {}", e);
        }
        if let Err(e) = write_system_stats(&self.ctx.influxdb, worker_id, &stats_report).await {
            error!("handle_stats_report: write_system_stats error: {}", e);
        }
        trace!(
            "handle_stats_report: {}, report: {:?}",
            worker_id,
            stats_report
        );

        Ok(())
    }
}

prisma::port::select!(
    UpdateTrafficData{
        port_v_4
        subscription_id
    }
);

async fn handle_port_traffic_usage_inner(
    session: Arc<WorkerSession>,
    u: &mut HashMap<u16, PortTrafficUsage>,
    sub_acc: &mut HashMap<i32, u128>,
) -> Result<(), RetryError<anyhow::Error>> {
    let redis = session.ctx.redis.clone();
    let db = session.ctx.db.clone();
    let worker_id = session.worker_info.id;
    let port_temp_map = u
        .iter()
        .map(|x| (*x.0 as i32, x.1.used))
        .collect::<HashMap<_, _>>();
    debug!("handle_port_traffic_usage: {}, usage: {:?}", worker_id, u);
    let all_ports = u.iter().map(|x| *x.0 as i32).collect::<Vec<_>>();
    let Ok(all_ports_data) = db
        .port()
        .find_many(vec![and(vec![
            port::port_v_4::in_vec(all_ports),
            port::outbound_endpoint_id::equals(Some(worker_id)),
        ])])
        .select(UpdateTrafficData::select())
        .exec()
        .await
    else {
        error!("handle_port_traffic_usage: find ports failed!");
        return Err(RetryError::Retry(anyhow::anyhow!(
            "worker:{} find ports failed when handle port traffic usage",
            worker_id
        )));
    };
    let mut conn = redis.get().await.map_err(|e| {
        RetryError::Retry(anyhow::anyhow!(
            "worker:{} get redis failed when handle port traffic usage: {}",
            worker_id,
            e
        ))
    })?;
    let line_lock = get_line_lock(&session.ctx.redis, &session.ctx.redis_line_lock, worker_id)
        .await
        .map_err(RetryError::Retry)?;
    for port in all_ports_data {
        let Ok(end_point_data) = session.ctx.cached_endpoint_list.get(&worker_id).await else {
            error!(
                "worker: {} Port: {} endpoint not found!",
                worker_id, port.port_v_4
            );
            continue;
        };
        let Some(subscription_id) = port.subscription_id else {
            error!(
                "worker: {} Port: {} subscription is None!",
                worker_id, port.port_v_4
            );
            continue;
        };
        let Some(raw_used) = port_temp_map.get(&port.port_v_4) else {
            error!(
                "worker: {} Port: {} used is None!",
                worker_id, port.port_v_4
            );
            continue;
        };
        log::debug!(
            "worker: {} Port: {} raw_used: {}",
            worker_id,
            port.port_v_4,
            raw_used
        );

        let scale = end_point_data.traffic_scale.unwrap_or(1.0);
        log::debug!(
            "worker: {} Port: {} scale: {}",
            worker_id,
            port.port_v_4,
            scale
        );

        let used = (*raw_used as f64 * scale) as i128;
        if used < 0 || used > *********** {
            log::error!(
                "worker: {} Port: {} scaled_used: {} abnormal (raw_used: {}, scale: {})",
                worker_id,
                port.port_v_4,
                used,
                raw_used,
                scale
            );
            continue;
        }
        // let used_cache: i128 = get_number!(conn, format!("sub:used:{}", subscription_id), 0);
        let port_used_cache: i128 = get_number!(
            conn,
            format!(
                "sub:line:port:used:{}:{}:{}",
                subscription_id, worker_id, port.port_v_4
            ),
            0
        );

        log::debug!(
            "worker: {} Port: {}, port_used_cache: {} used: {}",
            worker_id,
            port.port_v_4,
            port_used_cache,
            used
        );

        let port_amount = match port_used_cache.checked_add(used) {
            Some(amount) => amount,
            None => {
                log::error!(
                    "worker: {} Port: {} port_amount overflow: {} + {}",
                    worker_id,
                    port.port_v_4,
                    port_used_cache,
                    used
                );
                continue;
            }
        };

        match conn
            .set::<_, _, ()>(
                format!(
                    "sub:line:port:used:{}:{}:{}",
                    subscription_id, worker_id, port.port_v_4
                ),
                port_amount.to_string(),
            )
            .await
        {
            Ok(_) => {
                u.remove(&(port.port_v_4 as u16));
                *sub_acc.entry(subscription_id).or_insert(0) += used as u128;
            }
            Err(e) => {
                error!(
                    "worker:{} set sub:used:{} failed: {}",
                    worker_id, subscription_id, e
                );
                return Err(RetryError::Retry(anyhow::anyhow!(
                    "worker:{} set sub:used:{} failed: {}",
                    worker_id,
                    subscription_id,
                    e
                )));
            }
        }
    }
    drop(line_lock);
    // proc sub_acc
    let _lock = match session.ctx.redis_lock.clone().acquire().await {
        Ok(g) => g,
        Err(_) => {
            error!("worker:{} get global traffic_usage_lock failed", worker_id);
            return Err(RetryError::Retry(anyhow::anyhow!(
                "worker:{} get global traffic_usage_lock failed",
                worker_id
            )));
        }
    };
    for (sub_id, inc) in sub_acc.iter_mut() {
        if *inc == 0 {
            continue;
        }
        let used_cache: i128 = get_number!(conn, format!("sub:used:{}", sub_id), 0);
        let added_amount = used_cache + *inc as i128;
        match conn
            .set::<_, _, ()>(format!("sub:used:{}", sub_id), added_amount.to_string())
            .await
        {
            Ok(_) => {
                log::debug!(
                    "worker:{} set sub:{} amount:{}, inc:{} success",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc
                );
            }
            Err(e) => {
                error!(
                    "worker:{} set sub:{} amount:{}, inc:{} failed: {}",
                    worker_id, sub_id, added_amount, inc, e
                );
                return Err(RetryError::Retry(anyhow::anyhow!(
                    "worker:{} set sub:{} amount:{}, inc:{} failed: {}",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc,
                    e
                )));
            }
        }
        *inc = 0;
    }

    Ok(())
}

prisma::port::select!(
    UpdateForwardEndpointTrafficData{
        port_v_4
        subscription_id
        related_forward_endpoint_ids
        related_tot_server_ids
    }
);

async fn handle_forward_endpoint_traffic_usage_inner(
    session: Arc<WorkerSession>,
    u: &mut HashMap<(u16, String), StatData>,
    sub_acc: &mut HashMap<i32, u128>,
) -> Result<(), RetryError<anyhow::Error>> {
    let redis = session.ctx.redis.clone();
    let db = session.ctx.db.clone();
    let worker_id = session.worker_info.id;
    debug!(
        "handle_forward_endpoint_traffic_usage: {}, usage: {:?}",
        worker_id, u
    );
    let all_ports = u.iter().map(|x| x.0 .0 as i32).collect::<HashSet<_>>();
    let all_ports = all_ports.iter().map(|x| *x).collect::<Vec<_>>();
    let Ok(all_ports_data) = db
        .port()
        .find_many(vec![and(vec![
            port::port_v_4::in_vec(all_ports),
            port::outbound_endpoint_id::equals(Some(worker_id)),
        ])])
        .select(UpdateForwardEndpointTrafficData::select())
        .exec()
        .await
    else {
        error!(
            "worker:{} find ports failed when handle forward endpoint traffic usage",
            worker_id
        );
        return Err(RetryError::Retry(anyhow::anyhow!(
            "worker:{} find ports failed when handle forward endpoint traffic usage",
            worker_id
        )));
    };
    let mut conn = redis.get().await.map_err(|e| {
        RetryError::Retry(anyhow::anyhow!(
            "worker:{} get redis failed when handle forward endpoint traffic usage: {}",
            worker_id,
            e
        ))
    })?;
    let port_temp_map = u.iter().fold(HashMap::new(), |mut acc, ((p, f), v)| {
        let e = acc.entry((*p, f.clone())).or_insert(0);
        *e += v.traffic;
        acc
    });
    let line_lock = get_line_lock(&session.ctx.redis, &session.ctx.redis_line_lock, worker_id)
        .await
        .map_err(RetryError::Retry)?;
    for port in all_ports_data {
        let related_ids = port
            .related_forward_endpoint_ids
            .iter()
            .chain(port.related_tot_server_ids.iter());
        for forward_id in related_ids {
            let Some(used) = port_temp_map.get(&(port.port_v_4 as u16, forward_id.to_string()))
            else {
                continue;
            };

            let Some(subscription_id) = port.subscription_id else {
                error!(
                    "worker: {} Port: {} subscription is None!",
                    worker_id, port.port_v_4
                );
                continue;
            };

            let Ok(forward_endpoint_data) = session
                .ctx
                .cached_forward_endpoint_list
                .get(&forward_id)
                .await
            else {
                error!(
                    "worker: {} Port: {} forward endpoint {} not found!",
                    worker_id, port.port_v_4, forward_id
                );
                continue;
            };

            let scale = forward_endpoint_data.traffic_scale.unwrap_or(1.0);
            let used = (*used as f64 * scale) as i128;
            if used > *********** {
                log::error!(
                    "worker: {} Port: {} forward endpoint {} used: {} abnormal",
                    worker_id,
                    port.port_v_4,
                    forward_id,
                    used
                );
                continue;
            }
            log::debug!(
                "worker: {} Port: {} forward endpoint {} used: {} fwd_scale: {}",
                worker_id,
                port.port_v_4,
                forward_id,
                used,
                scale
            );

            // let used_cache: i128 = get_number!(conn, format!("sub:used:{}", subscription_id), 0);
            let port_used_cache: i128 = get_number!(
                conn,
                format!(
                    "sub:line:port:used:{}:{}:{}",
                    subscription_id, worker_id, port.port_v_4
                ),
                0
            );

            let port_amount = port_used_cache + used;
            match conn
                .set::<_, _, ()>(
                    format!(
                        "sub:line:port:used:{}:{}:{}",
                        subscription_id, worker_id, port.port_v_4
                    ),
                    port_amount.to_string(),
                )
                .await
            {
                Ok(_) => {}
                Err(e) => {
                    error!(
                        "worker:{} set sub:line:port:used:{}:{}:{} failed: {}",
                        worker_id, subscription_id, worker_id, port.port_v_4, e
                    );
                    return Err(RetryError::Retry(anyhow::anyhow!(
                        "worker:{} set sub:line:port:used:{}:{}:{} failed: {}",
                        worker_id,
                        subscription_id,
                        worker_id,
                        port.port_v_4,
                        e
                    )));
                }
            }
            u.remove(&(port.port_v_4 as u16, forward_id.to_string()));
            *sub_acc.entry(subscription_id).or_insert(0) += used as u128;
        }
    }

    drop(line_lock);
    // proc sub_acc
    let _lock = match session.ctx.redis_lock.clone().acquire().await {
        Ok(g) => g,
        Err(_) => {
            error!("worker:{} get global traffic_usage_lock failed", worker_id);
            return Err(RetryError::Retry(anyhow::anyhow!(
                "worker:{} get global traffic_usage_lock failed",
                worker_id
            )));
        }
    };
    for (sub_id, inc) in sub_acc.iter_mut() {
        if *inc == 0 {
            continue;
        }
        let used_cache: i128 = get_number!(conn, format!("sub:used:{}", sub_id), 0);
        let added_amount = used_cache + *inc as i128;
        match conn
            .set::<_, _, ()>(format!("sub:used:{}", sub_id), added_amount.to_string())
            .await
        {
            Ok(_) => {
                log::debug!(
                    "worker:{} set fwd sub:{} amount:{}, inc:{} success",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc
                );
            }
            Err(e) => {
                error!(
                    "worker:{} set fwd sub:{} amount:{}, inc:{} failed: {}",
                    worker_id, sub_id, added_amount, inc, e
                );
                return Err(RetryError::Retry(anyhow::anyhow!(
                    "worker:{} set fwd sub:{} amount:{}, inc:{} failed: {}",
                    worker_id,
                    sub_id,
                    added_amount,
                    inc,
                    e
                )));
            }
        }
        *inc = 0;
    }
    Ok(())
}

pub struct UpdatePortTrafficUsageJob {
    session: Arc<WorkerSession>,
    u: HashMap<u16, PortTrafficUsage>,
    sub_acc: HashMap<i32, u128>,
}

impl UpdatePortTrafficUsageJob {
    pub fn new(session: Arc<WorkerSession>, u: Vec<PortTrafficUsage>) -> Self {
        let u = u.into_iter().map(|x| (x.port, x)).collect();
        Self {
            session,
            u,
            sub_acc: HashMap::new(),
        }
    }
}

#[async_trait::async_trait]
impl Job for UpdatePortTrafficUsageJob {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        handle_port_traffic_usage_inner(self.session.clone(), &mut self.u, &mut self.sub_acc).await
    }
}

pub struct UpdateForwardEndpointTrafficUsageJob {
    session: Arc<WorkerSession>,
    u: HashMap<(u16, String), StatData>,
    sub_acc: HashMap<i32, u128>,
}

impl UpdateForwardEndpointTrafficUsageJob {
    pub fn new(session: Arc<WorkerSession>, u: Vec<StatData>) -> Self {
        let u = u
            .into_iter()
            .map(|x| ((x.port as u16, x.forward_endpoint.clone()), x))
            .collect();
        Self {
            session,
            u,
            sub_acc: HashMap::new(),
        }
    }
}

#[async_trait::async_trait]
impl Job for UpdateForwardEndpointTrafficUsageJob {
    type Error = anyhow::Error;
    async fn run(&mut self) -> Result<(), RetryError<Self::Error>> {
        handle_forward_endpoint_traffic_usage_inner(
            self.session.clone(),
            &mut self.u,
            &mut self.sub_acc,
        )
        .await
    }
}
