pub use crate::prisma::*;
use crate::redis_lock::DistributedLock;
use crate::retry_queue::BoxedRetryQueue;
pub use anyhow::{bail, Error, Result};
use app_message::{WorkerTestLatencyRequest, WorkerTestLatencyResponse};
use bb8::Pool;
use bb8_redis::RedisConnectionManager;
use cache::Cache;
use clap::Parser;
pub use common::crypto::*;
pub use common::*;
use influxdb::Client as InfluxDBClient;
pub use log::{debug, error, info, trace, warn};
pub use primitive_types::{U128, U256};
pub use redis::AsyncCommands;
use std::collections::{HashMap, HashSet};
pub use std::str::FromStr;
use std::sync::atomic::AtomicU64;
pub use std::sync::Arc;
pub use tokio::sync::mpsc;
use tokio::sync::Mutex;

pub struct CommandWrapper<T, R> {
    pub command: T,
    pub rst_tx: tokio::sync::oneshot::Sender<anyhow::Result<R>>,
}
impl<T, R> CommandWrapper<T, R> {
    pub fn new(command: T, rst_tx: tokio::sync::oneshot::Sender<anyhow::Result<R>>) -> Self {
        Self { command, rst_tx }
    }
}

pub enum WorkerCommand {
    TestLatency(CommandWrapper<WorkerTestLatencyRequest, WorkerTestLatencyResponse>),
}
pub struct AppContext {
    pub opt: Opt,
    pub db: AppPrismaClient,
    pub influxdb: InfluxDBClient,
    pub redis: RedisPool,
    pub mgmt_priv_key: SecretKey,
    pub black_worker_list: Arc<Mutex<HashSet<String>>>,
    pub cached_endpoint_list: Arc<Cache<i32, outbound_endpoint::Data>>,
    pub cached_forward_endpoint_list: Arc<Cache<i32, forward_endpoint::Data>>,
    pub worker_command_tx: Arc<Mutex<HashMap<i32, mpsc::Sender<WorkerCommand>>>>,
    pub request_id: Arc<AtomicU64>,
    pub traffic_retry_queue: Arc<BoxedRetryQueue>,
    pub redis_lock: Arc<DistributedLock>,
    pub redis_line_lock: RedisLineLock,
}

pub async fn setup_redis(opt: &Opt) -> Result<RedisPool> {
    let manager = RedisConnectionManager::new(opt.redis_path.as_str())?;
    Ok(Pool::builder().build(manager).await?)
}

pub async fn setup_influxdb(opt: &Opt) -> Result<InfluxDBClient> {
    let client = InfluxDBClient::new(opt.influxdb_url.as_str(), opt.influxdb_db.as_str())
        .with_auth(opt.influxdb_user.as_str(), opt.influxdb_password.as_str());
    Ok(client)
}

pub type WrappedAppContext = Arc<AppContext>;

pub type AppPrismaClient = Arc<PrismaClient>;
pub type RedisPool = Pool<RedisConnectionManager>;

#[derive(Debug, Parser, Clone)]
pub struct Opt {
    #[clap(short = 'p', long, env, default_value_t = 3100)]
    pub mgmt_port: u16,
    #[clap(
        short,
        long,
        env,
        default_value = "postgres://postgres:postgres@127.0.0.1/datium"
    )]
    pub db_path: String,
    #[clap(short, long, env, default_value = "redis://127.0.0.1:6379")]
    pub redis_path: String,
    #[clap(short = 'k', long, env = "MGMT_ARRANGER_PRIV_KEY")]
    pub mgmt_priv_key: String,
    #[clap(short, long, env, default_value = "http://127.0.0.1:8086")]
    pub influxdb_url: String,
    #[clap(short, long, env, default_value = "")]
    pub influxdb_token: String,
    #[clap(short, long, env, default_value = "")]
    pub influxdb_db: String,
    #[clap(short, long, env, default_value = "")]
    pub influxdb_user: String,
    #[clap(short, long, env, default_value = "")]
    pub influxdb_password: String,
    #[clap(short, long, env, default_value = "worker/zf-worker")]
    pub worker_binary_path: String,
    #[clap(short, long, env)]
    pub worker_version: String,
    #[clap(short, long, env, default_value = "https://127.0.0.1:3100")]
    pub arranger_hosts_url: String,
}
