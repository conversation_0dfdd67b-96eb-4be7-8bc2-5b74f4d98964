use anyhow::Result;
use chrono::Utc;
use influxdb::{Client, InfluxDbWriteable};
use common::{
    app_message::StatsReport,
    stats::{NetcardSpeedMeasurement, SystemStatsMeasurement},
};

pub async fn write_netcard_stats(client: &Client, agent_id: i32, data: &StatsReport) -> Result<()> {
    for (interface, (rx, tx, total_rx, total_tx)) in data.netcard_speed.iter() {
        let measurement = NetcardSpeedMeasurement {
            time: Utc::now(),
            interface: interface.to_owned(),
            agent_id,
            tx: *tx as f64,
            rx: *rx as f64,
            total_tx: *total_tx as u64,
            total_rx: *total_rx as u64,
        };
        let write_query = measurement.into_query("netcard_speed");
        if let Err(e) = client.query(&write_query).await {
            log::error!("write_netcard_stats: {}, error: {}", agent_id, e);
        }
    }
    Ok(())
}

pub async fn write_system_stats(client: &Client, agent_id: i32, data: &StatsReport) -> Result<()> {
    let measurement = SystemStatsMeasurement {
        time: Utc::now(),
        agent_id,
        cpu_usage: data.cpu_usage,
        memory_total: data.memory_usage.1,
        memory_used: data.memory_usage.0,
        uptime: data.uptime,
    };
    let write_query = measurement.into_query("system_stats");
    if let Err(e) = client.query(&write_query).await {
        log::error!("write_system_stats: {}, error: {}", agent_id, e);
    }
    Ok(())
}
