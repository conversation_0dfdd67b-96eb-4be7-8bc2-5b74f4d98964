use crate::latency::test_latency;
use crate::online_tracker::{Event, OnlineTracker};
use crate::worker::WorkerSession;
use crate::{preludes::*, prisma};
use anyhow::{bail, Context};
use app_message::*;
use common::reset_traffic_task::{ArcResetTrafficContext, Update2DBTask};
use common::{
    chrono::{DateTime, Utc},
    tokio_util::sync::CancellationToken,
};
use futures_util::future::BoxFuture;
use futures_util::stream::SplitStream;
use futures_util::{SinkExt, StreamExt};
use k256::ecdh::SharedSecret;
use std::convert::Infallible;
use tokio::sync::{mpsc, oneshot};
use warp::ws::{Message, WebSocket};
use warp::{hyper::StatusCode, *};

pub async fn start_app(
    ctx: WrappedAppContext,
    reset_traffic_ctx: ArcResetTrafficContext,
) -> Result<()> {
    let ctx_move = ctx.clone();
    let traffic_retry_queue = reset_traffic_ctx.traffic_retry_queue.clone();
    let _handler_update = tokio::spawn(async move {
        loop {
            traffic_retry_queue.send(Box::new(Update2DBTask::new(reset_traffic_ctx.clone())));
            tokio::time::sleep(std::time::Duration::from_secs(60)).await;
        }
    });
    let redis = ctx_move.redis.clone();
    let (online_evt_tx, mut online_evt_rx) = tokio::sync::mpsc::channel(10);
    let _h = tokio::spawn(async move {
        while let Some(evt) = online_evt_rx.recv().await {
            let (id, st) = match evt {
                Event::Offline(id) => (id, 0),
                Event::Online(id) => (id, 1),
            };
            let _ = async {
                redis
                    .get()
                    .await?
                    .set::<_, _, ()>(format!("server:status:{}", id), st.to_string())
                    .await?;
                Ok::<_, anyhow::Error>(())
            }
            .await;
        }
        Ok::<_, anyhow::Error>(())
    });
    let online_tracker = Arc::new(OnlineTracker::new(online_evt_tx));
    let route_query = path("q")
        .and(post())
        .and(header("zfc-pubkey"))
        .and(header("zfc-ts"))
        .and(header("zfc-sign"))
        .and(body::bytes())
        .and_then(
            move |pubkey_str, ts, sign_buf_str, body_buf: hyper::body::Bytes| {
                let ctx_move = ctx_move.clone();
                async move {
                    handle_query(
                        ctx_move.clone(),
                        pubkey_str,
                        ts,
                        sign_buf_str,
                        body_buf.into(),
                    )
                    .await
                }
            },
        );

    let ctx_move = ctx.clone();

    let route_ws = path("w")
        .and(header("zfc-pubkey"))
        .and(header("zfc-ts"))
        .and(header("zfc-sign"))
        .and(ws())
        .map(move |pubkey_str, ts, sign_buf_str, ws: ws::Ws| {
            let ctx_move = ctx_move.clone();
            let online_tracker = online_tracker.clone();
            ws.on_upgrade(move |socket| {
                handle_websocket(
                    ctx_move,
                    socket,
                    pubkey_str,
                    ts,
                    sign_buf_str,
                    online_tracker,
                )
            })
        });

    let route_root = path::end().map(|| "🌚".to_string());
    let ctx_move = ctx.clone();
    let query_latency = path("test_latency")
        .and(header("zfc-pubkey"))
        .and(post())
        .and(body::json())
        .and_then(move |pubkey_str, body: WorkerTestLatencyRequest| {
            let cloned = ctx_move.clone();
            async move { test_latency(cloned, pubkey_str, body).await }
        });
    let ctx_move = ctx.clone();
    let check_user_filter =
        warp::header::<String>("worker-pubkey").and_then(move |pub_key: String| {
            let ctx_move = ctx_move.clone();
            async move {
                if ctx_move.black_worker_list.lock().await.contains(&pub_key) {
                    return Err(warp::reject::not_found());
                }
                let worker_info = ctx_move
                    .db
                    .outbound_endpoint()
                    .find_unique(outbound_endpoint::pubkey::equals(pub_key.clone()))
                    .select(prisma::outbound_endpoint::select!({ id }))
                    .exec()
                    .await
                    .map_err(|_| warp::reject::not_found())?;
                if worker_info.is_some() {
                    // 校验通过，进入下一个阶段
                    Ok(pub_key)
                } else {
                    // 校验失败，抛出自定义拒绝
                    error!("worker:{} not found", pub_key);
                    Err(warp::reject::not_found())
                }
            }
        });

    let hash_val = calculate_hash(
        ctx.opt.worker_version.clone(),
        tokio::fs::read(ctx.opt.worker_binary_path.clone())
            .await?
            .as_ref(),
    )
    .await?;
    let ctx_move = ctx.clone();
    let version_route = warp::path!("api" / "worker" / "version")
        .and(warp::get())
        .and(check_user_filter.clone())
        .and_then(move |_worker_pubkey: String| {
            let ctx_move = ctx_move.clone();
            let hash_val = hash_val.clone();
            async move { handle_worker_version(ctx_move, hash_val).await }
        });

    let download_route = warp::path!("api" / "worker" / "download")
        .and(warp::get())
        .and(check_user_filter.clone())
        .and(warp::fs::file(ctx.opt.worker_binary_path.clone()))
        .map(|_, f| f);

    let ctx_move = ctx.clone();
    let version_report = path!("api" / "worker" / "version_report")
        .and(warp::post())
        .and(check_user_filter.clone())
        .and(body::json())
        .and_then(move |pubkey_str, body: WorkerVersionReportRequest| {
            let ctx_move = ctx_move.clone();
            async move { handle_version_report(ctx_move, pubkey_str, body).await }
        });

    let ctx_move = ctx.clone();
    let fetch_global_latest_config = path!("api" / "worker" / "fetch_global_latest_config")
        .and(warp::get())
        .and(check_user_filter.clone())
        .and_then(move |pubkey_str| {
            let ctx_move = ctx_move.clone();
            async move { handle_worker_fetch_global_latest_config(ctx_move, pubkey_str).await }
        });
    let routes = any()
        .and(
            route_root
                .or(route_query)
                .or(route_ws)
                .or(query_latency)
                .or(version_route)
                .or(download_route)
                .or(version_report)
                .or(fetch_global_latest_config),
        )
        .recover(handle_rejection)
        .with(cors().allow_any_origin())
        .with(warp::log("zfc-arranger"));

    let port = ctx.opt.mgmt_port;
    info!("Listening on 0.0.0.0:{}", port);
    serve(routes).run(([0, 0, 0, 0], port)).await;

    Ok(())
}
prisma::outbound_endpoint::select!(
    WorkerInfo {
        id
        pubkey
    }
);
async fn create_worker_session(
    ctx: WrappedAppContext,
    pubkey_str: &String,
    time: DateTime<Utc>,
    sign_buf_str: String,
) -> Result<Arc<WorkerSession>> {
    let remote_pubkey =
        parse_public_key(pubkey_str).context(format!("worker:{} Invalid pubkey", pubkey_str))?;
    let message = "WorkerStartSession".as_bytes().into();
    let singature =
        hex::decode(sign_buf_str).context(format!("worker:{} Invalid sign", pubkey_str))?;
    if !check_signature(remote_pubkey, message, time, singature, false)? {
        bail!("worker:{} Invalid signature!", pubkey_str);
    }

    let worker_info = if let Some(worker_info) = ctx
        .db
        .outbound_endpoint()
        .find_unique(outbound_endpoint::pubkey::equals(pubkey_str.clone()))
        .select(WorkerInfo::select())
        .exec()
        .await?
    {
        worker_info
    } else {
        ctx.black_worker_list
            .lock()
            .await
            .insert(pubkey_str.clone());
        bail!("worker:{} Unknown worker, rejecting!", pubkey_str);
    };
    let session = WorkerSession::create(worker_info.clone(), ctx.clone());
    Ok(session)
}

async fn do_handle_websocket(
    tx: mpsc::UnboundedSender<AppWsResponse>,
    mut rx: SplitStream<WebSocket>,
    pubkey_str: String,
    q_tx: mpsc::UnboundedSender<FutureQueueItem>,
    online_tracker: Arc<OnlineTracker>,
    session: Arc<WorkerSession>,
    secret: SharedSecret,
) -> Result<()> {
    let worker_id = session.worker_info.id;

    while let Some(msg) = rx.next().await {
        let msg = msg?;
        let msg = msg.as_bytes();
        let msg: AppWsRequest = rmp_serde::from_slice(msg).context(format!(
            "worker:{} do_handle_websocket: rmp_serde error",
            pubkey_str
        ))?;

        let mut outer_msg = Some(msg);
        while let Some(msg) = outer_msg {
            outer_msg = None;
            match msg {
                AppWsRequest::Encrypted(enc_msg) => {
                    let msg = enc_msg.decrypt(&secret)?;
                    let msg = rmp_serde::from_slice(msg.as_slice()).context(format!(
                        "worker:{} do_handle_websocket: rmp_serde error",
                        pubkey_str
                    ))?;
                    outer_msg = Some(msg);
                }
                _ => {
                    session
                        .clone()
                        .handle_ws_message(
                            tx.clone(),
                            msg,
                            q_tx.clone(),
                            online_tracker.clone(),
                            worker_id as usize,
                        )
                        .await
                        .context(format!(
                            "worker:{} do_handle_websocket: handle_ws_message error",
                            pubkey_str
                        ))?;
                }
            }
        }
    }
    info!("worker:{} do_handle_websocket exited", pubkey_str);
    Ok(())
}

pub type FutureQueueItem = (
    Option<oneshot::Sender<Result<()>>>,
    BoxFuture<'static, Result<()>>,
);

async fn handle_websocket(
    ctx: WrappedAppContext,
    socket: ws::WebSocket,
    pubkey_str: String,
    ts: DateTime<Utc>,
    sign_buf_str: String,
    online_tracker: Arc<OnlineTracker>,
) {
    if ctx.black_worker_list.lock().await.contains(&pubkey_str) {
        return;
    }
    let Ok(remote_pubkey) = parse_public_key(&pubkey_str) else {
        error!("worker:{} Invalid pubkey", pubkey_str);
        return;
    };

    let Ok(secret) = get_shared_key(&ctx.mgmt_priv_key, &remote_pubkey) else {
        error!("worker:{} get shared key failed", pubkey_str);
        return;
    };

    let cancel_token = CancellationToken::new();

    let (mut ws_tx, ws_rx) = socket.split();
    let (inner_tx, mut inner_rx) = mpsc::unbounded_channel::<AppWsResponse>();

    let (q_tx, mut q_rx) = mpsc::unbounded_channel::<FutureQueueItem>();
    let cancel_token_clone = cancel_token.clone();
    let inner_tx_clone = inner_tx.clone();
    let pubkey_str_clone = pubkey_str.clone();
    let handle_for_ping = tokio::spawn(async move {
        loop {
            if cancel_token_clone.is_cancelled() {
                info!(
                    "worker:{} handle_for_ping exited by cancel token",
                    pubkey_str_clone
                );
                break;
            }
            let _ = inner_tx_clone.send(AppWsResponse::Ping);
            tokio::time::sleep(std::time::Duration::from_secs(10)).await;
        }
        info!("worker:{} handle_for_ping exited", pubkey_str_clone);
    });
    let cancel_token_move = cancel_token.clone();
    let pubkey_str_move = pubkey_str.clone();

    let q_rx_handle = tokio::spawn(async move {
        while let Some((shot, f)) = q_rx.recv().await {
            if cancel_token_move.is_cancelled() {
                info!(
                    "worker:{} q_rx_handle exited by cancel token",
                    pubkey_str_move
                );
                break;
            }
            let ret = f.await;
            if let Some(shot) = shot {
                let _ = shot.send(ret);
            }
        }
        info!("worker:{} q_rx_handle exited", pubkey_str_move);
    });

    let pubkey_str_tx = pubkey_str.clone();
    let secret_move = clone_shared_secret(&secret);
    let ws_tx_handle = tokio::spawn(async move {
        while let Some(m) = inner_rx.recv().await {
            match encode_rx_message(&m, &secret_move) {
                Ok(msg) => {
                    if let Err(e) = ws_tx.send(Message::binary(msg.as_slice())).await {
                        error!("worker:{} ws_tx_handle send error: {}", pubkey_str_tx, e);
                        break;
                    }
                }
                Err(e) => {
                    error!("ws_tx_handle: {}", e);
                }
            }
        }
    });
    info!("worker:{} begin wait session_rx", pubkey_str);
    let session = match create_worker_session(ctx.clone(), &pubkey_str, ts, sign_buf_str).await {
        Ok(session) => session,
        Err(e) => {
            error!("worker:{} create_worker_session error: {}", pubkey_str, e);
            ws_tx_handle.abort();
            cancel_token.cancel();
            return;
        }
    };
    let (cmd_tx, mut cmd_rx) = mpsc::channel::<WorkerCommand>(100);
    let inner_tx_clone = inner_tx.clone();
    let session_clone = session.clone();
    let ctx_clone = ctx.clone();
    let pubkey_str_clone = pubkey_str.clone();
    // start command process
    let cmd_process_handle = tokio::spawn(async move {
        while let Some(cmd) = cmd_rx.recv().await {
            match cmd {
                WorkerCommand::TestLatency(cmd) => {
                    let request_id = ctx_clone
                        .request_id
                        .fetch_add(1, std::sync::atomic::Ordering::AcqRel);
                    let Ok(_) = inner_tx_clone.send(AppWsResponse::TestLatencyReq {
                        request_id,
                        fwd_configs: cmd.command.fwd_configs.clone(),
                        info: cmd.command.info.clone(),
                    }) else {
                        error!(
                            "worker:{} send test latency req:{} failed",
                            pubkey_str_clone, request_id
                        );
                        continue;
                    };
                    session_clone
                        .pending_latency_test
                        .lock()
                        .await
                        .insert(request_id, cmd.rst_tx);
                }
            }
        }
        info!("worker:{} cmd_process exited", pubkey_str_clone);
    });

    ctx.worker_command_tx
        .lock()
        .await
        .insert(session.worker_info.id, cmd_tx);
    // update config map before session_loop and do_handle_websocket so the map will not be empty
    match session.update_config_map().await {
        Ok(_) => {}
        Err(e) => {
            error!(
                "worker:{} do_handle_websocket: update_config_map error: {}",
                pubkey_str, e
            );
            ws_tx_handle.abort();
            cancel_token.cancel();
            ctx.worker_command_tx
                .lock()
                .await
                .remove(&session.worker_info.id);
            return;
        }
    }
    info!("worker:{} end wait session_rx", pubkey_str);
    let session_handle = tokio::spawn(session.clone().session_loop(cancel_token.clone()));

    let ws_rx_handle = tokio::spawn(do_handle_websocket(
        inner_tx.clone(),
        ws_rx,
        pubkey_str.clone(),
        q_tx,
        online_tracker,
        session.clone(),
        secret,
    ));
    tokio::select! {
        ret = session_handle => {
            info!("worker:{} session_handle exited: {:?}", pubkey_str, ret);
        }
        ret = ws_rx_handle => {
            info!("worker:{} ws_rx_handle exited: {:?}", pubkey_str, ret);
            if let Some(e) = match ret {
                Ok(e) => {
                    if let Err(e) = e {
                        error!("worker:{} handle_websocket(inner): {:?}", pubkey_str, &e);
                        Some(format!("{:?}", e))
                    } else {
                        info!("worker:{} handle_websocket(inner) return Ok", pubkey_str);
                        None
                    }
                }
                Err(e) => {
                    error!("worker:{} handle_websocket(outer): {:?}", pubkey_str, &e);
                    Some(format!("{:?}", e))
                }
            } {
                let m = AppWsResponse::Error(e.to_string());
                let _ = inner_tx.send(m);
            }
        }
        ret = ws_tx_handle => {
            info!("worker:{} ws_tx_handle exited: {:?}", pubkey_str, ret);
        }
        ret = q_rx_handle => {
            info!("worker:{} q_rx_handle exited: {:?}", pubkey_str, ret);
        }
        ret = handle_for_ping => {
            info!("worker:{} handle_for_ping exited: {:?}", pubkey_str, ret);
        }
        ret = cmd_process_handle => {
            info!("worker:{} cmd_process_handle exited: {:?}", pubkey_str, ret);
        }
    }
    ctx.worker_command_tx
        .lock()
        .await
        .remove(&session.worker_info.id);
    cancel_token.cancel();
}

async fn do_handle_query(
    ctx: WrappedAppContext,
    _pubkey_str: String,
    _ts: DateTime<Utc>,
    _sign_buf_str: String,
    _body_buf: Vec<u8>,
) -> Result<(Vec<u8>, StatusCode)> {
    // todo
    Ok((ctx.opt.mgmt_priv_key.to_string().into(), StatusCode::OK))
}

async fn handle_query(
    ctx: WrappedAppContext,
    pubkey_str: String,
    ts: DateTime<Utc>,
    sign_buf_str: String,
    body_buf: Vec<u8>,
) -> std::result::Result<impl Reply, Infallible> {
    match do_handle_query(ctx, pubkey_str, ts, sign_buf_str, body_buf).await {
        Ok((ret_body, status_code)) => Ok(reply::with_status(ret_body, status_code)),
        Err(e) => {
            warn!("do_handle_query: {}", &e);
            let e = AppError::PlainError(e.to_string());
            Ok(reply::with_status(
                rmp_serde::encode::to_vec(&e).unwrap(),
                http::StatusCode::INTERNAL_SERVER_ERROR,
            ))
        }
    }
}

async fn handle_rejection(e: Rejection) -> Result<impl Reply, Infallible> {
    warn!("handle_rejection: {:?}", &e);
    let e = AppError::PlainError(format!("{:?}", &e));
    Ok(reply::with_status(
        rmp_serde::encode::to_vec(&e).unwrap(),
        StatusCode::BAD_REQUEST,
    ))
}
async fn handle_worker_version(
    ctx: WrappedAppContext,
    hash: String,
) -> Result<impl Reply, warp::Rejection> {
    let version_info = WorkerVersionInfo {
        version: ctx.opt.worker_version.clone(),
        download_url: Some(format!(
            "{}/api/worker/download",
            ctx.opt.arranger_hosts_url
        )),
        hash: Some(hash),
    };
    Ok(warp::reply::json(&version_info))
}

async fn handle_version_report(
    ctx: WrappedAppContext,
    pubkey_str: String,
    body: WorkerVersionReportRequest,
) -> Result<impl Reply, warp::Rejection> {
    ctx.db
        .outbound_endpoint()
        .update(
            prisma::outbound_endpoint::pubkey::equals(pubkey_str),
            vec![prisma::outbound_endpoint::version::set(Some(body.version))],
        )
        .exec()
        .await
        .map_err(|e| {
            error!("handle_version_report: {}", e);
            warp::reject()
        })?;
    Ok(warp::reply::html("ok"))
}

async fn handle_worker_fetch_global_latest_config(
    ctx: WrappedAppContext,
    pubkey_str: String,
) -> Result<impl Reply, warp::Rejection> {
    let server = ctx
        .db
        .outbound_endpoint()
        .find_unique(prisma::outbound_endpoint::pubkey::equals(
            pubkey_str.clone(),
        ))
        .select(prisma::outbound_endpoint::select!({
            proxy_config
            interface_name
            use_forward_as_tun
        }
        ))
        .exec()
        .await
        .map_err(|_e| warp::reject())?
        .ok_or(warp::reject::not_found())?;
    let proxy_config = server.proxy_config;
    let interface_name = server.interface_name.unwrap_or_default();
    let response = WorkerFetchGlobalLatestConfigResponse {
        proxy_config,
        interface_name,
        use_forward_as_tun: server.use_forward_as_tun,
    };
    info!(
        "worker: {} get latest global config: {:?}",
        pubkey_str, &response
    );
    Ok(warp::reply::json(&response))
}
