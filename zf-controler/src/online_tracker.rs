use std::{collections::HashMap, sync::Arc};
use log::debug;
use tokio::select;
use tokio::sync::Mutex;
use tokio::task::<PERSON><PERSON><PERSON><PERSON><PERSON>;
use common::tokio_util::sync::CancellationToken;
pub struct OnlineTracker {
    // evt_tx: tokio::sync::mpsc::Sender<Event>,
    states: Arc<Mutex<HashMap<usize, std::time::Instant>>>,
    task: <PERSON><PERSON><PERSON><PERSON><PERSON><()>,
    cancel: CancellationToken,
}
pub enum Event {
    Online(usize),
    Offline(usize),
}
impl Drop for OnlineTracker {
    fn drop(&mut self) {
        self.cancel.cancel();
        self.task.abort();
    }
}
impl OnlineTracker {
    pub fn new(evt_tx: tokio::sync::mpsc::Sender<Event>) -> Self {
        let states = Arc::new(Mutex::new(HashMap::<usize, std::time::Instant>::new()));
        let states_clone = states.clone();
        let cancel = common::tokio_util::sync::CancellationToken::new();
        let cancel_clone = cancel.clone();
        let task = tokio::spawn(async move {
            loop {
                select! {
                    _ = cancel_clone.cancelled() => {
                        log::warn!("online tracker is cancelled");
                        break;
                    }
                    _ = tokio::time::sleep(std::time::Duration::from_secs(30)) => {
                        let mut all_evt = vec![];
                        {
                            let l = states_clone.lock().await;
                            for (id, last_online) in l.iter() {
                                if last_online.elapsed() > std::time::Duration::from_secs(30) {
                                    debug!("server: {id} is offline");
                                    all_evt.push(Event::Offline(*id));
                                } else {
                                    debug!("server: {id} is online");
                                    all_evt.push(Event::Online(*id));

                                }
                            }
                        }
                        for e in all_evt {
                            let _ = evt_tx.send(e).await;
                        }

                    }
                }
            }
        });
        Self {
            states,
            task,
            cancel,
        }
    }
    pub async fn tick(&self, id: usize) {
        *self
            .states
            .lock()
            .await
            .entry(id)
            .or_insert_with(|| std::time::Instant::now()) = std::time::Instant::now();
    }
}
