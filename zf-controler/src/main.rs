use std::{
    collections::{HashMap, HashSet},
    sync::atomic::AtomicU64,
};

use anyhow::Result;
use cache::Cache;
use clap::Parser;
use common::{
    dotenv::dotenv, prisma, redis_lock::DistributedLock, reset_traffic_task::ResetTrafficContext,
};
use futures_util::future::BoxFuture;
use preludes::*;
use reset_traffic_task::reset_traffic_background;
use retry_queue::RetryQueue;
use tokio::{signal, sync::Mutex};

pub mod app;
pub mod error;
pub mod latency;
pub mod online_tracker;
pub mod preludes;
mod stats;
pub mod worker;

#[tokio::main]
async fn main() -> Result<()> {
    dotenv().ok();
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info")).init();
    let opt = Opt::parse();

    let mgmt_priv_key = parse_secret_key(&opt.mgmt_priv_key)?;

    let db = Arc::new(prisma::new_client_with_url(&opt.db_path).await?);
    let redis = setup_redis(&opt).await?;
    let influxdb = setup_influxdb(&opt).await?;
    let db_clone = db.clone();
    let db_clone1 = db.clone();
    let redis_lock = Arc::new(DistributedLock::new(redis.clone(), "traffic_lock", 5000));
    let ctx =
        Arc::new(AppContext {
            opt,
            db,
            redis,
            influxdb,
            mgmt_priv_key,
            black_worker_list: Arc::new(Mutex::new(HashSet::new())),
            worker_command_tx: Arc::new(Mutex::new(HashMap::new())),
            request_id: Arc::new(AtomicU64::new(0)),
            traffic_retry_queue: Arc::new(RetryQueue::new()),
            // forward_traffic_retry_queue: Arc::new(RetryQueue::new()),
            redis_lock,
            redis_line_lock: Arc::new(Mutex::new(HashMap::new())),
            cached_endpoint_list: Arc::new(
                Cache::new(
                    Box::new(
                        move |end_point_id: &i32| -> BoxFuture<
                            'static,
                            Result<outbound_endpoint::Data, anyhow::Error>,
                        > {
                            let db_clone1 = db_clone.clone();
                            let end_point_id_clone = end_point_id.clone();
                            Box::pin(async move {
                                let end_point_data = db_clone1
                                    .outbound_endpoint()
                                    .find_unique(outbound_endpoint::id::equals(end_point_id_clone))
                                    .exec()
                                    .await?
                                    .ok_or_else(|| anyhow::anyhow!("endpoint not found"))?;
                                Ok(end_point_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<outbound_endpoint::Data, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                ),
            ),
            cached_forward_endpoint_list: Arc::new(
                Cache::new(
                    Box::new(
                        move |id: &i32| -> BoxFuture<
                            'static,
                            Result<forward_endpoint::Data, anyhow::Error>,
                        > {
                            let db_clone2 = db_clone1.clone();
                            let id_clone = id.clone();
                            Box::pin(async move {
                                let forward_endpoint_data = db_clone2
                                    .forward_endpoint()
                                    .find_unique(forward_endpoint::id::equals(id_clone))
                                    .exec()
                                    .await?
                                    .ok_or_else(|| anyhow::anyhow!("forward endpoint not found"))?;
                                Ok(forward_endpoint_data)
                            })
                        },
                    )
                        as Box<
                            dyn Fn(
                                    &i32,
                                ) -> BoxFuture<
                                    'static,
                                    Result<forward_endpoint::Data, anyhow::Error>,
                                > + Send
                                + Sync
                                + 'static,
                        >,
                    std::time::Duration::from_secs(30),
                ),
            ),
        });
    let reset_traffic_ctx = Arc::new(ResetTrafficContext::new(
        ctx.db.clone(),
        ctx.redis.clone(),
        ctx.traffic_retry_queue.clone(),
        ctx.redis_lock.clone(),
        ctx.redis_line_lock.clone(),
    ));
    tokio::spawn(reset_traffic_background(reset_traffic_ctx.clone()));
    let app_handle = tokio::spawn(app::start_app(ctx.clone(), reset_traffic_ctx.clone()));

    tokio::select! {
        ret = app_handle => {
            match ret {
                Ok(ret) => {
                    if let Err(e) = ret {
                        error!("app_handle: {:?}", e)
                    }
                }
                Err(e) => error!("app_handle: {:?}", e)
            }
        }
        ret = signal::ctrl_c() => {
            if let Err(e) = ret {
                error!("ctrl_c: {e}")
            }
        }
    }
    info!("Exiting...");
    Ok(())
}
