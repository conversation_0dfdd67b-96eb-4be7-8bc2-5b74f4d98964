use std::{
    net::{IpAddr, SocketAddr},
    sync::Arc,
    time::Duration,
};

use anyhow::{anyhow, bail};
use common::{
    app_message::{LatencyTestMethod, TestLatencyInfo, TestLatencyType},
    constant::TCP_CONNECT_TIMEOUT,
    into_socket_addr,
};
use futures::future::try_join_all;
use private_tun::{
    address::Address,
    snell_impl_ver::{client_zfc::ConnType, TestLatencyMethod},
};
use tokio::net::TcpStream;

use crate::{
    backend_transport::BackendTransport,
    protocol::hammer::{start_hammer_client, HammerClient, HammerHandlerForWorkerTun},
    WorkerAppContext,
};

pub async fn direct_ping_test(ctx: Arc<WorkerAppContext>, ip_addr: IpAddr) -> anyhow::Result<u64> {
    let mut handles = Vec::with_capacity(3);
    let mut config = surge_ping::Config::default();
    if ip_addr.is_ipv4() {
        config.kind = surge_ping::ICMP::V4;
    } else {
        config.kind = surge_ping::ICMP::V6;
    }
    let client = if ip_addr.is_ipv4() {
        ctx.ping_client.v4_client()
    } else {
        ctx.ping_client.v6_client()
    };
    for i in 0..3 {
        let ip_addr = ip_addr.clone();
        let client_clone = client.clone();
        handles.push(tokio::spawn(async move {
            let mut pingger = client_clone
                .pinger(ip_addr, surge_ping::PingIdentifier(i))
                .await;
            pingger.ping(surge_ping::PingSequence(i), &[0; 64]).await
        }));
    }

    let mut results = Vec::with_capacity(3);
    for handle in handles {
        let result = handle.await??;
        results.push(result.1.as_micros() as u64);
    }
    let rtt = results.iter().sum::<u64>() / 3;
    Ok(rtt)
}

pub async fn hammer_backend_latency_test(
    fwd_client: &HammerHandlerForWorkerTun,
    ip_addr: IpAddr,
    port: Option<u16>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let best_latency_server = fwd_client.get_best_latency_server().await?;
    let now = std::time::Instant::now();
    let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
    fwd_client
        .client()
        .lock()
        .await
        .push_evt(ConnType::TestLatency {
            rst_tx: rst_tx,
            target: Address::Socket((ip_addr, port.unwrap_or(80)).into()),
            server_name: best_latency_server.clone(),
            method: into_test_latency_method(method),
            piped_stream: None,
            reuse_tcp: true,
        })
        .await
        .map_err(|_| anyhow!("hammer_backend_latency_test push error"))?;
    let latency = rst_rx.await??;
    log::info!(
        "hammer_backend_latency_test elasped latency: {:?} remote result: {} micros sec backend_server: {best_latency_server} ip: {ip_addr}:{port:?} method: {method:?}",
        now.elapsed(),
        latency
    );
    Ok(latency as u64)
}

pub async fn direct_tcpping_test(
    out_ip_addr: &Option<IpAddr>,
    ip_addr: IpAddr,
    port: u16,
) -> anyhow::Result<u64> {
    let now = std::time::Instant::now();
    let target_addr = (ip_addr, port).into();
    let stream = if let Some(out_ip_addr) = out_ip_addr {
        log::info!(
            "direct_tcpping_test with out_ip_addr: {out_ip_addr} ip_addr: {ip_addr} port: {port}"
        );
        match out_ip_addr {
            IpAddr::V4(_v4) => {
                let socket = tokio::net::TcpSocket::new_v4()?;
                socket.bind(SocketAddr::new(out_ip_addr.clone(), 0))?;
                tokio::time::timeout(
                    Duration::from_secs(TCP_CONNECT_TIMEOUT),
                    socket.connect(target_addr),
                )
                .await
            }
            IpAddr::V6(_v6) => {
                let socket = tokio::net::TcpSocket::new_v6()?;
                socket.bind(SocketAddr::new(out_ip_addr.clone(), 0))?;
                tokio::time::timeout(
                    Duration::from_secs(TCP_CONNECT_TIMEOUT),
                    socket.connect(target_addr),
                )
                .await
            }
        }
    } else {
        tokio::time::timeout(
            Duration::from_secs(TCP_CONNECT_TIMEOUT),
            TcpStream::connect(target_addr),
        )
        .await
    };
    // 尝试在5秒内建立连接
    match stream {
        Ok(connect_result) => {
            match connect_result {
                Ok(_stream) => {
                    // 连接成功，返回延迟时间
                    Ok(now.elapsed().as_micros() as u64)
                }
                Err(e) => {
                    // 连接失败，返回错误
                    Err(anyhow!("test latency for: {ip_addr}:{port} failed: {e}"))
                }
            }
        }
        Err(_) => {
            // 超时未连接成功，返回超时错误
            Err(anyhow!(
                "test latency for: {ip_addr}:{port} failed: timeout"
            ))
        }
    }
}
fn into_test_latency_method(method: LatencyTestMethod) -> TestLatencyMethod {
    match method {
        LatencyTestMethod::Tcpping => private_tun::snell_impl_ver::TestLatencyMethod::Tcpping,
        LatencyTestMethod::Icmp => private_tun::snell_impl_ver::TestLatencyMethod::Icmp,
    }
}
pub async fn fwd_server_to_remote_test(
    ip_addr: IpAddr,
    port: Option<u16>,
    fwd_client: &HammerClient,
    fwd_server: Box<str>,
    method: LatencyTestMethod,
) -> anyhow::Result<u64> {
    if port.is_none() && method == LatencyTestMethod::Tcpping {
        anyhow::bail!("port is None");
    }
    let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
    fwd_client
        .push_evt(ConnType::TestLatency {
            rst_tx: rst_tx,
            target: Address::Socket((ip_addr, port.unwrap_or(80)).into()),
            server_name: Arc::new(fwd_server),
            method: into_test_latency_method(method),
            piped_stream: None,
            reuse_tcp: true,
        })
        .await
        .map_err(|_| anyhow!("fwd_server_to_remote_test push error"))?;
    let latency = rst_rx.await??;
    Ok(latency as u64)
}

pub async fn setup_fwd_server(
    ctx: Arc<WorkerAppContext>,
    fwd_configs: Vec<String>,
) -> anyhow::Result<Vec<HammerClient>> {
    if fwd_configs.len() == 0 {
        return Ok(vec![]);
    }
    let tasks = fwd_configs.iter().map(|fwd_config| async {
        let config = serde_json::from_str(fwd_config)?;
        let hammer_client = start_hammer_client(
            ctx.hammer_manager.ctx().rt_provider().clone(),
            config,
            "test_server".to_string(),
            false,
        )
        .await?;
        Ok::<HammerClient, anyhow::Error>(hammer_client)
    });
    let rst = try_join_all(tasks).await?;
    Ok(rst)
}

pub async fn test_latency_msg(
    ctx: Arc<WorkerAppContext>,
    info: &TestLatencyInfo,
    hammer_clients: &Vec<HammerClient>,
) -> Result<u64, anyhow::Error> {
    let latency = match &info.test_type {
        TestLatencyType::FwdServerToRemote { fwd_name, remote } => {
            let Some(client) = hammer_clients
                .iter()
                .find(|c| c.get_server_addr(&fwd_name).is_some())
            else {
                bail!("fwd_server not found: {}", fwd_name);
            };
            fwd_server_to_remote_test(
                remote.ip(),
                Some(remote.port()),
                client,
                fwd_name.to_string().into_boxed_str(),
                info.test_method,
            )
            .await
        }
        TestLatencyType::DirectRemote { remote } => match &ctx.backend_transport {
            Some(BackendTransport::Hammer(fwd)) => {
                hammer_backend_latency_test(fwd, remote.ip(), Some(remote.port()), info.test_method)
                    .await
            }
            _ => match info.test_method {
                LatencyTestMethod::Icmp => direct_ping_test(ctx, remote.ip()).await,
                LatencyTestMethod::Tcpping => {
                    direct_tcpping_test(&ctx.out_ip_addr, remote.ip(), remote.port()).await
                }
            },
        },

        TestLatencyType::ToFwdServer { fwd_name } => {
            let Some((fwd_server_addr, _client)) =
                hammer_clients
                    .iter()
                    .find_map(|c| match c.get_server_addr(&fwd_name) {
                        Some(addr) => Some((addr, c)),
                        None => None,
                    })
            else {
                bail!("fwd_server not found: {}", fwd_name);
            };
            let fwd_server_addr = into_socket_addr(&fwd_server_addr).await?;
            match info.test_method {
                LatencyTestMethod::Icmp => direct_ping_test(ctx, fwd_server_addr.ip()).await,
                LatencyTestMethod::Tcpping => {
                    direct_tcpping_test(
                        &ctx.out_ip_addr,
                        fwd_server_addr.ip(),
                        fwd_server_addr.port(),
                    )
                    .await
                }
            }
        }
    };
    Ok(latency?)
}
