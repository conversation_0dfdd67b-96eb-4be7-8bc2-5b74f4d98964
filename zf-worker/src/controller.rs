use crate::backend_transport::BackendTransport;
use crate::latency::{setup_fwd_server, test_latency_msg};
use crate::preludes::*;
use crate::protocol::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>};
use crate::server::listen_tcp;
use crate::server::listen_udp;
use crate::stats::StatsCollector;
use crate::types::TargetData;
use anyhow::Context;
use common::constant::TCP_CONNECT_TIMEOUT;
use common::{
    app_message::{sign_message, AppWsRequest},
    tokio_util::sync::CancellationToken,
};
use futures::stream::FuturesUnordered;
use futures_util::{future::join_all, stream::SplitStream, SinkExt, StreamExt};
use http::Uri;
use private_tun::snell_impl_ver::client_zfc::TokioRW;
use socket2::TcpKeepalive;
use socks5_impl::client;
use std::collections::HashMap;
use std::future::Future;
use std::net::SocketAddr;
use tokio::net::TcpStream;
use tokio::time::Instant;
use tokio::{join, select};
use tokio_websockets::{ClientBuilder, Message, WebSocketStream};

pub type WsClient = WebSocketStream<Box<dyn TokioRW>>;
use std::sync::Mutex as StdMutex;

async fn init_controller(
    ctx: &Arc<WorkerAppContext>,
    stats_collector: Option<Arc<StdMutex<StatsCollector>>>,
) -> Result<()> {
    let worker_idx = ctx.idx;
    log::debug!("worker: {} start init controller", worker_idx);
    let secret = get_shared_key(&ctx.local_privkey, &ctx.remote_pubkey)
        .context(format!("worker: {} get shared key failed", worker_idx))?;

    let ws_client = tokio::time::timeout(
        Duration::from_secs(TCP_CONNECT_TIMEOUT),
        setup_ws_conn(ctx.clone()),
    )
    .await
    .context(format!("worker: {} setup ws conn timeout", worker_idx))?
    .context(format!("worker: {} setup ws conn failed", worker_idx))?;
    let (mut ws_tx, ws_rx) = ws_client.split();
    let (inner_tx, mut inner_rx) = mpsc::unbounded_channel::<AppWsRequest>();

    let cancel_token = CancellationToken::new();

    let ws_rx_handle = tokio::spawn(handle_websocket_message(
        ctx.clone(),
        inner_tx.clone(),
        ws_rx,
        cancel_token.clone(),
    ));

    let secret_move = clone_shared_secret(&secret);
    let ct_move = cancel_token.clone();
    let ws_tx_handle = tokio::spawn(async move {
        while let Some(m) = inner_rx.recv().await {
            if ct_move.is_cancelled() {
                break;
            };
            match encode_tx_message(&m, &secret_move) {
                Ok(msg) => {
                    ws_tx.send(Message::binary(msg)).await?;
                }
                Err(e) => {
                    error!("worker: {} ws_tx_handle: {}", worker_idx, e);
                }
            }
        }
        log::error!("worker: {} ws_tx_handle exited", worker_idx);
        Ok::<_, anyhow::Error>(())
    });
    let inner_tx_clone = inner_tx.clone();
    let cancel_token_clone = cancel_token.clone();
    if let Some(stats_collector) = stats_collector {
        let interval = stats_collector
            .lock()
            .unwrap()
            .get_report_interval()
            .clone();
        let intf = ctx.iface.clone();
        tokio::spawn(async move {
            log::info!("worker: {} report stats started", worker_idx);
            while !cancel_token_clone.is_cancelled() {
                let collector = stats_collector.clone();
                // using blocking pool to collect stats
                let intf_clone = intf.clone();
                let data = match tokio::task::spawn_blocking(move || {
                    collector.lock().unwrap().collect_all(&intf_clone)
                })
                .await
                {
                    Ok(data) => data,
                    Err(e) => {
                        error!("worker: {} stats_report_handle error: {}", worker_idx, e);
                        break;
                    }
                };
                match data {
                    Ok(stats_report) => {
                        log::debug!("worker: {} stats_report: {:?}", worker_idx, stats_report);
                        let _ = inner_tx_clone.send(AppWsRequest::StatsReport(stats_report));
                    }
                    Err(e) => {
                        error!("worker: {} stats_report_handle: {}", worker_idx, e);
                        break;
                    }
                }
                sleep(interval).await;
            }
            log::info!("worker: {} report stats exited", worker_idx);
            Ok::<_, anyhow::Error>(())
        });
    }
    let main_handle = tokio::spawn(main_loop(
        ctx.clone(),
        inner_tx.clone(),
        cancel_token.clone(),
    ));
    log::info!(
        "worker: {} Connection to manager established successfully",
        worker_idx
    );
    tokio::select! {
        ret = main_handle => {
            log::info!("worker: {} main_handle exited ret: {ret:?}", worker_idx);
        }
        ret = ws_tx_handle => {
            log::info!("worker: {} ws_tx_handle exited ret: {ret:?}", worker_idx);
        }
        ret = ws_rx_handle => {
            log::info!("worker: {} ws_rx_handle exited ret: {ret:?}", worker_idx);
            match ret {
                Ok(r) => {
                    if let Err(e) = r {
                        error!("worker: {} handle_websocket: {}", worker_idx, e.to_string());
                        let m = AppWsRequest::Error(e.to_string());
                        let _ = inner_tx.send(m);
                    }
                }
                Err(e) => {
                    error!("worker: {} handle_websocket: {}", worker_idx, e.to_string());
                    let m = AppWsRequest::Error(e.to_string());
                    let _ = inner_tx.send(m);
                }
            }
        }
    }
    cancel_token.cancel();
    Ok(())
}

async fn main_loop(
    ctx: Arc<WorkerAppContext>,
    tx: mpsc::UnboundedSender<AppWsRequest>,
    cancel_token: CancellationToken,
) -> Result<()> {
    loop {
        tx.send(AppWsRequest::ShouldDumpPortMap)?;

        if cancel_token.is_cancelled() {
            log::info!("worker: {} main_loop exited by cancel token", ctx.idx);
            return Ok(());
        }

        sleep(Duration::from_secs(rand::random::<u64>() % 10 + 10)).await;
    }
}

fn report_data(ctx: Arc<WorkerAppContext>, tx: mpsc::UnboundedSender<AppWsRequest>) -> Result<()> {
    debug!("worker: {} Dumping data to report.", ctx.idx);

    // let config_map = ctx.port_config_map.clone();

    let mut report = vec![];

    for p in ctx.port_config_map.iter().map(|x| *x.key()) {
        let used = match ctx.port_traffic_map.get(&p) {
            Some(v) => *v,
            None => 0,
        };
        if used == 0 {
            continue;
        }
        log::debug!("worker: {} port: {p} used: {used}", ctx.idx);
        report.push(PortTrafficUsage {
            port: p,
            used: used.into(),
        });
    }
    if !report.is_empty() {
        let _ = tx.send(AppWsRequest::PortTrafficUsage(report));
    }
    ctx.port_traffic_map.clear();
    // report forward endpoint traffic
    if ctx.forward_endpoint_traffic_map.is_empty() {
        return Ok(());
    }
    let report = ctx
        .forward_endpoint_traffic_map
        .iter()
        .map(|x| StatData {
            port: x.key().0,
            forward_endpoint: x.key().1.to_string(),
            traffic: *x.value(),
        })
        .collect();
    ctx.forward_endpoint_traffic_map.clear();
    log::trace!(
        "worker: {} send forward endpoint traffic report: {:?}",
        ctx.idx,
        report
    );
    let _ = tx.send(AppWsRequest::ForwardEndpointTrafficReport(report));
    Ok(())
}

pub async fn start_controller(ctx: Arc<WorkerAppContext>) -> Result<()> {
    let mut last_error_time: Option<Instant> = None;
    let mut error_count = 0;
    loop {
        let _ = ctx.check_upgrade_tx.try_send(());
        if let Err(e) = init_controller(&ctx, ctx.stats.clone()).await {
            error!(
                "worker: {} Failed to connect to arranger, retrying in 10s...:\n{:?}",
                ctx.idx, e
            );
        } else {
            error!(
                "worker: {} Connection to arranger lost, retrying in 10s...",
                ctx.idx
            );
        }

        if let Some(last_error_time) = last_error_time {
            if last_error_time.elapsed() < Duration::from_secs(120) {
                error_count += 1;
            } else {
                error_count = 0;
            }
        }
        if error_count > 3 {
            error!("worker: {} Too many errors, exiting...", ctx.idx);
            tokio::time::sleep(Duration::from_secs(5)).await;
            let _ = ctx.global_exit_tx.send(()).await;
        }
        last_error_time = Some(Instant::now());
        log::info!("worker: {} sleep 10s", ctx.idx);
        sleep(Duration::from_secs(10)).await;
    }
}

async fn handle_websocket_message(
    ctx: Arc<WorkerAppContext>,
    tx: mpsc::UnboundedSender<AppWsRequest>,
    mut rx: SplitStream<WsClient>,
    cancel_token: CancellationToken,
) -> Result<()> {
    let worker_idx = ctx.idx;
    let secret = get_shared_key(&ctx.local_privkey, &ctx.remote_pubkey).context(format!(
        "worker: {} ws rx get shared key failed",
        worker_idx
    ))?;
    let mut manager_dead_timer = tokio::time::interval_at(
        tokio::time::Instant::now() + Duration::from_secs(180),
        Duration::from_secs(180),
    );
    loop {
        let msg = select! {
            msg = rx.next() => msg,
            _ = manager_dead_timer.tick() => {
                log::error!("worker: {} manager dead try exit", worker_idx);
                return Err(anyhow::anyhow!("worker: {} manager dead", worker_idx));
            }
            _ = cancel_token.cancelled() => {
                log::info!("worker: {} cancel token cancelled", worker_idx);
                return Ok(());
            }
        };

        if let Some(msg) = msg {
            let msg = msg.context(format!("worker: {} ws rx msg is error", worker_idx))?;
            let msg = msg.as_payload();
            let msg: AppWsResponse = rmp_serde::from_slice(msg)
                .context(format!("worker: {} ws rx decode rmp failed", worker_idx))?;

            let mut outer_msg = Some(msg);
            while let Some(msg) = outer_msg {
                outer_msg = None;
                match msg {
                    AppWsResponse::Encrypted(enc_msg) => {
                        let msg = enc_msg
                            .decrypt(&secret)
                            .context(format!("worker: {} ws rx decrypt msg failed", worker_idx))?;
                        let msg = rmp_serde::from_slice(msg.as_slice()).context(format!(
                            "worker: {} ws rx decode rmp inner failed",
                            worker_idx
                        ))?;
                        outer_msg = Some(msg);
                        manager_dead_timer.reset();
                    }
                    AppWsResponse::Error(e) => {
                        error!("worker: {} Arranger Error: {}", worker_idx, e);
                    }
                    AppWsResponse::Ping => {
                        log::debug!("worker: {} recved ping", worker_idx);
                        tx.send(AppWsRequest::Pong)
                            .context(format!("worker: {} ws rx send pong failed", worker_idx))?;
                    }
                    AppWsResponse::PortMapUpdate(map) => {
                        report_data(ctx.clone(), tx.clone())
                            .context(format!("worker: {} ws rx report data failed", worker_idx))?;
                        process_port_map(ctx.clone(), map).await;
                    }
                    AppWsResponse::ShouldReportPortTrafficUsage => {
                        report_data(ctx.clone(), tx.clone()).context(format!(
                            "worker: {} ws rx proc should report traffic usage failed",
                            worker_idx
                        ))?;
                    }
                    AppWsResponse::TestLatencyReq {
                        request_id,
                        fwd_configs,
                        info,
                    } => {
                        log::info!(
                            "worker: {} ws rx test latency request: {:?} req_id: {}",
                            worker_idx,
                            info,
                            request_id
                        );
                        tokio::spawn(proc_test_latency(
                            ctx.clone(),
                            request_id,
                            fwd_configs,
                            info,
                            tx.clone(),
                        ));
                    }
                }
            }
        } else {
            log::error!("worker: {} ws recved None", worker_idx);
            break;
        }
    }
    log::error!("worker: {} ws rx exited", worker_idx);
    Ok::<_, anyhow::Error>(())
}
fn should_rebuild_server(new_port_cfg: &PortConfig, old_port_cfg: &PortConfig) -> bool {
    new_port_cfg.subscription_id != old_port_cfg.subscription_id
        || new_port_cfg.target_addr_list != old_port_cfg.target_addr_list
        || new_port_cfg.bandwidth != old_port_cfg.bandwidth
        || new_port_cfg.mode != old_port_cfg.mode
        || new_port_cfg.latency_test_method != old_port_cfg.latency_test_method
        || new_port_cfg.allow_ip_num != old_port_cfg.allow_ip_num
        || new_port_cfg.allow_conn_num != old_port_cfg.allow_conn_num
}
async fn create_protocol_handler(
    ctx: Arc<WorkerAppContext>,
    port: u16,
    target_data: TargetData,
    port_cancel: CancellationToken,
) -> Result<Option<Arc<Box<dyn ProtocolHandler>>>> {
    if ctx.hammer_manager.is_my_port(port) {
        ctx.hammer_manager
            .create_protocol_handler(
                port,
                target_data,
                ctx.collector_factory.clone(),
                ctx.ping_client.clone(),
                port_cancel,
                ctx.io_runtime.clone(),
                ctx.conn_sem.clone(),
            )
            .await
    } else if ctx.tot_manager.is_my_port(port) {
        ctx.tot_manager
            .create_protocol_handler(
                port,
                target_data,
                ctx.collector_factory.clone(),
                ctx.ping_client.clone(),
                port_cancel,
                ctx.io_runtime.clone(),
                ctx.conn_sem.clone(),
            )
            .await
    } else {
        if let Some(BackendTransport::Hammer(hammer_handler)) = ctx.backend_transport.as_ref() {
            hammer_handler
                .create_handler(
                    target_data,
                    port,
                    ctx.collector_factory.clone(),
                    ctx.ping_client.clone(),
                    port_cancel,
                    ctx.io_runtime.clone(),
                    ctx.conn_sem.clone(),
                )
                .await
                .map(|h| Some(h))
        } else {
            Ok(None)
        }
    }
}
async fn create_port_update_task(
    ctx: Arc<WorkerAppContext>,
    remote_map: HashMap<u16, PortConfig>,
) -> FuturesUnordered<impl Future<Output = ()>> {
    let local_config_map = ctx.port_config_map.clone();
    log::debug!(
        "worker: {} process_port_map, remote ports: {:?}",
        ctx.idx,
        remote_map.keys()
    );

    let bind_addr = ctx.bind_addr.clone();
    let out_addr = ctx.out_ip_addr.clone();
    let hammer_process_task = ctx.hammer_manager.process_port_map(&remote_map);
    let tot_process_task = ctx.tot_manager.process_port_map(&remote_map);
    let (hammer_port_checker, tot_port_checker) = join!(hammer_process_task, tot_process_task);
    let mut port_checkers: Vec<PortChecker> = vec![];
    // current only support hammer
    if let Ok(port_checker) = hammer_port_checker {
        port_checkers.push(port_checker);
    } else {
        error!("worker: {} hammer manager process port map failed", ctx.idx);
    }
    if let Ok(port_checker) = tot_port_checker {
        port_checkers.push(port_checker);
    } else {
        error!("worker: {} tot manager process port map failed", ctx.idx);
    }
    let mut rmv_ports = vec![];
    // remove all unused port
    let mut have_stop = false;
    for x in local_config_map.iter() {
        let port = x.key();
        if let None = remote_map.get(port) {
            info!("worker: {} Port {} no more used", ctx.idx, port);
            x.value().cancel.cancel();
            x.value().tcp_listen_handle.abort();
            x.value().udp_listen_handle.abort();
            rmv_ports.push(*port);
            let _ = ctx.port_to_flow_map.remove(port);
            have_stop = true;
        }
    }
    if have_stop {
        // wait for stop
        tokio::time::sleep(Duration::from_millis(200)).await;
    }
    for p in rmv_ports.into_iter() {
        local_config_map.remove(&p);
    }
    let mut old_config_maps = HashMap::new();
    local_config_map.iter().for_each(|x| {
        old_config_maps.insert(*x.key(), x.value().clone());
    });
    let check_is_proxy_modified =
        |port: u16| port_checkers.iter().any(|x| x.is_port_modified(port));
    let f = remote_map
        .into_iter()
        .map(|(port, remote_config)| {
            let ctx = ctx.clone();
            let old_port_config = old_config_maps.get(&port);
            let create = if let Some(old_cfg) = old_port_config {
                // 修改检测
                if should_rebuild_server(&remote_config, &old_cfg.config)
                    || check_is_proxy_modified(port)
                {
                    true
                } else {
                    // 健康检查
                    let tcp_is_finished = old_cfg.tcp_listen_handle.is_finished();
                    let udp_is_finished = old_cfg.udp_listen_handle.is_finished();
                    if tcp_is_finished || udp_is_finished {
                        log::warn!(
                            "worker: {} port: {} healthy check failed, tcp_is_finished: {}, udp_is_finished: {}, rebuild tcp and udp",
                            ctx.idx,
                            port,
                            tcp_is_finished,
                            udp_is_finished
                        );
                        true
                    } else {
                        false
                    }
                }
            } else {
                // 新增
                info!("worker: {} add port: {port}", ctx.idx);
                true
            };
            let local_config_map_clone = local_config_map.clone();
            let backend = ctx.backend_transport.clone();
            let port_to_flow_map = ctx.port_to_flow_map.clone();
            let port_cancel = CancellationToken::new();
            let forward_handler_creator = if create {
                let target_data = TargetData {
                    addr_list: remote_config.target_addr_list.clone(),
                    mode: remote_config.mode,
                    latency_test_method: remote_config.latency_test_method,
                    sub_id: remote_config.subscription_id,
                    allow_ip_num: remote_config.allow_ip_num,
                    allow_conn_num: remote_config.allow_conn_num,
                };
                Some(create_protocol_handler(ctx.clone(), port, target_data, port_cancel.clone()))
            } else {
                None
            };
            let collector_factory = ctx.collector_factory.clone();
            #[cfg(feature = "audit_log")]
            let log_client = ctx.log_client.clone();
            let filters = ctx.traffic_filter.clone();
            let io_runtime = ctx.io_runtime.clone();
            async move {
                {
                    // update map if it's new map
                    if port_to_flow_map.get(&port, 0).is_err() {
                        let _ = port_to_flow_map.insert(port, port as u32, 0);
                    }
                }
                if !create {
                    return;
                }
                // 先尝试删除
                local_config_map_clone.remove(&port).map(|(_, x)| {
                    log::info!("worker: {} remove port: {port}", ctx.idx);
                    // stop tcp and udp server
                    x.cancel.cancel();
                    x.tcp_listen_handle.abort();
                    x.udp_listen_handle.abort();
                });
                let forward_handler = if let Some(forward_handler_creator) = forward_handler_creator
                {
                    // using forward handler
                    match forward_handler_creator.await {
                        Ok(Some(handler)) => Some(handler),
                        Ok(None) => None,
                        Err(e) => {
                            error!(
                                "worker: {} port: {} create forward handler failed: {}",
                                ctx.idx, port, e
                            );
                            None
                        }
                    }
                } else {
                    None
                };
                // #[cfg(target_os = "linux")]
                // if let Some(bw) = remote_config.bandwidth {
                //     use crate::platform::set_flow_bandwidth;
                //     let _ = set_flow_bandwidth(&ctx.tc_path, &ctx.iface, port as _, bw).await;
                // }
                let tcp_target_data = Arc::new(TargetData {
                    addr_list: remote_config.target_addr_list.clone(),
                    mode: remote_config.mode,
                    latency_test_method: remote_config.latency_test_method,
                    sub_id: remote_config.subscription_id,
                    allow_ip_num: remote_config.allow_ip_num,
                    allow_conn_num: remote_config.allow_conn_num,
                });
                info!(
                    "worker: {} start tcp server on port: {}  backend: {:?}",
                    ctx.idx, port, backend
                );
                let socks_backend = match backend.as_ref() {
                    Some(BackendTransport::Socks5(socks_backend)) => Some(socks_backend.clone()),
                    _ => None,
                };
                let bind_addr = bind_addr.map(|x| (x.ip(), port).into());
                let tcp_listen_handle = io_runtime.get_rt().spawn(listen_tcp(
                    port,
                    bind_addr.clone(),
                    tcp_target_data.clone(),
                    socks_backend.clone(),
                    forward_handler.clone(),
                    ctx.ping_client.clone(),
                    port_cancel.clone(),
                    collector_factory.clone(),
                    #[cfg(feature = "audit_log")]
                    log_client.clone(),
                    filters,
                    out_addr.clone(),
                    ctx.conn_sem.clone(),
                ));
                let udp_listen_handle = io_runtime.get_rt().spawn(listen_udp(
                    port,
                    bind_addr,
                    out_addr,
                    tcp_target_data,
                    socks_backend,
                    forward_handler.clone(),
                    ctx.ping_client.clone(),
                    port_cancel.clone(),
                    collector_factory,
                    #[cfg(feature = "audit_log")]
                    log_client,
                    ctx.conn_sem.clone(),
                ));
                local_config_map_clone.insert(
                    port,
                    Arc::new(LocalServer {
                        tcp_listen_handle,
                        udp_listen_handle,
                        config: remote_config,
                        cancel: port_cancel,
                    }));
            }
        });
    FuturesUnordered::from_iter(f)
}
async fn process_port_map(ctx: Arc<WorkerAppContext>, remote_map: HashMap<u16, PortConfig>) {
    let mut f = create_port_update_task(ctx, remote_map).await;
    // join without result
    while let Some(_) = f.next().await {}
}

fn setup_tcp_keepalive(stream: &TcpStream) {
    let socket_ref = socket2::SockRef::from(stream);
    #[cfg(target_os = "linux")]
    let _ = socket_ref.set_tcp_user_timeout(Some(std::time::Duration::from_secs(302)));
    let _ = socket_ref.set_tcp_keepalive(
        &TcpKeepalive::new()
            .with_time(std::time::Duration::from_secs(300))
            .with_interval(std::time::Duration::from_secs(2))
            .with_retries(1),
    );
}
async fn setup_ws_conn(ctx: Arc<WorkerAppContext>) -> Result<WsClient> {
    let message = "WorkerStartSession".as_bytes().into();
    let (signature, time) =
        sign_message(&ctx.local_privkey, message).context("sign message failed")?;
    let signature = hex::encode(&signature);
    let ts = time.to_rfc3339_opts(chrono::SecondsFormat::Millis, true);

    let local_pubkey = stringify_public_key_from_secret_key(&ctx.local_privkey);

    let mgmt_url: Uri = ctx.mgmt_ws_endpoint.as_str().parse()?;
    let host = mgmt_url.host().unwrap();
    let port = mgmt_url.port().unwrap().as_u16();
    let stream = match ctx.mgmt_socks_proxy.as_ref() {
        Some(socks_server) => {
            log::debug!("use socks server to arranger: {socks_server}");
            match socks_server.parse::<SocketAddr>() {
                Ok(addr) => {
                    let mut stream = TcpStream::connect(addr)
                        .await
                        .context("connect socks server failed")?;
                    let _ = stream.set_nodelay(true);
                    setup_tcp_keepalive(&mut stream);

                    let _addr = client::connect(&mut stream, (host.to_owned(), port), None)
                        .await
                        .context("socks handshake failed")?;
                    Box::new(stream) as Box<dyn TokioRW>
                }
                Err(_e) => {
                    log::warn!(
                        "socks server is not a valid address, try using hammer as ws transport"
                    );
                    if let Some(hammer_proxyer) = ctx.worker_proxyer.clone() {
                        log::info!("using hammer as ws transport");
                        let (dup_a, dup_b) = tokio::io::duplex(4096);
                        tokio::spawn(hammer_proxyer.proc_worker_proxy_stream(dup_b));
                        Box::new(dup_a) as Box<dyn TokioRW>
                    } else {
                        let stream = TcpStream::connect((host.to_owned(), port)).await?;
                        let _ = stream.set_nodelay(true);
                        setup_tcp_keepalive(&stream);
                        Box::new(stream) as Box<dyn TokioRW>
                    }
                }
            }
        }
        None => {
            if let Some(hammer_proxyer) = ctx.worker_proxyer.clone() {
                log::info!("using hammer as ws transport");
                let (dup_a, dup_b) = tokio::io::duplex(4096);
                tokio::spawn(hammer_proxyer.proc_worker_proxy_stream(dup_b));
                Box::new(dup_a) as Box<dyn TokioRW>
            } else {
                let stream = TcpStream::connect((host.to_owned(), port)).await?;
                let _ = stream.set_nodelay(true);
                setup_tcp_keepalive(&stream);
                Box::new(stream) as Box<dyn TokioRW>
            }
        }
    };
    log::info!("worker: {} begin setup websocket stream", ctx.idx);
    let client = ClientBuilder::from_uri(mgmt_url)
        .add_header("zfc-pubkey".parse()?, local_pubkey.parse()?)?
        .add_header("zfc-ts".parse()?, ts.parse()?)?
        .add_header("zfc-sign".parse()?, signature.parse()?)?
        .connect_on(stream)
        .await
        .context("setup websocket stream failed")?
        .0;
    log::info!("worker: {} setup websocket stream success", ctx.idx);
    Ok(client)
}

async fn proc_test_latency(
    ctx: Arc<WorkerAppContext>,
    request_id: u64,
    fwd_configs: Vec<String>,
    info: Vec<TestLatencyInfo>,
    tx: mpsc::UnboundedSender<AppWsRequest>,
) -> Result<()> {
    let clients = setup_fwd_server(ctx.clone(), fwd_configs).await?;
    let latencies = info
        .iter()
        .map(|x| async { test_latency_msg(ctx.clone(), x, &clients).await });
    let latencies_rst = join_all(latencies).await;
    let latencies = latencies_rst
        .iter()
        .map(|x| match &x {
            Ok(latency) => *latency,
            Err(e) => {
                error!("worker: {} test latency failed: {}", ctx.idx, e);
                65535000
            }
        })
        .collect();
    let _ = tx.send(AppWsRequest::TestLatencyResp {
        request_id,
        latencies,
    });
    Ok(())
}
