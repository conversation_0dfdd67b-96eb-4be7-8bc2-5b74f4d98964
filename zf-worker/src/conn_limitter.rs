use std::{
    collections::{hash_map::Entry, HashMap, HashSet},
    net::{IpAddr, SocketAddr},
    sync::Arc,
};

type IpMap = HashMap<IpAddr, HashSet<u16>>; // ip -> conn count

#[derive(Debug)]
struct LimiterInner {
    ip_map: Arc<std::sync::Mutex<IpMap>>,
}
pub struct ConnLimitter {
    allow_ip_num: Option<usize>,
    allow_conn_num: Option<usize>,
    inner: Arc<LimiterInner>,
}
#[derive(Debug)]
pub struct Guard {
    conn_limitter: Arc<LimiterInner>,
    socket_addr: SocketAddr,
}
impl Drop for Guard {
    fn drop(&mut self) {
        let limitter = self.conn_limitter.clone();
        let socket_addr = self.socket_addr;
        log::debug!("drop guard: {:?} current: {:?}", socket_addr, limitter);
        limitter.remove_conn(socket_addr);
    }
}

#[derive(Debug)]
pub enum ConnLimitResult {
    Ok(Guard),
    IpLimit {
        allow_ip_num: usize,
        current_ip_num: usize,
    },
    ConnLimit {
        allow_conn_num: usize,
        current_conn_num: usize,
    },
}

impl ConnLimitter {
    pub fn new(allow_ip_num: Option<usize>, allow_conn_num: Option<usize>) -> Self {
        Self {
            allow_ip_num,
            allow_conn_num,
            inner: Arc::new(LimiterInner {
                ip_map: Arc::new(std::sync::Mutex::new(HashMap::new())),
            }),
        }
    }
    pub fn add_conn(&self, socket_addr: SocketAddr) -> ConnLimitResult {
        self.inner
            .clone()
            .should_limit(socket_addr, &self.allow_ip_num, &self.allow_conn_num)
    }
}

impl LimiterInner {
    fn remove_conn(self: Arc<Self>, socket_addr: SocketAddr) {
        let mut ip_map = self.ip_map.lock().unwrap();
        let entry = ip_map.entry(socket_addr.ip());
        match entry {
            Entry::Occupied(mut entry) => {
                let conn_set = entry.get_mut();
                conn_set.remove(&socket_addr.port());
                if conn_set.is_empty() {
                    entry.remove();
                    log::debug!("remove ip from: {:?}", socket_addr.ip());
                } else {
                    log::debug!(
                        "remove ip conn from: {:?} cur conn num: {:?}",
                        socket_addr.ip(),
                        conn_set.len()
                    );
                }
            }
            Entry::Vacant(_) => {}
        }
    }

    fn should_limit(
        self: Arc<Self>,
        from_socket_addr: SocketAddr,
        allow_ip_num: &Option<usize>,
        allow_conn_num: &Option<usize>,
    ) -> ConnLimitResult {
        if let Some(allow_ip_num) = allow_ip_num {
            let ip_map = self.ip_map.lock().unwrap();
            let map_len = ip_map.len();
            match ip_map.get(&from_socket_addr.ip()) {
                Some(_) => {}
                None => {
                    if map_len >= *allow_ip_num as usize {
                        return ConnLimitResult::IpLimit {
                            allow_ip_num: *allow_ip_num,
                            current_ip_num: map_len,
                        };
                    }
                }
            }
        }

        if let Some(allow_conn_num) = allow_conn_num {
            let ip_map = self.ip_map.lock().unwrap();
            if let Some(conn_set) = ip_map.get(&from_socket_addr.ip()) {
                if conn_set.len() >= *allow_conn_num {
                    return ConnLimitResult::ConnLimit {
                        allow_conn_num: *allow_conn_num,
                        current_conn_num: conn_set.len(),
                    };
                }
            }
        }
        // update
        let mut ip_map = self.ip_map.lock().unwrap();
        let conn_set = ip_map
            .entry(from_socket_addr.ip())
            .or_insert(HashSet::new());
        conn_set.insert(from_socket_addr.port());
        ConnLimitResult::Ok(Guard {
            conn_limitter: self.clone(),
            socket_addr: from_socket_addr,
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{IpAddr, Ipv4Addr, SocketAddr};
    use tokio::sync::mpsc;

    #[tokio::test]
    async fn test_new_limiter() {
        let limiter = ConnLimitter::new(Some(10), Some(20));
        assert_eq!(limiter.allow_ip_num, Some(10));
        assert_eq!(limiter.allow_conn_num, Some(20));
    }

    #[tokio::test]
    async fn test_no_limits() {
        // Test with no limits set (None)
        let limiter = ConnLimitter::new(None, None);
        let addr1 = SocketAddr::new(IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1)), 8080);

        // Should always succeed without limits
        match limiter.add_conn(addr1) {
            ConnLimitResult::Ok(_guard) => {
                // Success as expected
            }
            _ => panic!("Expected OK result when no limits are set"),
        }
    }

    #[tokio::test]
    async fn test_ip_limit() {
        // Test IP limit with 2 IPs maximum
        let limiter = ConnLimitter::new(Some(2), None);

        let ip1 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let ip2 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2));
        let ip3 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 3));

        let addr1 = SocketAddr::new(ip1, 8080);
        let addr2 = SocketAddr::new(ip2, 8080);
        let addr3 = SocketAddr::new(ip3, 8080);

        // First two IPs should be accepted
        let guard1 = match limiter.add_conn(addr1) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept first IP"),
        };

        let guard2 = match limiter.add_conn(addr2) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept second IP"),
        };

        // Third IP should be rejected due to IP limit
        match limiter.add_conn(addr3) {
            ConnLimitResult::IpLimit {
                allow_ip_num,
                current_ip_num,
            } => {
                assert_eq!(allow_ip_num, 2);
                assert_eq!(current_ip_num, 2);
            }
            _ => panic!("Expected IP limit to be reached"),
        }

        // Drop one guard to free up an IP
        drop(guard1);

        // Need to wait a bit for the async drop to complete
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        // Now should be able to add the third IP
        match limiter.add_conn(addr3) {
            ConnLimitResult::Ok(_) => {}
            _ => panic!("Expected to be able to add third IP after dropping first"),
        }

        // Clean up
        drop(guard2);
    }

    #[tokio::test]
    async fn test_conn_limit() {
        // Test connection limit with 3 connections maximum
        let limiter = ConnLimitter::new(None, Some(3));

        let ip = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let addr1 = SocketAddr::new(ip, 8080);
        let addr2 = SocketAddr::new(ip, 8081);
        let addr3 = SocketAddr::new(ip, 8082);
        let addr4 = SocketAddr::new(ip, 8083);

        // First three connections should be accepted
        let guard1 = match limiter.add_conn(addr1) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept first connection"),
        };

        let guard2 = match limiter.add_conn(addr2) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept second connection"),
        };

        let guard3 = match limiter.add_conn(addr3) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept third connection"),
        };

        // Fourth connection should be rejected due to connection limit
        match limiter.add_conn(addr4) {
            ConnLimitResult::ConnLimit {
                allow_conn_num,
                current_conn_num,
            } => {
                assert_eq!(allow_conn_num, 3);
                assert_eq!(current_conn_num, 3);
            }
            _ => panic!("Expected connection limit to be reached"),
        }

        // Drop one guard to free up a connection
        drop(guard1);

        // Need to wait a bit for the async drop to complete
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        // Now should be able to add the fourth connection
        match limiter.add_conn(addr4) {
            ConnLimitResult::Ok(_) => {}
            _ => panic!("Expected to be able to add fourth connection after dropping first"),
        }

        // Clean up
        drop(guard2);
        drop(guard3);
    }

    #[tokio::test]
    async fn test_both_limits() {
        // Test both IP and connection limits
        let limiter = ConnLimitter::new(Some(2), Some(2));

        let ip1 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let ip2 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2));
        let ip3 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 3));

        let addr1a = SocketAddr::new(ip1, 8080);
        let addr1b = SocketAddr::new(ip1, 8081);
        let addr1c = SocketAddr::new(ip1, 8082); // 第三个连接，用于测试每个IP的连接限制
        let addr2a = SocketAddr::new(ip2, 8080);
        let addr3a = SocketAddr::new(ip3, 8080);

        // 第一个IP添加2个连接
        let guard1a = match limiter.add_conn(addr1a) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept first connection from IP1"),
        };

        let guard1b = match limiter.add_conn(addr1b) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept second connection from IP1"),
        };

        // 第一个IP的第三个连接应该被拒绝（达到单IP连接限制）
        match limiter.add_conn(addr1c) {
            ConnLimitResult::ConnLimit {
                allow_conn_num,
                current_conn_num,
            } => {
                assert_eq!(allow_conn_num, 2);
                assert_eq!(current_conn_num, 2);
            }
            _ => panic!("Expected connection limit to be reached for IP1"),
        }

        // 第二个IP添加一个连接
        let guard2a = match limiter.add_conn(addr2a) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept connection from IP2"),
        };

        // 第三个IP添加连接应该被拒绝（达到IP数量限制）
        match limiter.add_conn(addr3a) {
            ConnLimitResult::IpLimit {
                allow_ip_num,
                current_ip_num,
            } => {
                assert_eq!(allow_ip_num, 2);
                assert_eq!(current_ip_num, 2);
            }
            _ => panic!("Expected IP limit to be reached"),
        }

        // 释放IP1的一个连接
        drop(guard1a);

        // 等待异步清理完成
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        // 现在IP1应该能够再次添加连接
        match limiter.add_conn(addr1c) {
            ConnLimitResult::Ok(_) => {}
            _ => panic!("Expected to accept third connection from IP1 after dropping first"),
        }

        // 释放IP2的连接
        drop(guard2a);

        // 等待异步清理完成
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        // 现在应该能够添加第三个IP
        match limiter.add_conn(addr3a) {
            ConnLimitResult::Ok(_) => {}
            _ => panic!("Expected to be able to add third IP after dropping second"),
        }

        // 清理
        drop(guard1b);
    }

    #[tokio::test]
    async fn test_per_ip_connection_limit() {
        // 测试每个IP的连接限制
        let limiter = ConnLimitter::new(None, Some(2));

        let ip1 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let ip2 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2));

        // IP1的连接
        let addr1a = SocketAddr::new(ip1, 8080);
        let addr1b = SocketAddr::new(ip1, 8081);
        let addr1c = SocketAddr::new(ip1, 8082);

        // IP2的连接
        let addr2a = SocketAddr::new(ip2, 8080);
        let addr2b = SocketAddr::new(ip2, 8081);
        let addr2c = SocketAddr::new(ip2, 8082);

        // IP1的前两个连接应该成功
        let guard1a = match limiter.add_conn(addr1a) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept first connection from IP1"),
        };

        let guard1b = match limiter.add_conn(addr1b) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept second connection from IP1"),
        };

        // IP1的第三个连接应该被拒绝
        match limiter.add_conn(addr1c) {
            ConnLimitResult::ConnLimit {
                allow_conn_num,
                current_conn_num,
            } => {
                assert_eq!(allow_conn_num, 2);
                assert_eq!(current_conn_num, 2);
            }
            _ => panic!("Expected connection limit to be reached for IP1"),
        }

        // IP2的前两个连接应该成功（因为是每个IP单独限制）
        let guard2a = match limiter.add_conn(addr2a) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept first connection from IP2"),
        };

        let guard2b = match limiter.add_conn(addr2b) {
            ConnLimitResult::Ok(guard) => guard,
            _ => panic!("Expected to accept second connection from IP2"),
        };

        // IP2的第三个连接应该被拒绝
        match limiter.add_conn(addr2c) {
            ConnLimitResult::ConnLimit {
                allow_conn_num,
                current_conn_num,
            } => {
                assert_eq!(allow_conn_num, 2);
                assert_eq!(current_conn_num, 2);
            }
            _ => panic!("Expected connection limit to be reached for IP2"),
        }

        // 释放IP1的一个连接
        drop(guard1a);

        // 等待异步清理完成
        tokio::time::sleep(tokio::time::Duration::from_millis(50)).await;

        // IP1应该能再添加一个连接
        match limiter.add_conn(addr1c) {
            ConnLimitResult::Ok(_) => {}
            _ => panic!("Expected to accept third connection from IP1 after dropping first"),
        }

        // 清理资源
        drop(guard1b);
        drop(guard2a);
        drop(guard2b);
    }

    #[tokio::test]
    async fn test_concurrent_connections() {
        // 测试并发连接请求
        let limiter = Arc::new(ConnLimitter::new(Some(5), Some(3))); // 每个IP最多3个连接

        // 使用两个不同的IP
        let ip1 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 1));
        let ip2 = IpAddr::V4(Ipv4Addr::new(127, 0, 0, 2));

        let (tx, mut rx) = mpsc::channel::<(IpAddr, ConnLimitResult)>(100);

        // 每个IP尝试创建5个连接
        for ip in [ip1, ip2] {
            for port in 8080..8085 {
                let limiter_clone = limiter.clone();
                let tx_clone = tx.clone();
                let task_ip = ip;

                tokio::spawn(async move {
                    let addr = SocketAddr::new(task_ip, port);
                    let result = limiter_clone.add_conn(addr).await;
                    let _ = tx_clone.send((task_ip, result)).await;
                });
            }
        }

        drop(tx); // 关闭发送端，允许通道在所有任务完成后关闭

        let mut results = HashMap::new();

        // 初始化结果计数
        for ip in [ip1, ip2] {
            results.insert(ip, (0, 0)); // (成功数, 拒绝数)
        }

        // 收集结果
        while let Some((ip, result)) = rx.recv().await {
            let (ref mut ok_count, ref mut reject_count) = results.get_mut(&ip).unwrap();
            match result {
                ConnLimitResult::Ok(_) => *ok_count += 1,
                ConnLimitResult::ConnLimit { .. } => *reject_count += 1,
                _ => {}
            }
        }

        // 验证结果：每个IP应该有3个成功（连接限制）和2个拒绝
        for (ip, (ok_count, reject_count)) in results {
            assert_eq!(
                ok_count, 3,
                "IP {:?} should have 3 successful connections",
                ip
            );
            assert_eq!(
                reject_count, 2,
                "IP {:?} should have 2 rejected connections",
                ip
            );
        }
    }
}
