cfg_if::cfg_if! {
    if #[cfg(all(target_os = "linux" , feature = "aya"))] {
        pub mod other;
        pub use other::*;
    } else {
        pub mod other;
        pub use other::*;
    }
}
#[allow(unused)]
pub async fn platform_init(config: &crate::preludes::Config) -> anyhow::Result<()> {
    // cfg_if::cfg_if! {
        // if #[cfg(target_os = "linux")] {
        //     linux::platform_init(config).await
        // } else {
            other::platform_init().await
        // }
    // }
}
