use aya::maps::HashMap;
use aya::programs::{SchedClassifier, TcAttachType};
use aya::{include_bytes_aligned, Ebpf};
use aya_log::EbpfLogger;
use log::{debug, error, warn};

use anyhow::Result;
use aya::{maps::MapData, programs::tc};
use ipnetwork::IpNetwork::{V4, V6};
use pnet_datalink::interfaces;
use tokio::process::Command;

pub async fn tc_cmd(tc: &str, arg_string: String) -> Result<()> {
    match Command::new(tc).args(arg_string.split(" ")).output().await {
        Ok(output) => {
            if !(output.status.success()) {
                debug!(
                    "tc_cmd: command `{}` exited with code {}, STDERR:\n{}",
                    arg_string,
                    output.status.code().unwrap_or_default(),
                    String::from_utf8(output.stderr).unwrap_or_default()
                )
            }
        }
        Err(e) => {
            error!("tc_cmd: failed to spawn {}: {}", arg_string, e);
            return Err(e.into());
        }
    }

    Ok(())
}

pub fn set_local_addr(
    iface: &str,
    map_v4: &mut HashMap<MapData, u32, u8>,
    map_v6: &mut HashMap<MapData, [u8; 16], u8>,
) -> Result<()> {
    for i in interfaces() {
        if i.name.eq(iface) {
            for ip in i.ips {
                match ip {
                    V4(ip) => {
                        let ip = ip.ip();
                        log::info!("set_local_addr: ip: {}", ip);
                        let mut ip = ip.octets();
                        ip.reverse();
                        let ip: u32 = unsafe { core::mem::transmute(ip) };
                        map_v4.insert(ip, 0, 0)?;
                    }
                    V6(ip) => {
                        let ip = ip.ip();
                        log::info!("set_local_addr: v6 ip: {}", ip);
                        let ip = ip.octets();
                        map_v6.insert(ip, 0, 0)?;
                    }
                }
            }
        }
    }
    Ok(())
}

pub async fn set_flow_bandwidth(tc_path: &str, iface: &str, flow_id: u32, rate: u16) -> Result<()> {
    let rate = (rate as u64) * 1024 * 1024;
    let _ = tc_cmd(
        tc_path,
        format!(
            "filter del dev {} protocol ip prio 1 handle {} fw",
            iface, flow_id,
        ),
    )
    .await;
    let _ = tc_cmd(
        tc_path,
        format!("class del dev {} parent 1: classid 1:{}", iface, flow_id),
    )
    .await;
    tc_cmd(
        tc_path,
        format!(
            "class add dev {} parent 1: classid 1:{} htb rate {} burst {}",
            iface, flow_id, rate, rate
        ),
    )
    .await?;
    tc_cmd(
        tc_path,
        format!(
            "filter add dev {} parent 1: prio 1 protocol ip handle {} fw classid 1:{}",
            iface, flow_id, flow_id
        ),
    )
    .await?;

    Ok(())
}

pub async fn init_env(iface: &str, tc_path: &str) -> Result<()> {
    let rlim = libc::rlimit {
        rlim_cur: libc::RLIM_INFINITY,
        rlim_max: libc::RLIM_INFINITY,
    };
    let ret = unsafe { libc::setrlimit(libc::RLIMIT_MEMLOCK, &rlim) };
    if ret != 0 {
        debug!("remove limit on locked memory failed, ret is: {}", ret);
    }
    let _ = tc::qdisc_add_clsact(&iface);

    let tc = &tc_path;
    let iface = &iface;

    tc_cmd(tc, format!("qdisc del root dev {}", iface)).await?;
    tc_cmd(
        tc,
        format!("qdisc add dev {} root handle 1: htb default 1", iface),
    )
    .await?;
    tc_cmd(
        tc,
        format!(
            "class add dev {} parent 1: classid 1:1 htb rate 40000mbit",
            iface
        ),
    )
    .await?;

    Ok(())
}
pub struct Context {
    ebpf: Ebpf,
}

impl Context {
    pub fn new(ebpf: Ebpf) -> Self {
        Self { ebpf }
    }
}
static mut CONTEXT: Option<Context> = None;

pub async fn platform_init(config: &crate::preludes::Config) -> anyhow::Result<()> {
    for iface in config.unique_iface() {
        init_env(&iface, &config.tc_path).await?;
    }

    #[cfg(debug_assertions)]
    let mut ebpf = Ebpf::load(include_bytes_aligned!(
        "../../../target/bpfel-unknown-none/debug/zfc"
    ))?;
    #[cfg(not(debug_assertions))]
    let mut ebpf = Ebpf::load(include_bytes_aligned!(
        "../../../target/bpfel-unknown-none/release/zfc"
    ))?;
    if let Err(e) = EbpfLogger::init(&mut ebpf) {
        // This can happen if you remove all log statements from your eBPF program.
        warn!("failed to initialize eBPF logger: {}", e);
    }

    let mut local_addr_map: HashMap<_, u32, u8> =
        HashMap::try_from(ebpf.take_map("LOCAL_ADDR").unwrap())?;
    let mut local_addr_v6_map: HashMap<_, [u8; 16], u8> =
        HashMap::try_from(ebpf.take_map("LOCAL_ADDR_V6").unwrap())?;

    let program: &mut SchedClassifier = ebpf.program_mut("zfc").unwrap().try_into()?;
    program.load()?;
    for iface in config.unique_iface() {
        log::info!("add ebpf to iface: {iface}");
        set_local_addr(&iface, &mut local_addr_map, &mut local_addr_v6_map)?;
        program.attach(&iface, TcAttachType::Ingress)?;
        program.attach(&iface, TcAttachType::Egress)?;
    }
    unsafe {
        CONTEXT = Some(Context::new(ebpf));
    }
    Ok(())
}

pub struct PortToFlowMap {
    map: HashMap<MapData, u16, u32>,
}
pub fn get_port_to_flow_map() -> Result<PortToFlowMap> {
    let ebpf = unsafe { &mut CONTEXT.as_mut().unwrap().ebpf };
    let port_to_flow_map: HashMap<_, u16, u32> =
        HashMap::try_from(ebpf.take_map("PORT_TO_FLOW_MAP").unwrap())?;
    Ok(PortToFlowMap {
        map: port_to_flow_map,
    })
}
impl PortToFlowMap {
    pub fn get(&self, key: &u16, flag: u64) -> anyhow::Result<u32> {
        self.map
            .get(key, flag)
            .map_err(|e| anyhow::anyhow!("get port to flow map failed: {}", e))
    }

    pub fn insert(&self, key: u16, value: u32, flag: u64) -> anyhow::Result<()> {
        self.map
            .insert(key, value, flag)
            .map_err(|e| anyhow::anyhow!("insert port to flow map failed: {}", e))
    }

    pub fn remove(&self, key: &u16) -> anyhow::Result<()> {
        self.map
            .remove(key)
            .map_err(|e| anyhow::anyhow!("remove port to flow map failed: {}", e))
    }
}
