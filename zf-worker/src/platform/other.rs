use anyhow::Result;
pub async fn platform_init() -> Result<()> {
    Ok(())
}

pub struct PortToFlowMap {
    // map: DashMap<u16, u32>,
}
pub fn get_port_to_flow_map() -> Result<PortToFlowMap> {
    Ok(PortToFlowMap {
        // map: DashMap::new(),
    })
}
impl PortToFlowMap {
    pub fn get(&self, _key: &u16, _flag: u64) -> anyhow::Result<u32> {
        // self.map
        //     .get(key)
        //     .map(|v| v.clone())
        //     .ok_or(anyhow::anyhow!("port not found"))
        Err(anyhow::anyhow!("port not found"))
    }

    pub fn insert(&self, _key: u16, _value: u32, _flag: u64) -> anyhow::Result<()> {
        // self.map.insert(key, value);
        Ok(())
    }

    pub fn remove(&self, _key: &u16) -> anyhow::Result<()> {
        // self.map.remove(key);
        Ok(())
    }
}
