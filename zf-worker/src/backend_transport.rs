use std::{net::SocketAddr, sync::Arc};

use base64::Engine;
use private_tun::snell_impl_ver::config::ClientConfig;

use crate::{
    protocol::hammer::{
        setup_for_worker_tun_proxy_from_worker_proxy, start_hammer_client, HammerCtx,
        HammerHandlerForWorkerProxy, HammerHandlerForWorkerTun,
    },
    server::SocksBackend,
};

#[derive(Debu<PERSON>, <PERSON>lone)]
pub enum BackendTransport {
    Socks5(Arc<SocksBackend>),
    <PERSON>(HammerHandlerForWorkerTun),
}

impl BackendTransport {
    pub async fn new_from_str(
        backend: &Option<String>,
        outbound_proxy_auth: &Option<String>,
        hammer_proxyer: Option<Arc<HammerHandlerForWorkerProxy>>,
        hammer_ctx: &HammerCtx,
    ) -> Option<Self> {
        let Some(backend) = backend.as_ref() else {
            return None;
        };
        if backend.trim().is_empty() || backend.trim() == "direct" {
            None
        } else if backend.trim() == "use_proxy" {
            if let Some(hammer_proxyer) = hammer_proxyer.as_ref() {
                Some(Self::Hammer(setup_for_worker_tun_proxy_from_worker_proxy(
                    hammer_proxyer.clone(),
                )))
            } else {
                log::warn!("hammer proxyer is not set direct forwarding");
                None
            }
        } else if let Ok(addr) = backend.parse::<SocketAddr>() {
            // socks5
            if let Some(auth) = outbound_proxy_auth.as_ref() {
                let mut sp = auth.split(':');
                let user = sp.next()?;
                let password = sp.next()?;
                Some(Self::Socks5(Arc::new(SocksBackend::new(
                    addr,
                    Some(socks5_impl::protocol::UserKey {
                        username: user.to_owned(),
                        password: password.to_owned(),
                    }),
                ))))
            } else {
                Some(Self::Socks5(Arc::new(SocksBackend::new(addr, None))))
            }
        } else {
            let base64_hammer_config = base64::engine::general_purpose::STANDARD
                .decode(backend)
                .ok()?;
            if let Ok(client_config) = serde_json::from_slice::<ClientConfig>(&base64_hammer_config)
            {
                let client = start_hammer_client(
                    hammer_ctx.rt_provider.clone(),
                    client_config,
                    "for-zfc-worker".to_string(),
                    false, // no need for test because has own full link test
                )
                .await
                .ok()?;
                return Some(Self::Hammer(HammerHandlerForWorkerTun::new(client)));
            }
            None
        }
    }
}
