use reqwest::{Request, Response};

use crate::{
    backend_transport::BackendTransport, protocol::hammer::HammerHandlerForWorkerTun,
};

#[derive(Clone)]
pub enum HttpClient {
    ProxyedHammer(HammerHandlerForWorkerTun),
    // TODO: implement tiny_http_client instead of reqwest
    Reqwest(reqwest::Client),
}

impl HttpClient {
    pub fn new(backend: Option<BackendTransport>) -> anyhow::Result<Self> {
        if let Some(backend) = backend {
            match backend {
                BackendTransport::Hammer(hammer) => Ok(Self::Proxyed<PERSON><PERSON><PERSON>(hammer)),
                BackendTransport::Socks5(socks) => {
                    let client = reqwest::Client::builder()
                        .proxy(reqwest::Proxy::all(socks.proxy_url())?)
                        .build()?;
                    Ok(Self::Reqwest(client))
                }
            }
        } else {
            Ok(Self::Reqwest(reqwest::Client::new()))
        }
    }
    pub async fn request(&self, req: Request) -> anyhow::Result<Response> {
        match self {
            Self::Proxyed<PERSON><PERSON><PERSON>(hammer) => hammer.request(req).await,
            Self::Reqwest(client) => Ok(client.execute(req).await?),
        }
    }
}
