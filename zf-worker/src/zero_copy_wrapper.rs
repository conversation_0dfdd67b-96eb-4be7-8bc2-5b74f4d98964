use private_tun::traffic_collector::RealtimeTrafficCollector;
use std::{
    sync::Arc,
    task::{ready, Poll},
};

use tokio::io::{AsyncRead, AsyncWrite};

#[pin_project::pin_project]
pub struct ZeroCopyWrapper<T> {
    collector: Option<Arc<RealtimeTrafficCollector>>,
    #[pin]
    pub stream: T,
}
impl<T: AsyncRead + AsyncWrite + Send + Sync + 'static + Unpin> ZeroCopyWrapper<T> {
    pub fn new(stream: T, collector: Option<Arc<RealtimeTrafficCollector>>) -> Self {
        Self { collector, stream }
    }
}

impl<T: AsyncRead + AsyncWrite + Send + Sync + 'static + Unpin> AsyncRead for ZeroCopyWrapper<T> {
    fn poll_read(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
        buf: &mut tokio::io::ReadBuf<'_>,
    ) -> std::task::Poll<std::io::Result<()>> {
        let this = self.project();
        let ret = ready!(this.stream.poll_read(cx, buf));
        if ret.is_ok() {
            if let Some(collector) = this.collector.as_ref() {
                let filled_len = buf.filled().len();
                let addition_bytes = (filled_len / 1500 + 1) * 44; // 1500 is the max mtu size, 44 is the average ip+tcp+udp+icmp header size
                collector.add_up_bytes(filled_len as u64 + addition_bytes as u64);
            }
        }
        Poll::Ready(ret)
    }
}
impl<T: AsyncWrite + AsyncRead + Send + Sync + 'static + Unpin> AsyncWrite for ZeroCopyWrapper<T> {
    fn poll_flush(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), std::io::Error>> {
        self.project().stream.poll_flush(cx)
    }
    fn poll_shutdown(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
    ) -> std::task::Poll<Result<(), std::io::Error>> {
        self.project().stream.poll_shutdown(cx)
    }
    fn poll_write(
        self: std::pin::Pin<&mut Self>,
        cx: &mut std::task::Context<'_>,
        buf: &[u8],
    ) -> std::task::Poll<Result<usize, std::io::Error>> {
        let this = self.project();
        let ret = ready!(this.stream.poll_write(cx, buf));
        if ret.is_ok() {
            if let Some(collector) = this.collector.as_ref() {
                let addition_bytes = (buf.len() / 1500 + 1) * 44; // 1500 is the max mtu size, 44 is the average ip+tcp+udp+icmp header size
                collector.add_up_bytes(buf.len() as u64 + addition_bytes as u64);
            }
        }
        Poll::Ready(ret)
    }
}
