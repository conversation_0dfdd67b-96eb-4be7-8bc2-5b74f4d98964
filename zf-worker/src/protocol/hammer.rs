use std::{
    collections::HashSet,
    fmt::{Debug, Display},
    hash::{<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>},
    net::{IpAddr, SocketAddr},
    ops::Deref,
    sync::Arc,
    time::{Duration, Instant},
};

use anyhow::Context;
use async_ringbuf::traits::AsyncProducer;
use bytes::BytesMut;
use common::{
    app_message::{LatencyTestMethod, Mode},
    get_bind_addr_for_port,
    increment_stat::IncrementalStats,
    normalize_ip,
    rechecker::{<PERSON><PERSON>, Rechecker},
    server_provider::{
        ServerInfoProvider, ServerProvider, ServerProviderConfig, ServerProviderImpl, TimeRange,
    },
};
use futures::future::join_all;
use lru_time_cache::{Entry, LruCache};
use private_tun::{
    address::Address,
    buf_pool::create_bytesmut_pool_with_size,
    peek_channel::RxChannel,
    runtime_provider::Runtime<PERSON>rovider,
    self_proxy::MemDuplex,
    snell_impl_ver::{
        client_run::{init_ring_provider, ProxyReportError, ProxyReportResult},
        client_zfc::{run_client_with_config_and_name, ConnType},
        config::{ClientConfig, ServerEndPoint, UDP_BUFFER_SIZE},
        udp_ext::{buf_alloc_for_bytes_or_pool_item_fn, UdpExt},
        udp_intf::{
            create_ringbuf_channel, create_udp_ringbuf_channel_l2t, create_udp_ringbuf_channel_t2l,
            AsyncRingBufSender, BytesOrPoolItem, BytesWithHandle, UdpRingBufL2TSender,
        },
        udp_stream::{create_optimized_socket, MaybePooledBytesMut},
        TestLatencyMethod,
    },
};
use reqwest::{Request, Response};
use smallvec::smallvec;
#[cfg(feature = "retry_conn")]
use tokio::sync::SemaphorePermit;
use tokio::{
    io::DuplexStream,
    net::TcpStream,
    select,
    sync::{mpsc::Sender, oneshot, Mutex, Semaphore},
};
use tokio_util::sync::CancellationToken;

#[cfg(feature = "audit_log")]
use crate::log_reporter::LogClient;
use crate::{
    collector_factory::{CollectorFactory, Handle},
    conn_limitter::{ConnLimitResult, ConnLimitter, Guard},
    preludes::FilterType,
    server_provider::LatencyTester,
    types::TargetData,
    PingClient,
};

use super::{
    PipedStreamCreator, Protocol, ProtocolHandler, ProtocolHandlerFactory, ProtocolManager,
};

const UDP_HEADER_SIZE: usize = 32;
const UDP_MAX_PACKET_SIZE: usize = 2000;
pub struct HammerProtocol;

pub struct ServerMetadata {
    pub name: Arc<Box<str>>,
    pub address: Address,
}

pub struct HammerClient {
    pub cancel_token: tokio_util::sync::CancellationToken,
    pub config: ClientConfigWrapper,
    pub inbound_tx: Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
    servers: Vec<Arc<ServerMetadata>>,
}

impl HammerClient {
    pub fn get_server_addr(&self, name: &str) -> Option<Address> {
        self.servers
            .iter()
            .find(|server| server.name.as_ref().as_ref() == name)
            .map(|server| server.address.clone())
    }
    pub fn servers_iter(&self) -> impl Iterator<Item = &Arc<ServerMetadata>> {
        self.servers.iter()
    }
    pub fn get_identifier(&self) -> HammerServerIdentifier {
        (&self.config).into()
    }
    pub fn get_select_mode(&self) -> Option<Mode> {
        if let Some(multi_server_config) = &self.config.multi_server_config {
            multi_server_config.mode.try_into().ok()
        } else {
            None
        }
    }
    pub async fn push_evt(&self, evt: ConnType) -> anyhow::Result<()> {
        self.inbound_tx
            .lock()
            .await
            .push(evt)
            .await
            .map_err(|_| anyhow::anyhow!("push evt error"))
    }
}

#[derive(Debug, Clone, Hash, Eq, PartialEq)]
pub struct HammerServerIdentifier {
    pub name: Arc<Box<str>>,
    pub hash: u64,
}
impl HammerServerIdentifier {
    pub fn name_for_server(&self) -> String {
        format!("{}", self)
    }
}

impl Display for HammerServerIdentifier {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "Hammer: {}-{}", self.name, self.hash)
    }
}

#[derive(Clone)]
pub struct HammerCtx {
    pub rt_provider: Arc<RuntimeProvider>,
}

impl HammerCtx {
    pub fn new(rt_provider: Arc<RuntimeProvider>) -> Self {
        let _ = init_ring_provider();
        Self { rt_provider }
    }
    pub fn rt_provider(&self) -> &Arc<RuntimeProvider> {
        &self.rt_provider
    }
}

fn calc_hash(cfg: &ClientConfig) -> u64 {
    let mut all_server_names = smallvec::SmallVec::<[ServerEndPoint; 8]>::new();
    all_server_names.push(ServerEndPoint::from_config_main_server(cfg));
    let mut hasher = DefaultHasher::new();
    if let Some(multi_server_config) = &cfg.multi_server_config {
        // 如果mode不是fall back，则不考虑server的顺序, 尽量避免重复的配置
        all_server_names.extend(
            multi_server_config
                .back_servers
                .iter()
                .map(|backend| backend.clone()),
        );
        multi_server_config.mode.hash(&mut hasher);
        multi_server_config.test_http_url.hash(&mut hasher);
        multi_server_config.test_timeout_ms.hash(&mut hasher);
        multi_server_config.best_url_thold_ms.hash(&mut hasher);
        multi_server_config.best_speed_thold_mbps.hash(&mut hasher);
        multi_server_config.recheck_interval_sec.hash(&mut hasher);
        multi_server_config.smart_mode.hash(&mut hasher);

        let is_order_aware = matches!(
            multi_server_config.mode,
            private_tun::snell_impl_ver::server_provider::Mode::Fallback
        );

        if !is_order_aware {
            all_server_names.sort_by_key(|server| server.name.clone());
        }
    }

    all_server_names.hash(&mut hasher);
    cfg.relay_method.hash(&mut hasher);
    cfg.notify.hash(&mut hasher);
    cfg.enable_affinity.hash(&mut hasher);
    cfg.detect_ipv6.hash(&mut hasher);
    cfg.udp_batch_size.hash(&mut hasher);
    cfg.udp_buffer_size.hash(&mut hasher);
    cfg.reuse_tcp.hash(&mut hasher);
    cfg.single_runtime.hash(&mut hasher);
    cfg.default_runtime.hash(&mut hasher);
    hasher.finish()
}

#[derive(Debug, Clone)]
pub struct ClientConfigWrapper(pub ClientConfig);
impl TryFrom<&common::app_message::Protocol> for ClientConfigWrapper {
    type Error = anyhow::Error;
    fn try_from(value: &common::app_message::Protocol) -> Result<Self, Self::Error> {
        match value {
            common::app_message::Protocol::Hammer { config } => {
                Ok(Self(serde_json::from_str::<ClientConfig>(&config)?))
            }
            _ => Err(anyhow::anyhow!("protocol not belong to hammer")),
        }
    }
}
impl Deref for ClientConfigWrapper {
    type Target = ClientConfig;
    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

impl From<&ClientConfigWrapper> for HammerServerIdentifier {
    fn from(cfg: &ClientConfigWrapper) -> Self {
        let hash = calc_hash(&cfg.0);
        Self {
            name: if let Some(name) = cfg.name.as_ref() {
                Arc::new(name.clone())
            } else {
                Arc::new("".to_string().into_boxed_str())
            },
            hash,
        }
    }
}

#[async_trait::async_trait]
impl Protocol for HammerProtocol {
    type Config = ClientConfigWrapper;
    type Id = HammerServerIdentifier;
    type Client = Arc<HammerClient>;
    type Ctx = HammerCtx;
    const NEED_PORT: bool = false;
    fn init_ctx(&self, rt_provider: Arc<RuntimeProvider>) -> Self::Ctx {
        HammerCtx::new(rt_provider)
    }
    async fn start(
        ctx: &Self::Ctx,
        _manager: &ProtocolManager<Self>,
        id: &Self::Id,
        config: &Self::Config,
    ) -> anyhow::Result<Self::Client> {
        Ok(Arc::new(
            start_hammer_client(
                ctx.rt_provider.clone(),
                config.0.clone(),
                id.name_for_server(),
                false,
            )
            .await?,
        ))
    }
    async fn stop(_ctx: &Self::Ctx, client: Self::Client) -> anyhow::Result<()> {
        stop_hammer_client(&client);
        Ok(())
    }
    async fn clear_used_port(
        _ctx: &Self::Ctx,
        _manager: &ProtocolManager<Self>,
        _config: &Self::Config,
    ) -> Option<u16> {
        // do nothing
        None
    }
    fn config_from_client<'a>(_ctx: &Self::Ctx, client: &'a Self::Client) -> &'a Self::Config {
        &client.config
    }
}

pub fn collect_servers_address(config: &ClientConfig) -> Vec<(String, Address)> {
    let mut servers = Vec::new();

    servers.push((
        config
            .name
            .clone()
            .map(|x| x.to_string())
            .unwrap_or("main".to_string()),
        Address::Domain(config.server_addr.clone(), config.server_port),
    ));
    if let Some(multi_server_config) = &config.multi_server_config {
        for (idx, backend) in multi_server_config.back_servers.iter().enumerate() {
            servers.push((
                backend
                    .name
                    .as_ref()
                    .map(|x| x.to_string())
                    .unwrap_or(format!("backend_{idx}")),
                backend.addr_port.clone(),
            ));
        }
    }
    servers
}
pub async fn start_hammer_client(
    rt_provider: Arc<RuntimeProvider>,
    config: ClientConfig,
    name: String,
    enable_test: bool,
) -> anyhow::Result<HammerClient> {
    let cancel_token = tokio_util::sync::CancellationToken::new();
    let cancel_token_clone = cancel_token.clone();
    let config_clone = config.clone();
    let (inbound_tx, inbound_rx) = create_ringbuf_channel(13);
    rt_provider.get_rt().spawn(async move {
        let name_clone = name.clone();
        let handle = run_client_with_config_and_name(
            config,
            inbound_rx,
            &name_clone,
            Some(cancel_token_clone),
            enable_test,
        )
        .await?;
        Ok::<_, anyhow::Error>(handle)
    });
    let servers = collect_servers_address(&config_clone);
    // let ipv6_support_servers =
    //     detect_support_ipv6(&inbound_tx, servers.iter().map(|(n, _)| n.as_str())).await;
    let servers = servers
        .into_iter()
        .map(|(server_name, addr)| {
            Arc::new(ServerMetadata {
                address: addr,
                name: Arc::new(server_name.into_boxed_str()),
            })
        })
        .collect();
    Ok(HammerClient {
        cancel_token,
        config: ClientConfigWrapper(config_clone),
        inbound_tx: Arc::new(tokio::sync::Mutex::new(inbound_tx)),
        servers,
    })
}

pub fn stop_hammer_client(client: &HammerClient) {
    client.cancel_token.cancel();
}

struct ClientData {
    target: SocketAddr,
    cancel_token: CancellationToken,
    data_send_to_remote_tx: UdpRingBufL2TSender,
    ts: Instant,
    _collector_handle: Option<Handle>,
    _guard: Option<Guard>,
}

pub struct HammerProtocolHandler {
    bind_port: u16,
    reuse_tcp: bool,
    target: ServerSelectorForTotal,
    inbound_tx: Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
    rechecker: Rechecker<ServerSelectorForTotal>,
    collector_factory: Arc<CollectorFactory>,
    piped_stream_creator: Option<PipedStreamCreator>,
}

async fn proc_udp_data(
    protocol_handler: &HammerProtocolHandler,
    all_clients: &mut LruCache<SocketAddr, ClientData>,
    selector: &ServerSelectorForTotal,
    buf: impl Iterator<Item = (BytesOrPoolItem, Option<SocketAddr>)>,
    from_addr: SocketAddr,
    bind: SocketAddr,
    out_ip: &Option<IpAddr>,
    socket: &Arc<UdpExt>,
    inbound_tx: &Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
    end_evt_tx: &Sender<(SocketAddr, Instant)>,
    #[cfg(feature = "audit_log")] log_client: &Arc<LogClient>,
    piped_stream_creator: &Option<PipedStreamCreator>,
    cancel_global: &CancellationToken,
    sub_id: i32,
    conn_limitter: &Option<ConnLimitter>,
) -> anyhow::Result<()> {
    let entry = match all_clients.entry(from_addr.clone()) {
        Entry::Occupied(e) => e.into_mut(),
        Entry::Vacant(e) => {
            let guard = if let Some(conn_limitter) = &conn_limitter {
                match conn_limitter.add_conn(from_addr) {
                    ConnLimitResult::Ok(guard) => Some(guard),
                    e => {
                        log::warn!("udp relay proxy conn limit failed from: {from_addr} bind: {bind} sub_id: {sub_id} e: {e:?}");
                        return Ok(());
                    }
                }
            } else {
                None
            };
            let target_info = selector.get_target_info(None)?;
            let target = match &target_info.target {
                Address::Socket(addr) => *addr,
                Address::Domain(addr, port) => tokio::time::timeout(
                    Duration::from_secs(2),
                    tokio::net::lookup_host((addr.to_string(), *port)),
                )
                .await??
                .next()
                .ok_or(anyhow::anyhow!("invalid target address"))?,
            };
            let server_name = target_info.hammer_server.clone();
            log::info!(
                "new udp conn from: {from_addr}, bind: {bind} remote: {target} server_name: {server_name} sub_id: {sub_id}"
            );
            #[cfg(feature = "audit_log")]
            let _ = log_client.info(
                format!("new udp conn from: {from_addr}, bind: {bind} remote: {target} server_name: {server_name} sub_id: {sub_id}"),
                vec![from_addr.to_string(), bind.to_string(), target.to_string(), server_name.to_string(), sub_id.to_string()],
            );
            let (rst_tx, rst_rx) = tokio::sync::oneshot::channel::<ProxyReportResult<()>>();
            let cancel_token = CancellationToken::new();
            let (data_send_to_remote_tx, data_send_to_remote_rx) = create_udp_ringbuf_channel_l2t();
            let (data_send_to_local_tx, mut data_send_to_local_rx) =
                create_udp_ringbuf_channel_t2l();
            let socket_clone = socket.clone();
            let from_addr_clone = from_addr.clone();
            let target_clone = target.clone();
            let cancel_token_clone = cancel_token.clone();
            let end_evt_tx = end_evt_tx.clone();
            let server_name_clone = server_name.clone();
            let cancel_global_clone = cancel_global.clone();
            tokio::spawn(async move {
                let normalize_target = normalize_ip(target_clone.ip());
                loop {
                    let mut recv_buf =
                        smallvec::SmallVec::<[(BytesWithHandle, SocketAddr); 32]>::new();
                    select! {
                        biased;
                        n = data_send_to_local_rx.recv_with_many(&mut recv_buf, 32) => {
                            if n == 0 {
                                log::info!("udp relay proxy eof bind: {bind} remote: {target_clone} server_name: {server_name_clone} to client: {from_addr_clone}");
                                cancel_token_clone.cancel();
                                break;
                            }
                        }
                        _ = cancel_token_clone.cancelled() => {
                            log::info!("udp relay proxy cancel bind: {bind} remote: {target_clone} server_name: {server_name_clone} to client: {from_addr_clone} canceled");
                            break;
                        }
                        _ = cancel_global_clone.cancelled() => {
                            log::info!("udp relay proxy global cancel bind: {bind} remote: {target_clone} server_name: {server_name_clone} to client: {from_addr_clone} canceled");
                            break;
                        }
                    };

                    if recv_buf.iter().any(|(_, from_remote_addr)| {
                        !(normalize_ip(from_remote_addr.ip()) == normalize_target
                            && from_remote_addr.port() == target_clone.port())
                    }) {
                        log::warn!("udp relay proxy from unknown remote bind: {bind} expect remote: {target_clone} server_name: {server_name_clone} client: {from_addr_clone}");
                        continue;
                    }
                    // log::trace!("udp relay bind: {bind} remote: {from_remote_addr} client: {from_addr_clone} data_len: {}", data.len());
                    if let Err(e) = socket_clone
                        .send_mmsg(
                            recv_buf
                                .iter()
                                .map(|(data, _from_remote_addr)| (data.deref(), &from_addr_clone)),
                            recv_buf.len(),
                        )
                        .await
                    {
                        log::error!("udp relay proxy send to client from remote failed: {e:?} bind: {bind} server_name: {server_name_clone} client: {from_addr_clone}");
                        cancel_token_clone.cancel();
                        break;
                    }
                }
                let _ = end_evt_tx.send((from_addr_clone, Instant::now())).await;
            });
            let port = bind.port();
            // wait for proxy report
            let rechecker = protocol_handler.rechecker.clone();
            let server_name_clone = server_name.clone();
            let from_addr_clone = from_addr.clone();
            tokio::spawn(async move {
                let Ok(rst) = rst_rx.await else {
                    log::error!("wait udp proxy report failed bind: {port} server_name: {server_name_clone} client: {from_addr_clone}");
                    return;
                };
                if let Some(error) = rst.error {
                    log::error!("udp proxy report error: {error:?} bind: {port} server_name: {server_name_clone} client: {from_addr_clone}");
                    rechecker.do_recheck().await;
                }
            });
            let custom_pipe = if let Some(creator) = piped_stream_creator {
                log::debug!("create custom piped stream for udp bind: {bind} remote: {target} server_name: {server_name} client: {from_addr}");
                Some(creator().await?)
            } else {
                None
            };
            log::debug!(
                "create udp collector group for server_name: {server_name} client: {from_addr_clone} bind: {bind}",
            );
            let collector = protocol_handler
                .collector_factory
                .clone()
                .create_collector_group(
                    smallvec![Some(server_name.clone()), None],
                    protocol_handler.bind_port,
                );
            log::debug!(
                "create collector group: {} for udp bind: {bind} remote: {target} server_name: {server_name} client: {from_addr}",
                collector.idx()
            );
            let conn_udp = ConnType::Udp {
                target: Address::Socket(target.clone()),
                rst_tx,
                bind_addr: bind.clone(),
                out_ip: out_ip.clone(),
                cancel_token: cancel_token.clone(),
                data_send_to_remote: data_send_to_remote_rx,
                data_send_to_local: data_send_to_local_tx,
                peer_addr: from_addr.clone(),
                traffic_collector: Some(collector.collector.clone()),
                server_name: Some(server_name),
                piped_stream: custom_pipe.map(|c| c.io),
            };
            inbound_tx
                .lock()
                .await
                .push(conn_udp)
                .await
                .map_err(|_| anyhow::anyhow!("push conn_udp error"))?;
            e.insert(ClientData {
                target: target,
                cancel_token,
                data_send_to_remote_tx,
                _collector_handle: Some(collector),
                _guard: guard,
                ts: Instant::now(),
            })
        }
    };
    if entry.cancel_token.is_cancelled() {
        all_clients.remove(&from_addr);
    } else {
        log::trace!("udp relay send to remote bind: {bind} client: {from_addr}");
        if !entry
            .data_send_to_remote_tx
            .push_iter_all(buf.filter_map(|(buf, addr)| {
                addr.map(|_addr| (buf, Address::Socket(entry.target.clone())))
            }))
            .await
        {
            log::error!("udp relay proxy send to remote failed bind: {bind} client: {from_addr}");
            all_clients.remove(&from_addr);
        }
    }
    Ok(())
}

#[async_trait::async_trait]
impl ProtocolHandler for HammerProtocolHandler {
    async fn proc_tcp_stream(
        &self,
        stream: TcpStream,
        cancel: CancellationToken,
        filters: Option<Arc<Vec<FilterType>>>,
        _out_ip_addr: Option<IpAddr>, // todo supprt dynamic out ip addr
        _sem: Arc<Semaphore>,
    ) -> anyhow::Result<()> {
        #[cfg(feature = "retry_conn")]
        let mut black_server_list = smallvec::SmallVec::<[Arc<ServerInfoForTotal>; 3]>::new();
        #[cfg(feature = "retry_conn")]
        let mut retry_sem: Option<SemaphorePermit> = None;
        let mut stream = Some(stream);
        for i in 0..3 {
            let Some(current_stream) = stream.take() else {
                break;
            };
            #[cfg(feature = "retry_conn")]
            let target_info = self
                .target
                .get_target_info(Some(black_server_list.as_slice()))?;
            #[cfg(not(feature = "retry_conn"))]
            let target_info = self.target.get_target_info(None)?;
            let target = &target_info.target;
            let server_name = &target_info.hammer_server;
            let collector = self
                .collector_factory
                .clone()
                .create_collector_group(smallvec![Some(server_name.clone()), None], self.bind_port);
            // TODO 优化这儿的内存占用，因为T是TcpStream，占用了不少内存
            let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
            let filter_vec = if let Some(filters) = filters.as_ref() {
                let mut filters_vec = Vec::with_capacity(filters.len());
                for filter in filters.iter() {
                    filters_vec.push(filter.into());
                }
                Some(filters_vec)
            } else {
                None
            };
            self.inbound_tx
                .lock()
                .await
                .push(ConnType::Tcp {
                    stream: current_stream,
                    target: target.clone(),
                    rst_tx,
                    traffic_collector: Some(collector.collector.clone()),
                    server_name: Some(server_name.clone()),
                    one_rtt: false,
                    reuse_tcp: self.reuse_tcp,
                    filters: filter_vec,
                })
                .await
                .map_err(|_| anyhow::anyhow!("push conn_tcp error"))
                .context(format!(
                    "bind: {} send to hammer inbound failed",
                    self.bind_port
                ))?;
            // wait for proxy report
            let rst = select! {
                biased;
                r = rst_rx => r.context(format!("bind: {} target: {} recv result", self.bind_port, target))?,
                _ = cancel.cancelled() => {
                    log::info!("bind: {} target: {} hammer_server: {} proc_tcp_stream cancel", self.bind_port, target, server_name);
                    return Ok(());
                }
            };
            log::debug!(
                "bind: {} target: {} hammer_server: {} proc_tcp_stream rst: {:?}",
                self.bind_port,
                target,
                server_name,
                rst
            );
            if let Some(error) = rst.error {
                log::error!(
                    "bind: {} target: {} hammer_server: {} proc_tcp_stream error: {:?}",
                    self.bind_port,
                    target,
                    server_name,
                    error
                );
                if i == 0 {
                    self.rechecker.do_recheck().await;
                }
                #[cfg(feature = "retry_conn")]
                match error {
                    ProxyReportError::ConnectRemoteFailed(_addr, io) => {
                        if let Some(io) = io {
                            stream = io.into_inner();
                            let _ = retry_sem.take();
                            retry_sem = Some(_sem.try_acquire()?);
                            black_server_list.push(target_info);
                            // tokio::time::sleep(std::time::Duration::from_millis(50)).await;
                        } else {
                            // can't retry
                            break;
                        }
                    }
                    ProxyReportError::Other(_) => {
                        break;
                    }
                    ProxyReportError::IoError(_) => {
                        break;
                    }
                }
            } else {
                break;
            }
        }
        Ok(())
    }
    async fn proc_udp_bind(
        &self,
        bind_addr: Option<SocketAddr>,
        out_addr: Option<IpAddr>,
        cancel: CancellationToken,
        #[cfg(feature = "audit_log")] log_client: Arc<LogClient>,
        sub_id: i32,
        conn_limitter: Option<ConnLimitter>,
    ) -> anyhow::Result<()> {
        let bind = match bind_addr {
            Some(bind) => bind,
            None => get_bind_addr_for_port(self.bind_port),
        };

        let mut all_clients = LruCache::<SocketAddr, ClientData>::with_expiry_duration(
            std::time::Duration::from_secs(30),
        );
        log::info!("start udp socks proxy bind: {bind}");
        let mut try_times = 0;
        let socket = loop {
            match create_optimized_socket(bind, unsafe { UDP_BUFFER_SIZE }) {
                Ok(socket) => {
                    break socket;
                }
                Err(e) => {
                    log::error!("bind udp socket failed: {e} wait for try again");
                    tokio::time::sleep(std::time::Duration::from_secs(5)).await;
                    try_times += 1;
                    if try_times > 10 {
                        return Err(anyhow::anyhow!(
                            "bind udp socket failed: {e} wait for try again"
                        ));
                    }
                }
            }
        };
        let socket_ref = socket2::SockRef::from(&socket);
        let _ = socket_ref.set_reuse_port(true);
        let socket: Arc<UdpExt> = Arc::new(socket.try_into()?);
        let (rmv_evt_tx, mut rmv_evt_rx) = tokio::sync::mpsc::channel::<(SocketAddr, Instant)>(4);
        // 不需要太大的buf，因为udp用得比较少
        let buf_pool = create_bytesmut_pool_with_size(1, UDP_MAX_PACKET_SIZE);
        loop {
            let mut buf_alloc = buf_alloc_for_bytes_or_pool_item_fn(|_idx| {
                let mut buf = match buf_pool.try_pull_one() {
                    Some(buf) => MaybePooledBytesMut::Pooled(buf),
                    None => {
                        if buf_pool.created_count() < 32 {
                            // 如果池子还比较小，扩充池子
                            MaybePooledBytesMut::Pooled(buf_pool.pull_one())
                        } else {
                            MaybePooledBytesMut::NotPooled(BytesMut::with_capacity(
                                UDP_MAX_PACKET_SIZE,
                            ))
                        }
                    }
                };
                buf.clear();
                buf.reserve(UDP_MAX_PACKET_SIZE);
                let capacity = buf.capacity();
                unsafe {
                    buf.set_len(capacity - 16); // save 16 bytes for encrypt tag
                }
                (buf, UDP_HEADER_SIZE)
            });
            select! {
                biased;
                r = socket.recv_with_buf_alloc(&mut buf_alloc) => {
                    if let Ok(_) = r {
                        let mut bufs = buf_alloc.into_bufs();
                        let len = bufs.iter().enumerate().find(|(_idx, (_, addr))| {
                            addr.is_none()
                        })
                        .map(|(id, _)| id)
                        .unwrap_or(bufs.len());
                        bufs.truncate(len);
                        if len == 1 {
                            // fast path
                            let Some(from_addr) = bufs[0].1 else {
                                continue;
                            };
                            if let Err(e) = proc_udp_data(self, &mut all_clients, &self.target, bufs.into_iter(), from_addr, bind.clone(), &out_addr, &socket, &self.inbound_tx, &rmv_evt_tx,
                            #[cfg(feature = "audit_log")]
                            &log_client.clone(),
                            &self.piped_stream_creator, &cancel, sub_id, &conn_limitter).await {
                                log::error!("bind:{bind} udp socks proc_udp_data failed: {:?}", e);
                            }
                        } else {
                            while let Some(Some(from_addr)) = bufs.first().map(|(_, addr)| addr) {
                                // find next different addr
                                let from_addr = from_addr.clone();
                                let next_idx = bufs.iter().enumerate().find(|(_id, (_, addr))| {
                                    addr.unwrap() != from_addr
                                })
                                .map(|(id, _)| id)
                                .unwrap_or(bufs.len());
                                let data_slice = bufs.drain(0..next_idx);
                                if let Err(e) = proc_udp_data(self, &mut all_clients, &self.target, data_slice, from_addr, bind.clone(), &out_addr, &socket, &self.inbound_tx, &rmv_evt_tx,
                                #[cfg(feature = "audit_log")]
                                &log_client.clone(),
                                &self.piped_stream_creator, &cancel, sub_id, &conn_limitter).await {
                                    log::error!("bind:{bind} udp socks proc_udp_data failed: {:?}", e);
                                }
                            }
                        }

                    }
                }
                client_addr = rmv_evt_rx.recv() => {
                    log::info!("udp socks proxy rmv: {:?}", client_addr);
                    // make sure can
                    if let Some((client_addr, ts)) = client_addr {
                        if let Some(to_removed) = all_clients.get(&client_addr) {
                            // 确保没有把新连接给移除掉
                            if ts > to_removed.ts {
                                all_clients.remove(&client_addr);
                                log::info!("current udp session count: {}", all_clients.len());
                                for (addr, _) in all_clients.iter() {
                                    log::info!("udp session client: {:?}", addr);
                                }
                            }
                        }


                    }
                }
                _ = cancel.cancelled() => {
                    log::info!("udp socks on port: {} proxy cancel", bind.port());
                    break;
                }
            }
        }
        Ok(())
    }
}
#[async_trait::async_trait]
impl ProtocolHandlerFactory for HammerClient {
    type Protocol = HammerProtocol;
    async fn create_protocol_handler(
        &self,
        bind_port: u16,
        target: TargetData,
        collector_factory: Arc<CollectorFactory>,
        _ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        piped_stream_creator: Option<PipedStreamCreator>,
        rt_provider: Arc<RuntimeProvider>,
        conn_sem: Arc<Semaphore>,
    ) -> anyhow::Result<Arc<Box<dyn ProtocolHandler>>> {
        let rt = rt_provider.get_rt().as_ref().clone();
        let latency_test_method = target
            .latency_test_method
            .unwrap_or(LatencyTestMethod::Tcpping);
        let selector = create_server_selector_hammer_full_link(
            (&target).into(),
            target.mode,
            port_cancel.clone(),
            &self,
            latency_test_method.clone(),
            piped_stream_creator.clone(),
            Some(rt),
            conn_sem,
        )
        .await?;
        Ok(Arc::new(Box::new(HammerProtocolHandler {
            bind_port,
            target: selector.clone(),
            inbound_tx: self.inbound_tx.clone(),
            collector_factory: collector_factory.clone(),
            rechecker: Rechecker::new(selector, Duration::from_secs(5)),
            piped_stream_creator,
            reuse_tcp: self.config.reuse_tcp.unwrap_or(true),
        })))
    }
}

pub struct HammerHandlerForWorkerProxy {
    client: Arc<HammerClient>,
    target: TargetData,
}

impl HammerHandlerForWorkerProxy {
    pub async fn proc_worker_proxy_stream(
        self: Arc<Self>,
        dup: DuplexStream,
    ) -> anyhow::Result<()> {
        let cancel = self.client.cancel_token.clone();
        let duplex = MemDuplex::new(dup, cancel);
        let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
        let target: Address = self
            .target
            .addr_list
            .first()
            .map(|x| Address::Socket(x.clone()))
            .unwrap();
        self.client
            .push_evt(ConnType::Duplex {
                stream: duplex,
                target,
                rst_tx,
                server_name: None,
                traffic_collector: None,
                one_rtt: false,
                reuse_tcp: self.client.config.reuse_tcp.unwrap_or(true),
            })
            .await?;
        let rst = rst_rx.await?;
        log::info!("hammer for worker proxy: rst: {:?}", rst);
        Ok(())
    }
}

#[derive(Clone)]
pub struct HammerHandlerForWorkerTun {
    client: Arc<Mutex<Arc<HammerClient>>>,
}

impl HammerHandlerForWorkerTun {
    pub fn new(client: HammerClient) -> Self {
        Self {
            client: Arc::new(Mutex::new(Arc::new(client))),
        }
    }
    pub fn client(&self) -> &Arc<Mutex<Arc<HammerClient>>> {
        &self.client
    }
    pub async fn get_best_latency_server(&self) -> anyhow::Result<Arc<Box<str>>> {
        let (tx, rx) = oneshot::channel();
        let client = self.client.lock().await.clone();
        client
            .push_evt(ConnType::GetCurrentBestLatencyServer { rst_tx: tx })
            .await?;
        let rst = rx.await?;
        Ok(rst)
    }
    pub async fn request(&self, req: Request) -> anyhow::Result<Response> {
        log::debug!("hammer for worker tun http request: {:?}", req);
        let (rst_tx, rst_rx) = oneshot::channel();
        let client = self.client.lock().await.clone();
        client
            .push_evt(ConnType::HttpRequest {
                req,
                timeout: None,
                server_name: None,
                rst_tx: rst_tx,
            })
            .await?;
        match rst_rx.await {
            Ok(rst) => {
                match rst {
                    Ok((resp, cancel_token)) => {
                        log::debug!(
                            "hammer for worker tun http request rst status: {:?}",
                            resp.status()
                        );
                        // wait for 10s to cancel the request make the connection to close
                        tokio::spawn(async move {
                            tokio::time::sleep(std::time::Duration::from_secs(10)).await;
                            cancel_token.cancel();
                        });
                        Ok(resp)
                    }
                    Err(e) => {
                        log::error!("hammer for worker tun http request failed: {:?}", e);
                        Err(e)
                    }
                }
            }
            Err(_e) => {
                log::error!("hammer for worker tun http request recv error");
                Err(anyhow::anyhow!(
                    "hammer for worker tun http request recv error"
                ))
            }
        }
    }
}

impl Debug for HammerHandlerForWorkerTun {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "HammerHandlerForWorkerTun")
    }
}

impl HammerHandlerForWorkerTun {
    pub async fn create_handler(
        &self,
        mut target: TargetData,
        port: u16,
        collector_factory: Arc<CollectorFactory>,
        ping_client: Arc<PingClient>,
        port_cancel: CancellationToken,
        rt_provider: Arc<RuntimeProvider>,
        conn_sem: Arc<Semaphore>,
    ) -> anyhow::Result<Arc<Box<dyn ProtocolHandler>>> {
        let client = self.client.lock().await;
        // override mode, if only one address, then use tun's select mode
        if target.addr_list.len() == 1 {
            if let Some(mode) = client.get_select_mode() {
                target.mode = Some(mode);
            }
        }

        client
            .create_protocol_handler(
                port,
                target,
                collector_factory,
                ping_client,
                port_cancel,
                None,
                rt_provider,
                conn_sem,
            )
            .await
    }
}

pub async fn setup_for_worker_proxy(
    client_config: ClientConfig,
    target: TargetData,
) -> anyhow::Result<HammerHandlerForWorkerProxy> {
    // using default runtime for worker proxy to avoid long schedule latency
    let client = start_hammer_client(
        Arc::new(RuntimeProvider::DefaultRuntime),
        client_config,
        "for_worker_proxy".to_string(),
        true,
    )
    .await?;
    Ok(HammerHandlerForWorkerProxy {
        client: Arc::new(client),
        target,
    })
}

pub fn setup_for_worker_tun_proxy_from_worker_proxy(
    worker_proxy: Arc<HammerHandlerForWorkerProxy>,
) -> HammerHandlerForWorkerTun {
    let client = worker_proxy.client.clone();
    HammerHandlerForWorkerTun {
        client: Arc::new(Mutex::new(client)),
    }
}

// pub async fn get_server_from_config(config: &ClientConfig, name: &str) -> Option<SocketAddr> {
//     // test main server
//     if let Some(main_server_name) = config.name.as_ref() {
//         if name == main_server_name.as_str() {
//             return tokio::net::lookup_host((config.server_addr.as_str(), config.server_port))
//                 .await
//                 .ok()?
//                 .next();
//         }
//     }
//     // test backup server
//     for backup_server in config
//         .multi_server_config
//         .as_ref()
//         .map(|x| &x.back_servers)
//         .into_iter()
//         .flatten()
//     {
//         if let Some(backup_server_name) = backup_server.name.as_ref() {
//             if name == backup_server_name.as_str() {
//                 match &backup_server.addr_port {
//                     Address::Socket(addr) => return Some(addr.clone()),
//                     Address::Domain(domain, port) => {
//                         return tokio::net::lookup_host((domain.as_str(), *port))
//                             .await
//                             .ok()?
//                             .next()
//                     }
//                 }
//             }
//         }
//     }
//     None
// }

//#[derive(Clone)]
pub struct ServerInfoForTotal {
    pub name: Box<str>,
    pub hammer_server: Arc<Box<str>>,
    pub target: Address,
    pub time_ranges: Option<Vec<TimeRange>>,
    pub avg_latency_ms: Arc<parking_lot::Mutex<IncrementalStats>>, // ms
    pub support_ipv6: bool,
}
impl Display for ServerInfoForTotal {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(
            f,
            "[server: {}, hammer_server: {}, target: {}]",
            self.name, self.hammer_server, self.target
        )
    }
}
impl ServerInfoForTotal {
    pub fn new(
        hammer_server: Arc<Box<str>>,
        target: Address,
        time_ranges: Option<Vec<TimeRange>>,
    ) -> Self {
        let name = format!("{}-{}", hammer_server, target);
        Self {
            name: name.into_boxed_str(),
            hammer_server,
            target,
            time_ranges,
            avg_latency_ms: Arc::new(parking_lot::Mutex::new(IncrementalStats::new())),
            support_ipv6: true, // 对于全链路检测，默认都支持，如果不支持，那么选择服务器自然会过滤掉
        }
    }
}

impl ServerInfoProvider for ServerInfoForTotal {
    fn name(&self) -> &str {
        &self.name
    }
    fn select_weight(&self) -> Option<u64> {
        None
    }
    fn time_ranges(&self) -> &Option<Vec<TimeRange>> {
        &self.time_ranges
    }
    fn allow_fallback(&self) -> bool {
        true
    }
    fn should_switch_server(&self, current_latency_ms: u64) -> bool {
        let mut lock = self.avg_latency_ms.lock();
        let avg_latency = lock.average();
        if avg_latency < 1.0 {
            return false;
        }
        let rst = current_latency_ms > (avg_latency * 1.5) as u64;
        lock.add(current_latency_ms as f64);
        rst
    }
    fn allow_ipv6(&self) -> bool {
        self.support_ipv6
    }
}

#[derive(Clone)]
pub struct HammerTotalLatencyChecker {
    method: LatencyTestMethod,
    evt_tx: Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
    piped_stream_creator: Option<PipedStreamCreator>,
}

impl LatencyTester for HammerTotalLatencyChecker {
    type Server = ServerInfoForTotal;
    async fn test_latency(
        &self,
        server: &Self::Server,
    ) -> Result<std::time::Duration, anyhow::Error> {
        // self -> hammer inbound
        let remote = server.target.clone();
        let method = &self.method;
        let server_name = server.hammer_server.clone();
        let begin = std::time::Instant::now();
        let piped_stream = match &self.piped_stream_creator {
            Some(creator) => Some(creator().await?),
            None => None,
        };
        // fwd-server -> remote
        let to_remote = async {
            let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
            self.evt_tx
                .lock()
                .await
                .push(ConnType::TestLatency {
                    rst_tx,
                    target: remote.clone(),
                    server_name: server_name.clone(),
                    method: match &method {
                        LatencyTestMethod::Tcpping => TestLatencyMethod::Tcpping,
                        LatencyTestMethod::Icmp => TestLatencyMethod::Icmp,
                    },
                    piped_stream: piped_stream.map(|c| c.io),
                    reuse_tcp: true, // do not reuse tcp to make latency more accurate
                })
                .await
                .map_err(|_| anyhow::anyhow!("push test latency error"))?;
            let rst = rst_rx.await?;
            Ok::<_, anyhow::Error>(rst)
        };
        let from_remote = match to_remote.await {
            Ok(v) => match v {
                Ok(v) => {
                    // skip too large latency >6s
                    if v > 6000000 {
                        log::error!(
                            "test latency from remote: {} latency: {}ms  too large skiped fwd server: {}",
                            remote, v / 1000, server_name
                        );
                        return Err(anyhow::anyhow!("latency too large"));
                    }
                    v
                }
                Err(e) => {
                    log::error!(
                        "test latency from remote: {} failed: {e:?} server: {}",
                        remote,
                        server_name
                    );
                    return Err(e.into());
                }
            },
            Err(e) => {
                log::error!(
                    "test latency from remote: {} failed: {e:?} server: {}",
                    remote,
                    server_name
                );
                return Err(e);
            }
        };
        log::debug!(
            "latency measure for: {} fwd_server: {} from remote: {:?}ms elapsed: {:?}ms",
            remote,
            server_name,
            from_remote / 1000,
            begin.elapsed().as_millis()
        );
        Ok(begin.elapsed())
    }
}

#[derive(Clone)]
pub struct ServerSelectorForTotal(Arc<ServerProviderImpl<ServerInfoForTotal>>);

impl Checker for ServerSelectorForTotal {
    async fn recheck(&self) {
        self.0.as_ref().recheck();
    }
}

pub async fn create_server_selector_hammer_full_link(
    target_list: Vec<Address>,
    mode: Option<Mode>,
    cancel_token: CancellationToken,
    hammer_client: &HammerClient,
    latency_test_method: LatencyTestMethod,
    piped_stream_creator: Option<PipedStreamCreator>,
    rt_provider: Option<tokio::runtime::Handle>,
    conn_sem: Arc<Semaphore>,
) -> anyhow::Result<ServerSelectorForTotal> {
    let tester = HammerTotalLatencyChecker {
        method: latency_test_method,
        evt_tx: hammer_client.inbound_tx.clone(),
        piped_stream_creator,
    };
    let mut server_list = Vec::<ServerInfoForTotal>::new();
    for hammer_server in hammer_client.servers.iter() {
        let target_list = target_list
            .iter()
            .map(|x| ServerInfoForTotal::new(hammer_server.name.clone(), x.clone(), None));
        server_list.extend(target_list);
    }
    let mut config = ServerProviderConfig::default();
    config.mode = mode;
    let provider = ServerProvider::create(
        server_list,
        config,
        tester,
        cancel_token,
        rt_provider,
        conn_sem,
    )
    .await?;
    Ok(ServerSelectorForTotal(Arc::new(provider)))
}

impl ServerSelectorForTotal {
    pub fn get_target_info(
        &self,
        black_servers: Option<&[Arc<ServerInfoForTotal>]>,
    ) -> Result<Arc<ServerInfoForTotal>, anyhow::Error> {
        let target = self
            .0
            .get_server::<Box<dyn Fn() -> &'static Address + Send + Sync + 'static>>(
                None,
                None,
                black_servers,
            )?;
        Ok(target)
    }
}

impl Deref for ServerSelectorForTotal {
    type Target = Arc<ServerProviderImpl<ServerInfoForTotal>>;
    fn deref(&self) -> &Self::Target {
        &self.0
    }
}

#[allow(dead_code)]
async fn detect_support_ipv6(
    evt_tx: &Sender<ConnType>,
    server_names: impl IntoIterator<Item = &str>,
) -> HashSet<String> {
    log::debug!("begin detect support ipv6");
    let mut tasks = Vec::new();
    const TEST_IPV6_ADDRESS: &str = "[2001:4860:4860::8888]:853";
    let mut total_cnt = 0;
    for server_name in server_names {
        let task = async {
            let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
            evt_tx
                .send(ConnType::TestLatency {
                    rst_tx,
                    target: Address::Socket(TEST_IPV6_ADDRESS.parse().unwrap()),
                    server_name: Arc::new(server_name.to_string().into_boxed_str()),
                    method: TestLatencyMethod::Tcpping,
                    piped_stream: None,
                    reuse_tcp: false,
                })
                .await?;
            let rst = tokio::time::timeout(std::time::Duration::from_secs(2), rst_rx).await??;
            Ok::<_, anyhow::Error>((server_name.to_string(), rst))
        };
        tasks.push(task);
        total_cnt += 1;
    }
    let rst = join_all(tasks).await;
    let rst = rst
        .into_iter()
        .filter_map(|x| {
            x.ok()
                .and_then(|(n, x)| x.ok().and_then(|v| (v < 2000000).then(|| n)))
        })
        .collect::<HashSet<_>>();
    log::debug!(
        "detect support ipv6 result: {:?}, supported: {}, total: {}",
        rst,
        rst.len(),
        total_cnt
    );
    rst
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_detect_support_ipv6() {
        println!("sizeof ClientData: {}", std::mem::size_of::<ClientData>());
    }
}
