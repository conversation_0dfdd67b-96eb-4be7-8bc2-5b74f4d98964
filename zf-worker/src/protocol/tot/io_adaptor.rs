use std::{
    pin::Pin,
    sync::Arc,
    task::{ready, Context, Poll},
    time::Duration,
};

use common::{
    app_message::{LatencyTestMethod, Mode},
    rechecker::<PERSON><PERSON><PERSON>,
    server_provider::{ServerInfo, ServerProviderImpl, ServerSelector},
    PingClient,
};
use lru_time_cache::LruCache;
use private_tun::{
    address::Address, batch_writer::StreamId, runtime_provider::RuntimeProvider,
    self_proxy::MemDuplex,
};
use smallvec::smallvec;
use tcp_over_multi_tcp_client::{BackendIO, InputIO, SplitableIO, StreamMeta};
use tokio::{
    io::{AsyncRead, AsyncWrite, ReadBuf},
    net::{
        tcp::{OwnedReadHalf, OwnedWriteHalf},
        TcpStream,
    },
    sync::{Mutex, RwLock, Semaphore},
};
use tokio_util::sync::CancellationToken;

use crate::collector_factory::CollectorFactory;

use super::{latency_tester::Tot<PERSON>ate<PERSON><PERSON><PERSON><PERSON>, session::FwdT<PERSON><PERSON><PERSON>r<PERSON><PERSON><PERSON>, HammerHandlerForTot};

pub struct TotInputIO {
    pub accept_rx: tokio::sync::mpsc::Receiver<TotStream>,
    pub cancel: CancellationToken,
}
#[pin_project::pin_project]
pub struct TotStream {
    #[pin]
    stream: TcpStream,
    cancel: CancellationToken,
}

impl TotStream {
    pub fn new(stream: TcpStream, cancel: CancellationToken) -> Self {
        Self { stream, cancel }
    }
}
// TotStream 不需要统计流量，交给TotBackendIO 统计
impl AsyncRead for TotStream {
    fn poll_read(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<std::io::Result<()>> {
        if self.cancel.is_cancelled() {
            return Poll::Ready(Err(std::io::Error::other(anyhow::anyhow!("closed"))));
        }
        let this = self.project();
        let rst = ready!(this.stream.poll_read(cx, buf));
        Poll::Ready(rst)
    }
}
impl AsyncWrite for TotStream {
    fn poll_write(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<std::io::Result<usize>> {
        if self.cancel.is_cancelled() {
            return Poll::Ready(Err(std::io::Error::other(anyhow::anyhow!("closed"))));
        }
        let this = self.project();
        let rst = ready!(this.stream.poll_write(cx, buf));
        Poll::Ready(rst)
    }
    fn poll_shutdown(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<std::io::Result<()>> {
        if self.cancel.is_cancelled() {
            return Poll::Ready(Err(std::io::Error::other(anyhow::anyhow!("closed"))));
        }
        self.project().stream.poll_shutdown(cx)
    }
    fn poll_flush(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), std::io::Error>> {
        if self.cancel.is_cancelled() {
            return Poll::Ready(Err(std::io::Error::other(anyhow::anyhow!("closed"))));
        }
        self.project().stream.poll_flush(cx)
    }
}

#[pin_project::pin_project]
pub struct TotStreamReader {
    #[pin]
    stream: OwnedReadHalf,
    _cancel: CancellationToken,
}

#[pin_project::pin_project]
pub struct TotStreamWriter {
    #[pin]
    stream: OwnedWriteHalf,
    _cancel: CancellationToken,
}

impl AsyncRead for TotStreamReader {
    fn poll_read(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &mut ReadBuf<'_>,
    ) -> Poll<std::io::Result<()>> {
        self.project().stream.poll_read(cx, buf)
    }
}
impl AsyncWrite for TotStreamWriter {
    fn poll_write(
        self: Pin<&mut Self>,
        cx: &mut Context<'_>,
        buf: &[u8],
    ) -> Poll<std::io::Result<usize>> {
        self.project().stream.poll_write(cx, buf)
    }
    fn poll_shutdown(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<std::io::Result<()>> {
        self.project().stream.poll_shutdown(cx)
    }
    fn poll_flush(self: Pin<&mut Self>, cx: &mut Context<'_>) -> Poll<Result<(), std::io::Error>> {
        self.project().stream.poll_flush(cx)
    }
}

impl SplitableIO for TotStream {
    type Reader = TotStreamReader;
    type Writer = TotStreamWriter;
    fn split(self) -> std::io::Result<(Self::Reader, Self::Writer)> {
        let (reader, writer) = self.stream.into_split();
        Ok((
            TotStreamReader {
                stream: reader,
                _cancel: self.cancel.clone(),
            },
            TotStreamWriter {
                stream: writer,
                _cancel: self.cancel,
            },
        ))
    }
}

impl InputIO for TotInputIO {
    type Conn = TotStream;

    async fn accept_conn(&mut self) -> std::io::Result<Self::Conn> {
        if self.cancel.is_cancelled() {
            return Err(std::io::Error::other(anyhow::anyhow!("closed")));
        }
        let conn = self
            .accept_rx
            .recv()
            .await
            .ok_or(std::io::Error::other(anyhow::anyhow!("closed")))?;
        Ok(conn)
    }
}

#[derive(Clone)]
pub struct TotBackendIO {
    fwd_handler: Arc<HammerHandlerForTot>,
    tot_server_selector: ServerSelector,
    cancel: CancellationToken,
    all_streams: Arc<Mutex<LruCache<StreamId, (Arc<Box<str>>, Address)>>>,
    tot_port_bind_trigger: Rechecker<tokio::sync::mpsc::Sender<()>>,
    tot_server_rechecker: Rechecker<ServerSelector>,
    tot_server_bind_mapper: Arc<RwLock<FwdToServerMapper>>,
    collector_factory: Arc<CollectorFactory>,
    bind_port: u16,
}

impl TotBackendIO {
    async fn get_tot_server_addr(
        self: &Self,
        all_streams: &mut LruCache<StreamId, (Arc<Box<str>>, Address)>,
        stream_id: StreamId,
    ) -> Result<(Arc<Box<str>>, Address), std::io::Error> {
        // 选择一个tot server
        let target = match self.tot_server_selector.get_target_info(None) {
            Ok(target) => target,
            Err(e) => {
                log::error!("get tot server failed: {}", e);
                return Err(std::io::Error::other(anyhow::anyhow!(
                    "get tot server failed: {}",
                    e
                )));
            }
        };
        log::info!("get tot server:{} from tot_server_selector", target.name);
        // 获取tot server的tot服务绑定地址
        let tot_server_addr = self
            .tot_server_bind_mapper
            .read()
            .await
            .get_bind_address(&target.name);
        if let Some(tot_server_addr) = tot_server_addr {
            if tot_server_addr.port() == 0 {
                return Err(std::io::Error::other(anyhow::anyhow!(
                    "get tot server:{} bind port is zero",
                    target.name
                )));
            }
            all_streams.insert(stream_id, (target.name.clone(), tot_server_addr.clone()));
            return Ok((target.name.clone(), tot_server_addr));
        } else {
            return Err(std::io::Error::other(anyhow::anyhow!(
                "get tot server:{} but get_bind_address return None",
                target.name
            )));
        }
    }
    pub async fn create_hammer_io(
        &self,
        name: Arc<Box<str>>,
        meta: StreamMeta,
    ) -> std::io::Result<MemDuplex> {
        log::debug!("create hammer io for stream: {}", meta.stream_id);
        let (dup_a, dup_b) = tokio::io::duplex(16384);
        let (tot_server_name, target) = {
            let mut all_streams = self.all_streams.lock().await;
            let addr = if meta.stream_id == 0xaabbccddddccbbaa {
                // 如果是心跳检测的流，则必须要重新选择
                None
            } else {
                all_streams.get(&meta.stream_id).cloned()
            };
            if let Some(addr) = addr {
                addr.clone()
            } else {
                // 重新选择tot服务器
                let addr = self
                    .get_tot_server_addr(&mut all_streams, meta.stream_id)
                    .await;
                match addr {
                    Err(e) => {
                        log::error!("get tot server addr failed: {}", e);
                        let tot_server_rechecker = self.tot_server_rechecker.clone();
                        let tot_port_bind_trigger = self.tot_port_bind_trigger.clone();
                        // 必须要分开，因为单独每个do_recheck可能会等待很久
                        tokio::spawn(async move {
                            // 检查forwarder 到 tot 服务器的链路是否正常
                            tot_server_rechecker.do_recheck().await;
                        });
                        tokio::spawn(async move {
                            // 触发更新tot 服务端口刷新
                            let _ = tot_port_bind_trigger.do_recheck().await;
                        });
                        return Err(e);
                    }
                    Ok(addr) => addr,
                }
            }
        };
        log::info!(
            "tot server addr: {:?} tot_server_name: {}",
            target,
            tot_server_name
        );
        let tot_server_name_removed_prefix = tot_server_name
            .split("tot-server-")
            .last()
            .map(|s| Arc::new(s.to_string().into_boxed_str()))
            .unwrap_or(tot_server_name.clone());
        let fwd_handler = self.fwd_handler.clone();
        let tot_server_rechecker = self.tot_server_rechecker.clone();
        let tot_port_bind_trigger = self.tot_port_bind_trigger.clone();
        let collector = self.collector_factory.clone().create_collector_group(
            smallvec![
                Some(name.clone()),
                Some(tot_server_name_removed_prefix.clone()),
                None,
            ],
            self.bind_port,
        );
        tokio::spawn(async move {
            let handle = collector; // explicit move handle
            let rst = fwd_handler
                .proc_tot_proxy_stream(dup_b, target, Some(name), Some(handle.collector.clone()))
                .await;
            if let Err(e) = rst {
                log::error!("proc tot proxy stream failed: {}", e);
                // 必须要分开，因为单独每个do_recheck可能会等待很久
                tokio::spawn(async move {
                    // 检查forwarder 到 tot 服务器的链路是否正常
                    tot_server_rechecker.do_recheck().await;
                });
                tokio::spawn(async move {
                    // 触发更新tot 服务端口刷新
                    let _ = tot_port_bind_trigger.do_recheck().await;
                });
            }
        });

        Ok(MemDuplex::new(dup_a, self.cancel.clone()))
    }
}

impl BackendIO for TotBackendIO {
    type I = MemDuplex;
    type S = Arc<Box<str>>;
    async fn create_io(&self, name: Self::S, meta: StreamMeta) -> std::io::Result<Self::I> {
        self.create_hammer_io(name, meta).await
    }
}

impl TotBackendIO {
    pub async fn create(
        tot_server_bind_mapper: Arc<RwLock<FwdToServerMapper>>,
        ping_client: Arc<PingClient>,
        fwd_handler: Arc<HammerHandlerForTot>,
        tot_server_select_mode: Mode,
        tot_server_test_method: LatencyTestMethod,
        tot_port_bind_trigger: Rechecker<tokio::sync::mpsc::Sender<()>>,
        cancel_token: CancellationToken,
        bind_port: u16,
        collector_factory: Arc<CollectorFactory>,
        rt_provider: Arc<RuntimeProvider>,
        conn_sem: Arc<Semaphore>,
    ) -> anyhow::Result<Self> {
        let server_list = {
            let lock = tot_server_bind_mapper.read().await;
            lock.map
                .iter()
                .map(|(server_name, (addr, _current_port))| {
                    ServerInfo::new(server_name.clone(), addr.clone(), None)
                })
                .collect::<Vec<_>>()
        };
        let tot_server_selector = Arc::new(
            ServerProviderImpl::new(
                server_list,
                Some(tot_server_select_mode),
                cancel_token.clone(),
                TotLatencyChecker::new(
                    ping_client,
                    tot_server_test_method,
                    fwd_handler.client.inbound_tx.clone(),
                    fwd_handler.client.config.0.clone(),
                    tot_server_bind_mapper.clone(),
                ),
                Some(rt_provider.get_rt().as_ref().clone()),
                conn_sem,
            )
            .await?,
        );

        Ok(Self {
            fwd_handler,
            cancel: cancel_token,
            tot_server_selector: tot_server_selector.clone(),
            all_streams: Arc::new(Mutex::new(LruCache::with_expiry_duration(
                Duration::from_secs(120),
            ))),
            tot_port_bind_trigger,
            tot_server_rechecker: Rechecker::new(tot_server_selector, Duration::from_secs(5)),
            tot_server_bind_mapper,
            collector_factory,
            bind_port,
        })
    }
}
