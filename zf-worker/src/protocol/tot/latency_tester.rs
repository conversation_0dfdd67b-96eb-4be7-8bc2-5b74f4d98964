use std::{
    net::{Ip<PERSON>ddr, SocketAddr},
    sync::Arc,
};

use anyhow::anyhow;
use async_ringbuf::traits::AsyncProducer;
use common::{
    app_message::LatencyTestMethod,
    into_socket_addr,
    server_provider::{LatencyTester, ServerInfo},
    PingClient,
};
use futures::{stream, StreamExt};
use private_tun::{
    address::Address,
    snell_impl_ver::{
        client_zfc::ConnType, config::ClientConfig, udp_intf::AsyncRingBufSender, TestLatencyMethod,
    },
};
use rand::Rng;
use tokio::{net::TcpStream, sync::RwLock};

use crate::constant::{FWD_TEST_TIMEOUT, TARGET_TEST_TIMEOUT};

use super::session::FwdToServerMapper;

#[derive(Clone)]
pub struct TotLatencyChecker {
    method: LatencyTestMethod,
    evt_tx: Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
    config: ClientConfig,
    ping_client: Arc<PingClient>,
    tot_server_bind_mapper: Arc<RwLock<FwdToServerMapper>>,
}

impl TotLatencyChecker {
    pub fn new(
        ping_client: Arc<PingClient>,
        method: LatencyTestMethod,
        evt_tx: Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
        config: ClientConfig,
        tot_server_bind_mapper: Arc<RwLock<FwdToServerMapper>>,
    ) -> Self {
        Self {
            method,
            evt_tx,
            config,
            ping_client,
            tot_server_bind_mapper,
        }
    }
}

pub async fn get_all_server_from_config(
    config: &ClientConfig,
) -> anyhow::Result<Vec<(Box<str>, SocketAddr)>> {
    let mut servers = Vec::new();
    // test main server
    if let Some(main_server_name) = config.name.as_ref() {
        let server_addr =
            tokio::net::lookup_host((config.server_addr.as_ref(), config.server_port))
                .await?
                .next()
                .ok_or(anyhow!("test latency: get server from config failed"))?;
        servers.push((main_server_name.clone(), server_addr));
    }
    // test backup server
    for backup_server in config
        .multi_server_config
        .as_ref()
        .map(|x| &x.back_servers)
        .into_iter()
        .flatten()
    {
        if let Some(backup_server_name) = backup_server.name.as_ref() {
            let server_addr = into_socket_addr(&backup_server.addr_port).await?;
            servers.push((backup_server_name.clone(), server_addr));
        }
    }
    Ok(servers)
}

async fn test_one_hammer_server_and_remote(
    fwd_server_name: &Box<str>,
    fwd_server_addr: &SocketAddr,
    remote: &Address,
    method: &LatencyTestMethod,
    ping_client: &Arc<PingClient>,
    evt_tx: &Arc<tokio::sync::Mutex<AsyncRingBufSender<ConnType>>>,
) -> Result<std::time::Duration, anyhow::Error> {
    let rand_num: u16 = rand::rng().random_range(0..65535);
    let fwd_server_name = Arc::new(fwd_server_name.clone());
    let server_name_clone = fwd_server_name.clone();
    let to_fwd_server = async {
        let latency = match &method {
            LatencyTestMethod::Tcpping => {
                let now = std::time::Instant::now();
                match tokio::time::timeout(
                    std::time::Duration::from_secs(FWD_TEST_TIMEOUT),
                    TcpStream::connect(fwd_server_addr),
                )
                .await
                {
                    Ok(_) => now.elapsed(),
                    Err(_) => {
                        log::error!(
                            "test latency: tcpping timeout for fwd server: {}",
                            server_name_clone
                        );
                        return Err(anyhow!(
                            "test latency: tcpping timeout for fwd server: {}",
                            server_name_clone
                        ));
                    }
                }
            }
            LatencyTestMethod::Icmp => {
                let client = match fwd_server_addr.ip() {
                    IpAddr::V4(_ip) => ping_client.v4_client(),
                    IpAddr::V6(_ip) => ping_client.v6_client(),
                };
                let mut pinger = client
                    .pinger(fwd_server_addr.ip(), surge_ping::PingIdentifier(rand_num))
                    .await;
                let rst = pinger.ping(surge_ping::PingSequence(0), &[0; 64]).await?;
                rst.1
            }
        };
        Ok::<_, anyhow::Error>(latency)
    };
    // fwd-server -> remote
    let to_remote = async {
        let (rst_tx, rst_rx) = tokio::sync::oneshot::channel();
        evt_tx
            .lock()
            .await
            .push(ConnType::TestLatency {
                rst_tx,
                target: remote.clone(),
                server_name: fwd_server_name.clone(),
                method: match &method {
                    LatencyTestMethod::Tcpping => TestLatencyMethod::Tcpping,
                    LatencyTestMethod::Icmp => TestLatencyMethod::Icmp,
                },
                piped_stream: None,
                reuse_tcp: false,
            })
            .await
            .map_err(|_| anyhow::anyhow!("push test latency error"))?;
        let rst = tokio::time::timeout(std::time::Duration::from_secs(TARGET_TEST_TIMEOUT), rst_rx)
            .await??;
        Ok::<_, anyhow::Error>(rst)
    };
    let (from_fwd_server, from_remote) = tokio::join!(to_fwd_server, to_remote);
    let from_fwd_server = match from_fwd_server {
        Ok(v) => v,
        Err(e) => {
            log::error!(
                "test latency from fwd server: {} failed: {e:?}",
                fwd_server_name
            );
            return Err(e);
        }
    };
    let from_remote = match from_remote {
        Ok(v) => match v {
            Ok(v) => v,
            Err(e) => {
                log::error!(
                    "test latency from remote: {} failed: {e:?} fwd server: {}",
                    remote,
                    fwd_server_name
                );
                return Err(e.into());
            }
        },
        Err(e) => {
            log::error!(
                "test latency from remote: {} failed: {e:?} fwd server: {}",
                remote,
                fwd_server_name
            );
            return Err(e);
        }
    };
    log::debug!(
            "tot latency measure for: {} to fwd server: {}ms, fwd server to remote: {:?}ms, total: {:?}ms",
            remote,
            from_fwd_server.as_millis(),
            from_remote / 1000,
            from_fwd_server.as_millis() + from_remote as u128 / 1000
        );
    Ok(std::time::Duration::from_micros(
        from_fwd_server.as_micros() as u64 + from_remote as u64,
    ))
}

impl LatencyTester for TotLatencyChecker {
    type Server = ServerInfo;
    async fn test_latency(
        &self,
        server: &Self::Server,
    ) -> Result<std::time::Duration, anyhow::Error> {
        // self -> hammer inbound
        let remote = match self
            .tot_server_bind_mapper
            .read()
            .await
            .get_bind_address(&server.name)
        {
            Some(addr) => addr,
            None => {
                log::error!("test tot latency: server: {} not found", server.name);
                return Err(anyhow!(
                    "test tot latency: server: {} not found",
                    server.name
                ));
            }
        };
        if remote.port() == 0 {
            // todo: trig tot bind setup again?
            log::error!(
                "test tot latency: no bind port found for server: {} remote: {}",
                server.name,
                remote
            );
            return Err(anyhow!(
                "test tot latency: no bind port found for server: {} remote: {}",
                server.name,
                remote
            ));
        }
        let method = &self.method;
        let all_fwd_servers = match get_all_server_from_config(&self.config).await {
            Ok(v) => v,
            Err(e) => {
                log::error!("test tot latency: get all fwd servers failed: {}", e);
                return Err(e);
            }
        };
        let total_tasks = all_fwd_servers.len();
        let (sum, count) = stream::iter(0..total_tasks)
            .map(|idx| {
                let (server_name, server_addr) = &all_fwd_servers[idx];
                test_one_hammer_server_and_remote(
                    &server_name,
                    &server_addr,
                    &remote,
                    &method,
                    &self.ping_client,
                    &self.evt_tx,
                )
            })
            .buffer_unordered(8)
            .fold((std::time::Duration::from_secs(0), 0), |(acc, count), r| {
                let r = if let Ok(v) = r {
                    (acc + v, count + 1)
                } else {
                    (acc, count)
                };
                std::future::ready(r)
            })
            .await;
        // let (sum, count) = rst;
        if count == 0 {
            log::error!(
                "test tot server: {} latency: no valid latency found",
                server.name
            );
            return Err(anyhow!(
                "test tot server: {} latency: no valid latency found",
                server.name
            ));
        }
        Ok(sum / count)
    }
}
