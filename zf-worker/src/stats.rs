use std::{collections::HashMap, time::Duration};

use anyhow::Result;
use common::app_message::StatsReport;
use sysinfo::{CpuRefreshKind, MemoryRefreshKind, Networks, RefreshKind, System};
pub struct NetcardStatsCollector {
    last_time: std::time::Instant,
    last_data: Option<HashMap<String, (u64, u64, u64, u64)>>,
    networks: Networks,
}

impl NetcardStatsCollector {
    pub fn new() -> Self {
        Self {
            last_time: std::time::Instant::now(),
            // intf: intf.to_string(),
            networks: Networks::new_with_refreshed_list(),
            last_data: None,
        }
    }
    pub fn collect_net_stats<'a>(
        &'a mut self,
    ) -> Result<&'a HashMap<String, (u64, u64, u64, u64)>> {
        let elapsed = self.last_time.elapsed();
        // if too short, return last data
        if elapsed.as_millis() >= 100 || self.last_data.is_none() {
            let mut result = HashMap::new();
            self.networks.refresh(true);
            for (interface, data) in self.networks.list() {
                let rx = data.received();
                let tx = data.transmitted();
                let rx_speed = rx as f64 / (elapsed.as_millis() + 1) as f64 * 1000.0;
                let tx_speed = tx as f64 / (elapsed.as_millis() + 1) as f64 * 1000.0;
                result.insert(
                    interface.clone(),
                    (
                        rx_speed as u64,
                        tx_speed as u64,
                        data.total_received(),
                        data.total_transmitted(),
                    ),
                );
            }
            self.last_time = std::time::Instant::now();
            self.last_data = Some(result);
        }
        Ok(self.last_data.as_ref().unwrap())
    }
}

pub struct SystemStatsCollector {
    sys: System,
}
pub struct SystemStats {
    cpu_usage: f32,
    memory_usage: (u64, u64), // (used, total) unit: byte
}
impl SystemStatsCollector {
    pub fn new() -> Self {
        let sys = System::new_with_specifics(
            RefreshKind::nothing()
                .with_cpu(CpuRefreshKind::nothing().with_cpu_usage())
                .with_memory(MemoryRefreshKind::everything()),
        );
        Self { sys }
    }
    pub fn collect_system_stats(&mut self) -> Result<SystemStats> {
        self.sys.refresh_cpu_usage();
        self.sys.refresh_memory();
        Ok(SystemStats {
            cpu_usage: self.sys.global_cpu_usage().round() as f32,
            memory_usage: (self.sys.used_memory(), self.sys.total_memory()),
        })
    }
}

pub struct StatsCollector {
    netcard_stats_collector: NetcardStatsCollector,
    system_stats_collector: SystemStatsCollector,
    report_interval: Duration,
}

impl StatsCollector {
    pub fn new(report_interval: Duration) -> Self {
        Self {
            netcard_stats_collector: NetcardStatsCollector::new(),
            system_stats_collector: SystemStatsCollector::new(),
            report_interval,
        }
    }
    pub fn get_report_interval(&self) -> Duration {
        self.report_interval
    }
    pub fn collect_all(&mut self, intf: &str) -> Result<StatsReport> {
        let net_stats = self.netcard_stats_collector.collect_net_stats()?;
        let mut netcard_speed = HashMap::new();
        for (interface, (rx, tx, total_rx, total_tx)) in net_stats.iter() {
            if interface == intf {
                netcard_speed.insert(interface.clone(), (*rx, *tx, *total_rx, *total_tx));
            }
        }
        let system_stats = self.system_stats_collector.collect_system_stats()?;
        Ok(StatsReport {
            netcard_speed,
            cpu_usage: system_stats.cpu_usage,
            memory_usage: system_stats.memory_usage,
            uptime: System::uptime(),
        })
    }
}
#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_netcard_speed_collector() {
        let mut collector = NetcardStatsCollector::new();
        let _ = collector.collect_net_stats();
    }
}
