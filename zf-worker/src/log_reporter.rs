use crate::http_client::HttpClient;
use chrono::{DateTime, Utc};
use http::{header, HeaderValue, Method};
use reqwest::Url;
use reqwest::{Body, Request};
use serde::{Deserialize, Serialize};
use thiserror::Error;
use tokio::sync::mpsc::Sender;

#[derive(Debug, Serialize, Deserialize, Clone, Copy)]
pub enum LogLevel {
    Debug,
    Info,
    Warning,
    Error,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct LogEntry {
    pub app_name: Box<str>,
    pub level: LogLevel,
    pub message: String,
    pub params: Vec<String>,
    pub timestamp: DateTime<Utc>,
}

#[derive(Error, Debug)]
pub enum LogClientError {
    #[error("HTTP client error: {0}")]
    HttpError(#[from] reqwest::Error),
    #[error("Too many parameters: maximum is 10")]
    TooManyParams,
    #[error("Failed to send log entry: {0}")]
    SendError(#[from] tokio::sync::mpsc::error::TrySendError<LogEntry>),
}

pub struct LogClient {
    app_name: Box<str>,
    log_tx: Sender<LogEntry>,
}
unsafe impl Send for LogClient {}
unsafe impl Sync for LogClient {}

fn create_json_request<T: Serialize>(
    method: Method,
    url: &Url,
    body: Option<T>,
) -> anyhow::Result<Request> {
    let host = url.host().ok_or(anyhow!("host not existed"))?.to_string();
    let mut request = Request::new(method, url.clone());
    request.headers_mut().insert(
        header::HOST,
        HeaderValue::from_str(&host)?,
    );
    if let Some(body) = body {
        let headers = request.headers_mut();
        headers.insert(
            header::CONTENT_TYPE,
            HeaderValue::from_static("application/json"),
        );
        let body = serde_json::to_string(&body)?;
        let body = Body::from(body);
        headers.insert(
            header::CONTENT_LENGTH,
            body.as_bytes().unwrap().len().into(),
        );
        *request.body_mut() = Some(body);
    }
    Ok(request)
}

impl LogClient {
    pub fn new(client: HttpClient, server_url: String, app_name: &str) -> Self {
        let (log_tx, mut log_rx) = tokio::sync::mpsc::channel(100);
        let url: Url = Url::parse(&format!("{}/api/log", server_url)).unwrap();
        tokio::spawn(async move {
            while let Some(entry) = log_rx.recv().await {
                if let Ok(request) =
                    create_json_request(Method::POST, &url, Some(entry))
                {
                    let _ = tokio::time::timeout(
                        std::time::Duration::from_secs(10),
                        client.request(request),
                    )
                    .await;
                }
            }
        });
        Self {
            app_name: app_name.to_string().into_boxed_str(),
            log_tx,
        }
    }

    pub fn log(
        &self,
        level: LogLevel,
        message: impl Into<String>,
        params: Vec<String>,
    ) -> Result<(), LogClientError> {
        if params.len() > 10 {
            return Err(LogClientError::TooManyParams);
        }

        let entry = LogEntry {
            app_name: self.app_name.clone(),
            level,
            message: message.into(),
            params,
            timestamp: Utc::now(),
        };

        if let Err(e) = self.log_tx.try_send(entry) {
            log::error!("Failed to send log entry: {:?}", e);
            return Err(LogClientError::SendError(e));
        };
        Ok(())
    }
    #[allow(unused)]
    pub fn debug(
        self: &Self,
        message: impl Into<String> + Send + Sync + 'static,
        params: Vec<String>,
    ) {
        self.log(LogLevel::Debug, message, params);
    }

    #[allow(unused)]
    pub fn info(
        self: &Self,
        message: impl Into<String> + Send + Sync + 'static,
        params: Vec<String>,
    ) {
        self.log(LogLevel::Info, message, params);
    }

    #[allow(unused)]
    pub fn warning(
        self: &Self,
        message: impl Into<String> + Send + Sync + 'static,
        params: Vec<String>,
    ) {
        self.log(LogLevel::Warning, message, params);
    }

    #[allow(unused)]
    pub fn error(
        self: &Self,
        message: impl Into<String> + Send + Sync + 'static,
        params: Vec<String>,
    ) {
        self.log(LogLevel::Error, message, params);
    }
}
