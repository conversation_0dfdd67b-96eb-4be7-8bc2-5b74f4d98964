use std::{
    collections::HashMap,
    sync::{
        atomic::{AtomicUsize, Ordering},
        Arc,
    },
};

use private_tun::traffic_collector::RealtimeTrafficCollector;
use tokio::sync::{mpsc::Sender, RwLock};
use tokio_util::sync::{CancellationToken, DropGuard};

use crate::preludes::LocalStatData;

#[derive(Clone)]
pub struct Handle {
    pub factory: Arc<CollectorFactory>,
    pub collector: Arc<RealtimeTrafficCollector>,
    idx: usize,
    main_rt: tokio::runtime::Handle,
}
impl Handle {
    pub fn idx(&self) -> usize {
        self.idx
    }
}
impl Drop for Handle {
    fn drop(&mut self) {
        let factory = self.factory.clone();
        let idx = self.idx;
        self.main_rt.spawn(async move {
            let mut all_collectors = factory.all_collectors.write().await;
            report(&all_collectors, &factory.stats_tx, idx).await;

            all_collectors.remove(&idx);
        });
    }
}
struct Item {
    collector: Arc<RealtimeTrafficCollector>,
    name: Option<Arc<Box<str>>>,
    port: u16,
    related_item: Option<Vec<Item>>,
}
pub struct CollectorFactory {
    all_collectors: Arc<RwLock<HashMap<usize, Item>>>,
    idx: Arc<AtomicUsize>,
    _cancel_token: DropGuard,
    stats_tx: Sender<LocalStatData>,
    main_rt: tokio::runtime::Handle,
}

impl CollectorFactory {
    pub fn new(stats_tx: Sender<LocalStatData>, main_rt: tokio::runtime::Handle) -> Self {
        let cancel_token = CancellationToken::new();
        let cancel_token_clone = cancel_token.clone();
        let all_collectors: Arc<RwLock<HashMap<usize, Item>>> =
            Arc::new(RwLock::new(HashMap::new()));
        let all_collectors_clone = all_collectors.clone();
        let stats_tx_clone = stats_tx.clone();
        main_rt.spawn(async move {
            let mut cnt: u64 = 0;
            loop {
                tokio::select! {
                    _ = cancel_token_clone.cancelled() => {
                        log::debug!("CollectorFactory cancelled");
                        break;
                    }
                    _ = tokio::time::sleep(std::time::Duration::from_secs(10)) => {
                        {
                            let lock = all_collectors_clone.read().await;
                            for id in lock.keys() {
                                report(&lock, &stats_tx_clone, *id).await;
                            }
                        }
                        // should never happen for over flow, but just in case
                        cnt = cnt.wrapping_add(1);
                        if cnt % 60 == 0 {
                            all_collectors_clone.write().await.shrink_to_fit();
                        }
                    }
                }
            }
        });
        Self {
            all_collectors: all_collectors.clone(),
            idx: Arc::new(AtomicUsize::new(0)),
            _cancel_token: cancel_token.drop_guard(),
            stats_tx,
            main_rt,
        }
    }
    fn create_handle(
        self: Arc<Self>,
        name: Option<Arc<Box<str>>>,
        port: u16,
        main_rt: tokio::runtime::Handle,
    ) -> (Handle, Item) {
        let collector = Arc::new(RealtimeTrafficCollector::new());
        let idx = self.idx.fetch_add(1, Ordering::Relaxed);
        let item = Item {
            collector: collector.clone(),
            name,
            port,
            related_item: None,
        };
        (
            Handle {
                factory: self,
                collector,
                idx,
                main_rt,
            },
            item,
        )
    }

    pub fn create_collector_group(
        self: Arc<Self>,
        mut names: smallvec::SmallVec<[Option<Arc<Box<str>>>; 3]>,
        port: u16,
    ) -> Handle {
        let mut related_items = vec![];
        let main_name = names.remove(0);
        let (main_h, mut main_item) =
            self.clone()
                .create_handle(main_name, port, self.main_rt.clone());
        for name in names {
            let (_h, item) = self.clone().create_handle(name, port, self.main_rt.clone());
            related_items.push(item);
        }
        main_item.related_item = Some(related_items);
        let idx = main_h.idx;
        // delay add to prevent await
        let main_rt = self.main_rt.clone();
        main_rt.spawn(async move {
            add_handler(self, idx, main_item).await;
        });
        main_h
    }
}

async fn report_to_stats(item: &Item, total_bytes: u64, stats_tx: &Sender<LocalStatData>) {
    log::debug!(
        "report traffic: {} for name: {:?} port: {}",
        total_bytes,
        item.name.clone(),
        item.port
    );
    if let Err(e) = stats_tx
        .send(LocalStatData {
            forward_endpoint: item.name.clone(),
            traffic: total_bytes,
            port: item.port,
        })
        .await
    {
        log::error!("Failed to send stats: {}", e);
    }
}
async fn report(locked: &HashMap<usize, Item>, stats_tx: &Sender<LocalStatData>, idx: usize) {
    let Some(item) = locked.get(&idx) else {
        return;
    };
    let total_bytes = item.collector.collect_total_bytes();
    if total_bytes > 0 {
        report_to_stats(item, total_bytes, stats_tx).await;
        for related_item in item.related_item.iter().flatten() {
            report_to_stats(related_item, total_bytes, stats_tx).await;
        }
    } else {
        log::debug!(
            "no traffic to report for collector: {idx} port: {} name: {:?}",
            item.port,
            item.name
        );
    }
}

async fn add_handler(factory: Arc<CollectorFactory>, main_idx: usize, main_item: Item) {
    let mut lock = factory.all_collectors.write().await;
    lock.insert(main_idx, main_item);
}
