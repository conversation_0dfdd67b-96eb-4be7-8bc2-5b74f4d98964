# Deployment Guide

This guide explains how to deploy the application using either separate deployment or embedded deployment approaches.

## Overview

The application supports two deployment modes:

1. **Separate Deployment**: Frontend and backend deployed independently
2. **Embedded Deployment**: Frontend embedded in backend binary

Both approaches are fully supported and maintain complete compatibility.

## Deployment Scenarios

### Scenario A: Separate Deployment

**Use this when:**
- Deploying to different servers/containers
- Need independent scaling of frontend/backend
- Using CDN for frontend assets
- Different teams manage frontend/backend
- Microservices architecture

**Steps:**

1. **Deploy Frontend (Docker)**:
   ```bash
   cd zfc-web-ui
   ./deploy.sh http://your-backend-server:3030/api 8080
   ```
   This creates a Docker container serving the frontend on port 8080.

2. **Deploy Backend (API-Only)**:
   ```bash
   cd zf-web
   cargo build --release
   ./target/release/zf-web
   ```
   Backend runs on port 3030 serving only API endpoints.

**Result**: Frontend at `http://frontend-server:8080`, Backend at `http://backend-server:3030`

### Scenario B: Embedded Deployment

**Use this when:**
- Single server deployment
- Simplified deployment process desired
- Reduced infrastructure complexity
- Edge computing or resource-constrained environments
- Single binary distribution preferred

**Steps:**

1. **Build with Embedded Frontend**:
   ```bash
   cd zf-web
   cargo build --release --features embed
   ```

2. **Deploy Single Binary**:
   ```bash
   ./target/release/zf-web
   ```

**Result**: Both frontend and backend served from `http://server:3030`

## Configuration

### Environment Variables

Both deployment modes support the same backend configuration:

```bash
# Backend configuration
BIND_ADDRESS=0.0.0.0
BACKEND_PORT=3030
DB_PATH=****************************
REDIS_PATH=redis://host:6379
JWT_SECRET=your-secret-key

# For separate deployment, frontend needs backend URL
VITE_API_URL=http://backend-server:3030/api
```

### Build Configuration

```bash
# Separate deployment (default)
cargo build --release

# Embedded deployment
cargo build --release --features embed
```

### Frontend Configuration

**Separate Deployment**: Configure API URL during Docker build
```bash
./deploy.sh http://backend-server:3030/api
```

**Embedded Deployment**: No additional configuration needed

## API Compatibility

**All API endpoints remain unchanged:**
- `/api/login` - Authentication
- `/api/ports` - Port management
- `/api/subscription` - Subscription info
- `/api/forward_endpoints` - Forward endpoints
- All other existing endpoints

**CORS settings are preserved** for cross-origin requests in separate deployment.

## Development Workflow

### Development Mode (Recommended for Development)

```bash
# Option 1: Embedded mode (single server)
cd zf-web
cargo run --features embed

# Option 2: Separate mode (backend API-only)
cd zf-web
cargo run

# Terminal 2: Start frontend dev server separately
cd zfc-web-ui
npm run dev
```

### Production Testing

```bash
# Test embedded mode locally
cd zf-web
cargo run --features embed

# Test separate deployment mode
cd zf-web
cargo run  # Backend API-only
```

## Migration Guide

### From Separate to Embedded

1. **No code changes required**
2. **Build with embed feature**: `cargo build --release --features embed`
3. **Deploy single binary**: `./target/release/zf-web`
4. **Update load balancer/proxy** to point to single server

### From Embedded to Separate

1. **No code changes required**
2. **Build without embed feature**: `cargo build --release`
3. **Deploy frontend separately**: Use existing `deploy.sh` script
4. **Configure frontend API URL** to point to backend server

## Troubleshooting

### Common Issues

**Frontend assets not loading (embedded mode)**:
- Ensure frontend was built: `cd ../zfc-web-ui && npm run build`
- Check build logs for frontend build errors
- Verify `dist` directory exists and contains files

**API calls failing (separate mode)**:
- Check CORS configuration
- Verify API URL in frontend configuration
- Ensure backend is accessible from frontend server

**Build failures**:
- Run `cargo clean` and rebuild
- Ensure Node.js and npm are installed for embedded builds
- Check frontend dependencies: `cd ../zfc-web-ui && npm install`

### Performance Considerations

**Embedded Mode**:
- Larger binary size (includes all frontend assets)
- Faster startup (no external dependencies)
- Better for single-server deployments

**Separate Mode**:
- Smaller backend binary
- Independent scaling
- Better for microservices architecture

## Security Considerations

Both deployment modes maintain the same security model:
- JWT authentication for API access
- CORS protection for cross-origin requests
- Path traversal protection for static files
- Same rate limiting and security headers

## Monitoring and Logging

**Embedded Mode**: Single service to monitor
**Separate Mode**: Monitor frontend and backend independently

Both modes support the same logging configuration and health check endpoints.
