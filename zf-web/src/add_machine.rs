use anyhow::anyhow;
use base64::Engine;
use common::{
    crypto::{random_key, stringify_public_key_from_secret_key, stringify_secret_key},
    replace_placeholders,
};
use std::{collections::HashMap, sync::Arc};

use super::prisma;
use crate::prisma::PrismaClient;

pub async fn add_machine(
    db: Arc<PrismaClient>,
    name: &str,
    ip: &str,
    port_start: i32,
    port_end: i32,
    traffic_scale: f32,
    admin_token_id: &str,
    interface_name: &str,
    allow_forward: bool,
    allow_latency_test: bool,
    port_forward_config: Option<(String, Vec<i32>)>,
    use_forward_as_tun: bool,
) -> anyhow::Result<()> {
    let key = random_key();
    let pub_key_str = stringify_public_key_from_secret_key(&key);
    let priv_key_str = stringify_secret_key(&key);

    let mut set_args = vec![
        prisma::outbound_endpoint::port_start::set(Some(port_start)),
        prisma::outbound_endpoint::port_end::set(Some(port_end)),
        prisma::outbound_endpoint::traffic_scale::set(Some(traffic_scale as f64)),
        prisma::outbound_endpoint::interface_name::set(Some(interface_name.to_string())),
        prisma::outbound_endpoint::allow_forward::set(Some(allow_forward)),
        prisma::outbound_endpoint::allow_latency_test::set(Some(allow_latency_test)),
        prisma::outbound_endpoint::private_key::set(Some(priv_key_str.clone())),
    ];

    if let Some((proxy_config, related_forward_endpoint_ids)) = port_forward_config {
        set_args.push(prisma::outbound_endpoint::proxy_config::set(Some(
            base64::engine::general_purpose::STANDARD.encode(proxy_config),
        )));
        set_args.push(
            prisma::outbound_endpoint::related_forward_endpoint_ids::set(
                related_forward_endpoint_ids,
            ),
        );
        set_args.push(prisma::outbound_endpoint::use_forward_as_tun::set(Some(
            use_forward_as_tun,
        )));
    }

    let data = match db
        .outbound_endpoint()
        .create(
            name.to_string(),
            pub_key_str.clone(),
            name.to_string(),
            ip.to_string(),
            set_args,
        )
        .exec()
        .await
    {
        Ok(v) => v,
        Err(e) => {
            log::error!("add machine: {} failed: {}", name, e);
            return Err(anyhow!("add machine: {} failed: {}", name, e));
        }
    };
    // add this machine to admin's line
    db.subscription()
        .update(
            prisma::subscription::token_id::equals(admin_token_id.to_string()),
            vec![prisma::subscription::lines::push(vec![data.id])],
        )
        .exec()
        .await?;
    Ok(())
}

pub async fn generate_worker_setup_script(
    db: Arc<PrismaClient>,
    worker_pub_key: &str,
    mgmt_pubkey: &str,
    mgmt_url: &str,
) -> anyhow::Result<String> {
    let data = db
        .outbound_endpoint()
        .find_unique(prisma::outbound_endpoint::pubkey::equals(
            worker_pub_key.to_owned(),
        ))
        .exec()
        .await?
        .ok_or(anyhow!("machine not found"))?;
    let config_file_name = "install_zfc_worker.sh";
    let script_content = tokio::fs::read_to_string(config_file_name).await?;
    let port_start = data.port_start.unwrap_or(30000).to_string();
    let port_end = data.port_end.unwrap_or(31000).to_string();
    let private_key = data.private_key.unwrap_or("".to_string());
    let mgmt_hammer_proxy = data.proxy_config.unwrap_or("".to_string());
    let outbound_proxy_addr = if data.use_forward_as_tun.unwrap_or(false) {
        "use_proxy".to_string()
    } else {
        "direct".to_string()
    };
    let replacements = HashMap::from([
        ("MGMT_ARRANGER_PUBLIC_KEY_STR", mgmt_pubkey),
        ("ARRANGER_URL_STR", mgmt_url),
        ("WORKER_PUBKEY", worker_pub_key),
        ("PORT_START_STR", &port_start),
        ("PORT_END_STR", &port_end),
        ("PRIVATE_KEY_STR", &private_key),
        ("MGMT_HAMMER_PROXY_STR", &mgmt_hammer_proxy),
        ("OUTBOUND_PROXY_ADDR_STR", &outbound_proxy_addr),
    ]);
    let script = replace_placeholders(&script_content, &replacements);
    Ok(script)
}

pub async fn remove_line(db: Arc<PrismaClient>, line_id: i32) -> anyhow::Result<()> {
    db._transaction()
        .run(|tx| async move {
            let line = tx
                .outbound_endpoint()
                .find_unique(prisma::outbound_endpoint::id::equals(line_id))
                .select(prisma::outbound_endpoint::select!({ id }))
                .exec()
                .await?
                .ok_or_else(|| anyhow!("line not found"))?;
            // delete all ports for this line
            let _ = tx
                .port()
                .delete_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                    line_id,
                ))])
                .exec()
                .await?;

            // deleted this line for subscription who has this line
            let subs = tx
                .subscription()
                .find_many(vec![prisma::subscription::lines::has_some(vec![line.id])])
                .select(prisma::subscription::select!({
                    id
                    lines
                }))
                .exec()
                .await?;
            for s in subs.iter() {
                let new_lines = s
                    .lines
                    .iter()
                    .filter(|x| *x != &line.id)
                    .map(|x| *x)
                    .collect::<Vec<_>>();
                let _ = tx
                    .subscription()
                    .update(
                        prisma::subscription::id::equals(s.id),
                        vec![prisma::subscription::lines::set(new_lines)],
                    )
                    .exec()
                    .await?;
            }
            // delete this line
            tx.outbound_endpoint()
                .delete(prisma::outbound_endpoint::id::equals(line.id))
                .exec()
                .await?;
            Ok::<(), anyhow::Error>(())
        })
        .await?;
    Ok(())
}
