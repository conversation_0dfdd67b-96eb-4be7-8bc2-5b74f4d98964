mod error;
mod handlers;
use common::{
    prisma,
    reset_traffic_task::{ArcResetTrafficContext, ResetTrafficContext},
    retry_queue::RetryQueue,
    RedisLineLock,
};
pub mod add_machine;
pub mod add_user;
pub mod forwarder;
pub mod message;
pub mod stats;
pub mod tot;

use anyhow::anyhow;
use bb8_redis::{bb8::Pool, RedisConnectionManager};
use common::{cache::Cache, redis_lock::DistributedLock};
use futures::future::BoxFuture;
use handlers::{
    handle_add_forward_endpoint, handle_add_server, handle_add_user, handle_create_port,
    handle_edit_user, handle_extend_subscription_time, handle_get_all_server_list,
    handle_get_forward_endpoint, handle_get_forward_endpoints_with_search, handle_get_line_stats,
    handle_get_ports_with_search, handle_get_server_list, handle_get_server_list_with_search,
    handle_get_setup_script, handle_get_subscription_list_with_search,
    handle_get_worker_setup_script, handle_login, handle_modify_forward_endpoint,
    handle_modify_port, handle_modify_server, handle_reset_user_traffic, handle_resume_port,
    handle_rmv_forward_endpoint, handle_rmv_port, handle_rmv_server, handle_rmv_user,
    handle_subscription_info, handle_suspend_port, handle_test_latency,
};
use jsonwebtoken::{decode, DecodingKey, Validation};
use message::{Claims, RmvServerRequest, RmvUserRequest, TestLatencyRequest};
use prisma::{subscription, PrismaClient};
use reqwest::Url;
use std::{
    collections::{HashMap, HashSet},
    convert::Infallible,
    net::IpAddr,
    sync::Arc,
};
use tokio::sync::Mutex;
use tracing::info;
use warp::{Filter, Rejection};

#[cfg(feature = "embed")]
use rust_embed::RustEmbed;

#[cfg(feature = "embed")]
use warp::Reply;

use clap::Parser;

use influxdb::Client as InfluxDBClient;

#[derive(Debug, Parser, Clone)]
pub struct Opt {
    #[clap(short = 'b', long, env, default_value = "127.0.0.1")]
    pub bind_address: String,
    #[clap(short = 'p', long, env, default_value_t = 3030)]
    pub backend_port: u16,
    #[clap(
        short,
        long,
        env,
        default_value = "postgres://postgres:postgres@127.0.0.1/datium"
    )]
    pub db_path: String,
    #[clap(short, long, env, default_value = "redis://127.0.0.1:6379")]
    pub redis_path: String,
    #[clap(short, long, env, default_value = "http://127.0.0.1:8086")]
    pub influxdb_url: String,
    #[clap(long, env, default_value = "")]
    pub influxdb_db: String,
    #[clap(long, env, default_value = "")]
    pub influxdb_user: String,
    #[clap(long, env, default_value = "")]
    pub influxdb_password: String,
    #[clap(long, env)]
    pub host_url: String,
    #[clap(long, env, default_value = "downloads")]
    pub app_download_dir: String,
    #[clap(long, env, default_value = "http://127.0.0.1:3100")]
    pub mgmt_url: Url,
    #[clap(long, env)]
    pub mgmt_pubkey: String,
}

pub type RedisPool = Pool<RedisConnectionManager>;

pub async fn setup_redis(opt: &Opt) -> anyhow::Result<RedisPool> {
    let manager = RedisConnectionManager::new(opt.redis_path.as_str())?;
    Ok(Pool::builder().build(manager).await?)
}

pub fn setup_influxdb(opt: &Opt) -> anyhow::Result<InfluxDBClient> {
    let client = InfluxDBClient::new(opt.influxdb_url.as_str(), opt.influxdb_db.as_str())
        .with_auth(opt.influxdb_user.as_str(), opt.influxdb_password.as_str());
    Ok(client)
}

#[derive(Clone)]
pub struct AppState {
    pub db: Arc<PrismaClient>,
    pub redis_pool: RedisPool,
    pub redis_lock: Arc<DistributedLock>,
    pub redis_line_lock: RedisLineLock,
    pub influxdb_client: InfluxDBClient,
    pub cache_sub_lines: Arc<Cache<String, Vec<i32>>>, // token_id -> lines
    pub cache_outbound_endpoint_data: Arc<Cache<i32, prisma::outbound_endpoint::Data>>,
    pub admin_tokens: Arc<HashSet<String>>,
    pub opt: Opt,
    pub web_client: Arc<reqwest::Client>,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    dotenv::dotenv().ok();

    // Initialize logging
    if std::env::var_os("RUST_LOG").is_none() {
        std::env::set_var("RUST_LOG", "info");
    }
    tracing_subscriber::fmt::init();

    let opt = Opt::parse();

    // Create Prisma client
    let client = Arc::new(prisma::new_client_with_url(&opt.db_path).await?);

    let redis_pool = setup_redis(&opt).await?;
    let influxdb_client = setup_influxdb(&opt)?;
    let db_clone = client.clone();
    let db_clone2 = client.clone();
    let download_path = opt.app_download_dir.clone();
    let admin_tokens = client
        .subscription()
        .find_many(vec![prisma::subscription::is_admin::equals(true)])
        .exec()
        .await?
        .into_iter()
        .map(|x| x.token_id)
        .collect::<HashSet<String>>();

    let reset_traffic_ctx = Arc::new(ResetTrafficContext::new(
        db_clone.clone(),
        redis_pool.clone(),
        Arc::new(RetryQueue::new()),
        Arc::new(DistributedLock::new(
            redis_pool.clone(),
            "traffic_lock",
            5000,
        )),
        Arc::new(tokio::sync::Mutex::new(std::collections::HashMap::new())),
    ));
    let app_state = AppState {
        db: client.clone(),
        redis_pool: redis_pool.clone(),
        redis_lock: reset_traffic_ctx.redis_lock.clone(),
        redis_line_lock: Arc::new(Mutex::new(std::collections::HashMap::new())),
        influxdb_client: influxdb_client.clone(),
        admin_tokens: Arc::new(admin_tokens),
        web_client: Arc::new(reqwest::Client::new()),
        cache_sub_lines: Arc::new(Cache::new(
            Box::new(
                move |token_id: &String| -> BoxFuture<'static, Result<Vec<i32>, anyhow::Error>> {
                    let db_clone1 = db_clone.clone();
                    let token_id_clone = token_id.clone();
                    Box::pin(async move {
                        let lines = db_clone1
                            .subscription()
                            .find_unique(subscription::token_id::equals(token_id_clone))
                            .exec()
                            .await?
                            .ok_or_else(|| anyhow::anyhow!("Subscription not found"))?
                            .lines;
                        Ok(lines)
                    })
                },
            )
                as Box<
                    dyn Fn(&String) -> BoxFuture<'static, Result<Vec<i32>, anyhow::Error>>
                        + Send
                        + Sync
                        + 'static,
                >,
            std::time::Duration::from_secs(30),
        )),
        cache_outbound_endpoint_data: Arc::new(Cache::new(
            Box::new(
                move |outbound_endpoint_id: &i32| -> BoxFuture<
                    'static,
                    Result<prisma::outbound_endpoint::Data, anyhow::Error>,
                > {
                    let db_clone2_clone = db_clone2.clone();
                    let outbound_endpoint_id_clone = outbound_endpoint_id.clone();
                    Box::pin(async move {
                        let outbound_endpoint = db_clone2_clone
                            .outbound_endpoint()
                            .find_unique(prisma::outbound_endpoint::id::equals(
                                outbound_endpoint_id_clone,
                            ))
                            .exec()
                            .await?
                            .ok_or_else(|| anyhow::anyhow!("Outbound endpoint not found"))?;
                        Ok(outbound_endpoint)
                    })
                },
            )
                as Box<
                    dyn Fn(
                            &i32,
                        ) -> BoxFuture<
                            'static,
                            Result<prisma::outbound_endpoint::Data, anyhow::Error>,
                        > + Send
                        + Sync
                        + 'static,
                >,
            std::time::Duration::from_secs(30),
        )),
        opt: opt.clone(),
    };

    // CORS configuration
    let cors = warp::cors()
        .allow_any_origin()
        .allow_headers(vec![
            "Content-Type",
            "Authorization",
            "Access-Control-Allow-Headers",
            "Access-Control-Request-Method",
            "Access-Control-Request-Headers",
            "Origin",
        ])
        .allow_methods(vec!["POST", "GET", "DELETE", "OPTIONS", "PUT"])
        .allow_credentials(true);

    // Log filter
    let log = warp::log::custom(|info| {
        info!(
            "{} {} {} {}",
            info.method(),
            info.path(),
            info.status(),
            info.elapsed().as_millis()
        );
    });

    // Define routes
    let api = warp::path("api");

    // Login route (unprotected)
    let login = api
        .and(warp::path("login"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(handle_login);

    // Protected routes
    let auth = api.and(with_auth());
    let forward_endpoint_auth = api.and(with_forward_endpoint_and_auth(app_state.clone()));

    let check_admin_filter = api
        .and(with_auth())
        .and(with_app_state(app_state.clone()))
        .and_then(move |token_id: String, app_state: AppState| {
            async move {
                if app_state.admin_tokens.contains(&token_id) {
                    Ok(token_id)
                } else {
                    // 校验失败，抛出自定义拒绝
                    Err(warp::reject::not_found())
                }
            }
        });

    let subscription_info = auth
        .clone()
        .and(warp::path("subscription"))
        .and(warp::get())
        .and(with_app_state(app_state.clone()))
        .and_then(handle_subscription_info);

    let ports = auth
        .clone()
        .and(warp::path("ports"))
        .and(warp::get())
        .and(warp::query::<HashMap<String, String>>())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, query_params: HashMap<String, String>, app_state| {
            let search_request = if query_params.is_empty() {
                None
            } else {
                Some(message::PortSearchRequest {
                    page: query_params.get("page").and_then(|p| p.parse().ok()),
                    page_size: query_params.get("page_size").and_then(|p| p.parse().ok()),
                    id: query_params.get("id").and_then(|p| p.parse().ok()),
                    name: query_params.get("name").map(|s| s.to_string()),
                    line: query_params.get("line").and_then(|p| p.parse().ok()),
                    entry_point: query_params.get("entry_point").map(|s| s.to_string()),
                    target: query_params.get("target").map(|s| s.to_string()),
                })
            };
            handle_get_ports_with_search(token, search_request, app_state)
        });

    let create_port = auth
        .clone()
        .and(warp::path("ports"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, client| {
            info!("Create port request: {:?}", body);
            handle_create_port(token, body, client)
        });

    let rmv_port = auth
        .clone()
        .and(warp::path("ports"))
        .and(warp::delete())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| {
            info!("Remove port request: {:?}", body);
            handle_rmv_port(token, body, app)
        });

    let modify_port = auth
        .clone()
        .and(warp::path("mod_port"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| {
            info!("Modify port request: {:?}", body);
            handle_modify_port(token, body, app)
        });

    let suspend_port = auth
        .clone()
        .and(warp::path("suspend_port"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| {
            info!("Suspend port request: {:?}", body);
            handle_suspend_port(token, body, app)
        });

    let resume_port = auth
        .clone()
        .and(warp::path("resume_port"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| {
            info!("Resume port request: {:?}", body);
            handle_resume_port(token, body, app)
        });

    let get_line_stats = auth
        .clone()
        .and(warp::path("line_stats"))
        .and(warp::get())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, app| handle_get_line_stats(token, app));

    let add_forward_endpoint = forward_endpoint_auth
        .clone()
        .and(warp::path("forward_endpoints"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| handle_add_forward_endpoint(token, body, app));

    let rmv_forward_endpoint = forward_endpoint_auth
        .clone()
        .and(warp::path("forward_endpoints"))
        .and(warp::delete())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| handle_rmv_forward_endpoint(token, body, app));

    let forward_endpoints = forward_endpoint_auth
        .clone()
        .and(warp::path("forward_endpoints"))
        .and(warp::get())
        .and(warp::query::<HashMap<String, String>>())
        .and(with_app_state(app_state.clone()))
        .and_then(
            |token, query_params: HashMap<String, String>, app| async move {
                // Check if search parameters are present
                let has_search_params = query_params.get("id").is_some()
                    || query_params.get("name").is_some()
                    || query_params.get("ingress_address").is_some();

                if has_search_params {
                    // Use search handler
                    let search_request = Some(message::ForwardEndpointSearchRequest {
                        page: query_params.get("page").and_then(|p| p.parse().ok()),
                        page_size: query_params.get("page_size").and_then(|p| p.parse().ok()),
                        id: query_params.get("id").and_then(|p| p.parse().ok()),
                        name: query_params.get("name").map(|s| s.to_string()),
                        ingress_address: query_params.get("ingress_address").map(|s| s.to_string()),
                    });
                    handle_get_forward_endpoints_with_search(token, search_request, app).await
                } else {
                    // Use original handler for backward compatibility
                    let pagination = if query_params.is_empty() {
                        None
                    } else {
                        Some(message::PaginationRequest {
                            page: query_params.get("page").and_then(|p| p.parse().ok()),
                            page_size: query_params.get("page_size").and_then(|p| p.parse().ok()),
                            fetch_all: query_params.get("fetch_all").and_then(|p| p.parse().ok()),
                        })
                    };
                    handle_get_forward_endpoint(token, pagination, app).await
                }
            },
        );

    let modify_forward_endpoint = forward_endpoint_auth
        .clone()
        .and(warp::path("mod_forward_endpoint"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| handle_modify_forward_endpoint(token, body, app));

    let get_setup_script = warp::path!("setup_script" / String)
        .and(warp::get())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, app| handle_get_setup_script(token, app));

    let get_worker_setup_script = warp::path!("worker_setup_script" / String)
        .and(warp::get())
        .and(with_app_state(app_state.clone()))
        .and_then(|worker_pubkey, app| handle_get_worker_setup_script(worker_pubkey, app));

    let download_dirs = warp::path("downloads")
        .and(warp::get())
        .and(warp::fs::dir(download_path));

    // Frontend asset serving (conditional)
    #[cfg(feature = "embed")]
    let frontend_routes = create_frontend_routes(&opt);

    let test_latency = auth
        .clone()
        .and(warp::path("test_latency"))
        .and(warp::query::<TestLatencyRequest>())
        .and(warp::get())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| handle_test_latency(token, body, app));

    // admin routes
    let subscription_list = check_admin_filter
        .clone()
        .and(warp::path("subscription_list"))
        .and(warp::get())
        .and(warp::query::<HashMap<String, String>>())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, query_params: HashMap<String, String>, app| {
            let search_request = if query_params.is_empty() {
                None
            } else {
                Some(message::SubscriptionSearchRequest {
                    page: query_params.get("page").and_then(|p| p.parse().ok()),
                    page_size: query_params.get("page_size").and_then(|p| p.parse().ok()),
                    id: query_params.get("id").and_then(|p| p.parse().ok()),
                    token_id: query_params.get("token_id").map(|s| s.to_string()),
                    email: query_params.get("email").map(|s| s.to_string()),
                    valid_until_start: query_params.get("valid_until_start").map(|s| s.to_string()),
                    valid_until_end: query_params.get("valid_until_end").map(|s| s.to_string()),
                    next_reset_start: query_params.get("next_reset_start").map(|s| s.to_string()),
                    next_reset_end: query_params.get("next_reset_end").map(|s| s.to_string()),
                    lines: {
                        let lines_params: Vec<i32> = query_params
                            .iter()
                            .filter(|(key, _)| key == &"lines")
                            .filter_map(|(_, value)| value.parse().ok())
                            .collect();
                        if lines_params.is_empty() {
                            None
                        } else {
                            Some(lines_params)
                        }
                    },
                })
            };
            handle_get_subscription_list_with_search(search_request, app)
        });

    let add_user = check_admin_filter
        .clone()
        .and(warp::path("add_user"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, body, app| handle_add_user(app, body));

    let edit_user = check_admin_filter
        .clone()
        .and(warp::path("edit_user"))
        .and(warp::put())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, body, app| handle_edit_user(app, body));

    let rmv_user = check_admin_filter
        .clone()
        .and(warp::path("rmv_user"))
        .and(warp::query::<RmvUserRequest>())
        .and(warp::delete())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, rmv_user_request, app| handle_rmv_user(app, rmv_user_request));

    let extend_subscription_time = check_admin_filter
        .clone()
        .and(warp::path("extend_subscription_time"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, extend_subscription_time_request, app| {
            handle_extend_subscription_time(app, extend_subscription_time_request)
        });

    let get_server_list = check_admin_filter
        .clone()
        .and(warp::path("server_list"))
        .and(warp::get())
        .and(warp::query::<HashMap<String, String>>())
        .and(with_app_state(app_state.clone()))
        .and_then(
            |_token, query_params: HashMap<String, String>, app| async move {
                // Check if search parameters are present
                let has_search_params = query_params.get("id").is_some()
                    || query_params.get("name").is_some()
                    || query_params.get("ip_addr").is_some()
                    || query_params.get("version").is_some()
                    || query_params.get("status").is_some();

                if has_search_params {
                    // Parse version and status arrays from comma-separated strings
                    let version_filter =
                        query_params
                            .get("version")
                            .filter(|s| !s.is_empty())
                            .map(|s| {
                                s.split(',')
                                    .map(|v| v.trim().to_string())
                                    .collect::<Vec<String>>()
                            });

                    let status_filter =
                        query_params
                            .get("status")
                            .filter(|s| !s.is_empty())
                            .map(|s| {
                                s.split(',')
                                    .map(|v| v.trim().to_string())
                                    .collect::<Vec<String>>()
                            });

                    let search_request = Some(message::ServerSearchRequest {
                        page: query_params.get("page").and_then(|p| p.parse().ok()),
                        page_size: query_params.get("page_size").and_then(|p| p.parse().ok()),
                        id: query_params.get("id").and_then(|p| p.parse().ok()),
                        name: query_params.get("name").map(|s| s.to_string()),
                        ip_addr: query_params.get("ip_addr").map(|s| s.to_string()),
                        version: version_filter,
                        status: status_filter,
                    });
                    handle_get_server_list_with_search(search_request, app).await
                } else {
                    // Use original handler for backward compatibility
                    let pagination = if query_params.is_empty() {
                        None
                    } else {
                        Some(message::PaginationRequest {
                            page: query_params.get("page").and_then(|p| p.parse().ok()),
                            page_size: query_params.get("page_size").and_then(|p| p.parse().ok()),
                            fetch_all: query_params.get("fetch_all").and_then(|p| p.parse().ok()),
                        })
                    };
                    handle_get_server_list(pagination, app).await
                }
            },
        );

    let get_all_server_list = check_admin_filter
        .clone()
        .and(warp::path("server_list_all"))
        .and(warp::get())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, app| handle_get_all_server_list(app));

    let add_server = check_admin_filter
        .clone()
        .and(warp::path("add_server"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| handle_add_server(app, token, body));

    let rmv_server = check_admin_filter
        .clone()
        .and(warp::path("rmv_server"))
        .and(warp::query::<RmvServerRequest>())
        .and(warp::delete())
        .and(with_app_state(app_state.clone()))
        .and_then(|_token, body, app| handle_rmv_server(app, body));

    let modify_server = check_admin_filter
        .clone()
        .and(warp::path("mod_server"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_app_state(app_state.clone()))
        .and_then(|token, body, app| handle_modify_server(token, app, body));

    let reset_user_traffic = check_admin_filter
        .clone()
        .and(warp::path("reset_user_traffic"))
        .and(warp::post())
        .and(warp::body::json())
        .and(with_reset_traffic_ctx(reset_traffic_ctx.clone()))
        .and_then(|_token, body, reset_traffic_ctx| {
            handle_reset_user_traffic(body, reset_traffic_ctx)
        });

    let admin_route = subscription_list
        .or(add_user)
        .or(edit_user)
        .or(rmv_user)
        .or(extend_subscription_time)
        .or(get_server_list)
        .or(get_all_server_list)
        .or(add_server)
        .or(rmv_server)
        .or(modify_server)
        .or(reset_user_traffic);

    // Combine all routes
    let routes = login
        .or(subscription_info)
        .or(ports)
        .or(create_port)
        .or(rmv_port)
        .or(modify_port)
        .or(suspend_port)
        .or(resume_port)
        .or(get_line_stats)
        .or(add_forward_endpoint)
        .or(rmv_forward_endpoint)
        .or(forward_endpoints)
        .or(modify_forward_endpoint)
        .or(get_setup_script)
        .or(download_dirs)
        .or(test_latency)
        .or(get_worker_setup_script)
        .or(admin_route);

    #[cfg(feature = "embed")]
    let routes = routes.or(frontend_routes);

    let routes = routes.recover(error::handle_rejection).with(cors).with(log);

    info!(
        "Server started at http://{}:{}",
        opt.bind_address, opt.backend_port
    );
    warp::serve(routes)
        .run((
            opt.bind_address
                .parse::<IpAddr>()
                .expect("Invalid bind address"),
            opt.backend_port,
        ))
        .await;

    Ok(())
}

// Frontend asset serving functions
#[cfg(feature = "embed")]
#[derive(RustEmbed)]
#[folder = "../zfc-web-ui/dist"]
struct FrontendAssets;

#[cfg(feature = "embed")]
fn create_frontend_routes(
    _opt: &Opt,
) -> impl Filter<Extract = (impl Reply,), Error = Rejection> + Clone {
    // Create the embedded file server
    let embedded_files = warp_embed::embed(&FrontendAssets);

    // Handle SPA routing - serve index.html for non-API routes
    let spa_fallback =
        warp::path::tail()
            .and(warp::get())
            .and_then(|path: warp::path::Tail| async move {
                let path_str = path.as_str();

                // Don't handle API routes or downloads
                if path_str.starts_with("api/") || path_str.starts_with("downloads/") {
                    return Err(warp::reject::not_found());
                }

                // For SPA routes, serve index.html
                match FrontendAssets::get("index.html") {
                    Some(file) => {
                        let response = warp::http::Response::builder()
                            .header("content-type", "text/html")
                            .header("cache-control", "no-cache")
                            .body(warp::hyper::Body::from(file.data.to_vec()))
                            .unwrap();
                        Ok(response)
                    }
                    None => Err(warp::reject::not_found()),
                }
            });

    // Try embedded files first, then fallback to SPA routing
    embedded_files.or(spa_fallback)
}

fn with_app_state(
    app_state: AppState,
) -> impl Filter<Extract = (AppState,), Error = Infallible> + Clone {
    warp::any().map(move || app_state.clone())
}

fn with_reset_traffic_ctx(
    reset_traffic_ctx: ArcResetTrafficContext,
) -> impl Filter<Extract = (ArcResetTrafficContext,), Error = Infallible> + Clone {
    warp::any().map(move || reset_traffic_ctx.clone())
}

fn with_auth() -> impl Filter<Extract = (String,), Error = Rejection> + Clone {
    warp::header::<String>("Authorization")
        .map(|bearer: String| {
            bearer
                .strip_prefix("Bearer ")
                .unwrap_or(&bearer)
                .to_string()
        })
        .and_then(|token: String| async move {
            let decoded = decode::<Claims>(
                &token,
                &DecodingKey::from_secret(
                    std::env::var("JWT_SECRET").unwrap_or_default().as_bytes(),
                ),
                &Validation::default(),
            )
            .map_err(|_| warp::reject::custom(error::Error::Auth("Invalid token".to_string())))?;

            Ok::<String, Rejection>(decoded.claims.token_id)
        })
}

// check if the user can access the forward endpoint
fn with_forward_endpoint_and_auth(
    app_state: AppState,
) -> impl Filter<Extract = (String,), Error = Rejection> + Clone {
    warp::any()
        .and(with_auth())
        .and_then(move |token_id: String| {
            let app_state = app_state.clone();
            async move {
                // Check if subscription has forward endpoint feature enabled
                let subscription = app_state
                    .db
                    .subscription()
                    .find_unique(subscription::token_id::equals(token_id.clone()))
                    .exec()
                    .await
                    .map_err(|_| {
                        warp::reject::custom(error::Error::Auth("Database error".to_string()))
                    })?
                    .ok_or_else(|| {
                        warp::reject::custom(error::Error::Auth(
                            "Subscription not found".to_string(),
                        ))
                    })?;

                if !subscription.allow_forward_endpoint {
                    return Err(warp::reject::custom(error::Error::InternalError(anyhow!(
                        "Forward endpoint feature not enabled for this subscription"
                    ))));
                }
                Ok::<String, Rejection>(token_id)
            }
        })
}
