use chrono::{DateTime, Utc};
use influxdb::{Client as InfluxDBClient, ReadQuery};
use log::error;
use serde::Deserialize;
use std::collections::HashMap;

use crate::message::{NetSpeedItem, SystemStatsItem};

pub async fn get_line_netcard_speed(
    line_id: &[i32],
    client: &InfluxDBClient,
) -> Result<HashMap<i32, NetSpeedItem>, anyhow::Error> {
    if line_id.is_empty() {
        return Ok(HashMap::new());
    }
    let mut lines_id_str = format!("agent_id = \'{}\'", line_id[0]);
    for line_id in line_id.iter().skip(1) {
        lines_id_str.push_str(&format!(" OR agent_id = \'{}\'", line_id));
    }
    let query_str = format!(
        "SELECT LAST(rx) as rx, LAST(tx) as tx, LAST(total_rx) as total_rx, LAST(total_tx) as total_tx FROM netcard_speed WHERE ({}) AND (time > now() - 1m) GROUP BY agent_id",
        lines_id_str
    );
    log::debug!("query_str: {:?}", query_str);
    let result = client.query(ReadQuery::new(query_str)).await?;
    #[derive(Deserialize)]
    struct ResultValue {
        results: Vec<ResultItem>,
    }
    #[allow(unused)]
    #[derive(Deserialize)]
    struct ResultItem {
        statement_id: i32,
        series: Vec<SeriesItem>,
    }
    #[allow(unused)]
    #[derive(Deserialize)]
    struct SeriesItem {
        name: String,
        tags: HashMap<String, String>,
        columns: Vec<String>,
        values: Vec<(DateTime<Utc>, f64, f64, u64, u64)>,
    }
    log::debug!("result: {:?}", result);
    let result = match serde_json::from_str::<ResultValue>(&result) {
        Ok(result) => result,
        Err(e) => {
            error!("Failed to parse result: {} for result: {}", e, result);
            return Ok(HashMap::new());
        }
    };
    let mut net_card_speed = HashMap::new();
    for result in result.results {
        for series in result.series {
            let agent_id = series.tags.get("agent_id").unwrap();
            for (_t, rx, tx, total_rx, total_tx) in series.values {
                net_card_speed.insert(
                    agent_id.parse::<i32>()?,
                    NetSpeedItem {
                        rx: rx as u64,
                        tx: tx as u64,
                        total_rx: total_rx as u64,
                        total_tx: total_tx as u64,
                    },
                );
            }
        }
    }
    Ok(net_card_speed)
}

pub async fn get_line_system_stats(
    line_id: &[i32],
    client: &InfluxDBClient,
) -> Result<HashMap<i32, SystemStatsItem>, anyhow::Error> {
    if line_id.is_empty() {
        return Ok(HashMap::new());
    }
    let mut lines_id_str = format!("agent_id = \'{}\'", line_id[0]);
    for line_id in line_id.iter().skip(1) {
        lines_id_str.push_str(&format!(" OR agent_id = \'{}\'", line_id));
    }
    let query_str = format!(
        "SELECT LAST(cpu_usage) as cpu_usage, LAST(memory_total) as memory_total, LAST(memory_used) as memory_used, LAST(uptime) as uptime FROM system_stats WHERE ({}) AND (time > now() - 1m) GROUP BY agent_id",
        lines_id_str
    );
    log::debug!("query_str: {:?}", query_str);
    let result = client.query(ReadQuery::new(query_str)).await?;
    #[derive(Deserialize)]
    struct ResultValue {
        results: Vec<ResultItem>,
    }
    #[allow(unused)]
    #[derive(Deserialize)]
    struct ResultItem {
        statement_id: i32,
        series: Vec<SeriesItem>,
    }
    #[allow(unused)]
    #[derive(Deserialize)]
    struct SeriesItem {
        name: String,
        tags: HashMap<String, String>,
        columns: Vec<String>,
        values: Vec<(DateTime<Utc>, f32, u64, u64, u64)>,
    }
    log::debug!("result: {:?}", result);
    let result = match serde_json::from_str::<ResultValue>(&result) {
        Ok(result) => result,
        Err(e) => {
            error!("Failed to parse result: {} for result: {}", e, result);
            return Err(anyhow::anyhow!("Failed to parse result"));
        }
    };
    let mut system_stats = HashMap::new();
    for result in result.results {
        for series in result.series {
            let agent_id = series.tags.get("agent_id").unwrap();
            for (_t, cpu_usage, memory_total, memory_used, uptime) in series.values {
                system_stats.insert(
                    agent_id.parse::<i32>()?,
                    SystemStatsItem {
                        cpu_usage,
                        memory_total,
                        memory_used,
                        uptime,
                    },
                );
            }
        }
    }
    Ok(system_stats)
}

#[cfg(test)]
mod tests {
    use super::*;
    use influxdb::Client;

    #[tokio::test]
    async fn test_get_line_netcard_speed() {
        let client = Client::new("http://45.127.34.116:8086", "zfcstats")
            .with_auth("zfcinfluxdb", "eMXbo^H2kcVpM*");
        let result = get_line_netcard_speed(&[1, 2], &client).await;
        println!("result: {:?}", result);
    }

    #[tokio::test]
    async fn test_get_line_system_stats() {
        let client = Client::new("http://45.127.34.116:8086", "zfcstats")
            .with_auth("zfcinfluxdb", "eMXbo^H2kcVpM*");
        let result = get_line_system_stats(&[1, 2], &client).await;
        println!("result: {:?}", result);
    }
}
