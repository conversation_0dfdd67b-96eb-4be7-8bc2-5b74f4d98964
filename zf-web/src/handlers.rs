use std::collections::HashMap;

use crate::add_machine::{add_machine, generate_worker_setup_script, remove_line};
use crate::add_user::{add_user_proc, edit_user_proc};
use crate::forwarder::{
    generate_hammer_server_config, generate_hammer_server_config_update_port,
    generate_port_forward_config, generate_random_hammer_server_config, generate_setup_script,
    get_blance_strategy_from_proxy_config, test_fwd_and_remote_latency, test_remote_latency,
    update_hammer_proxy_config, update_tot_proxy_config, ForwardEndpointInfoForForwarder,
    TestFwdAndRemoteLatencyResult,
};
use crate::tot::get_tot_config_from_str;
use crate::{error::Error, prisma::subscription};
use crate::{prisma, AppState};
use anyhow::anyhow;
use base64::Engine;
use chrono::Utc;
use common::app_message::{LatencyTestMethod, Mode};
use common::reset_traffic_task::{ArcResetTrafficContext, ResetTrafficTask};
use common::{get_line_lock, TotConfig, TotGlobalConfig};
use private_tun::snell_impl_ver::config::{ClientConfig, ServerConfig};
use reqwest::Url;
// use influxdb::{Client, ReadQuery};
use crate::message::*;
use jsonwebtoken::{encode, EncodingKey, Header};
use log::{error, info, warn};
use prisma_client_rust::{operator::*, Direction};
use redis::AsyncCommands;

use crate::stats::{get_line_netcard_speed, get_line_system_stats};
use warp::{reject::Rejection, Reply};
macro_rules! get_number {
    ($conn:expr, $k:expr, $d:expr) => {
        match $conn.get($k).await {
            Ok(v) => v,
            Err(e) => match e.kind() {
                redis::ErrorKind::TypeError => $d,
                _ => return Err(warp::reject::custom(crate::error::Error::Redis(e.into()))),
            },
        }
    };
}

pub async fn handle_login(
    login: LoginRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Login attempt with token_id: {}", login.token);

    // Find subscription by token
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(login.token.clone()))
        .select(prisma::subscription::select!({
            id
            token_id
            activated
            valid_until
        }))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Login failed: Invalid token");
            return Err(warp::reject::custom(Error::Auth(
                "Invalid token".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error during login: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Check if subscription is valid
    if !subscription.activated || subscription.valid_until < Utc::now() {
        warn!(
            "Login failed: Subscription expired or not activated for token_id: {}",
            subscription.token_id
        );
        return Err(warp::reject::custom(Error::Auth(
            "Subscription expired or not activated".to_string(),
        )));
    }

    // Create JWT token
    let claims = Claims {
        token_id: subscription.token_id.clone(),
        exp: (Utc::now() + chrono::Duration::days(7)).timestamp() as usize,
    };

    let jwt = match encode(
        &Header::default(),
        &claims,
        &EncodingKey::from_secret(std::env::var("JWT_SECRET").unwrap_or_default().as_bytes()),
    ) {
        Ok(token) => token,
        Err(e) => {
            error!("Failed to create JWT token: {}", e);
            return Err(warp::reject::custom(Error::Auth(
                "Failed to create token".to_string(),
            )));
        }
    };

    info!("Login successful for token_id: {}", subscription.token_id);
    Ok(warp::reply::json(&LoginResponse { jwt }))
}

pub async fn handle_subscription_info(
    token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!("Fetching subscription info for token_id: {}", token_id);

    let subscription = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({
            id
            token_id
            valid_until
            last_reset
            reset_days
            traffic
            bandwidth
            lines
            is_admin
            allow_forward_endpoint
        }))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };
    let mut line_info: Vec<LineInfo> = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![prisma::outbound_endpoint::id::in_vec(
            subscription.lines,
        )])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .select(prisma::outbound_endpoint::select!(
         {
                id
                display_name
                ingress_ipv_4
                port_start
                port_end
                allow_forward
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .into_iter()
        .map(|x| LineInfo {
            id: x.id,
            display_name: x.display_name,
            ip_addr: x.ingress_ipv_4,
            is_online: None,
            port_start: x.port_start,
            port_end: x.port_end,
            allow_forward: x.allow_forward,
        })
        .collect();
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;
    for l in line_info.iter_mut() {
        let online: u128 = get_number!(conn, format!("server:status:{}", l.id), 0);
        l.is_online = Some(online == 1);
    }
    let traffic_used = get_number!(conn, format!("sub:used:{}", subscription.id), 0);
    let info = SubscriptionInfo {
        valid_until: subscription.valid_until.into(),
        next_reset: subscription.last_reset.map(|last_reset| {
            (last_reset + chrono::Duration::days(subscription.reset_days.unwrap_or(30) as i64))
                .to_utc()
        }),
        traffic_total: subscription.traffic,
        traffic_used,
        bandwidth: subscription.bandwidth,
        lines: line_info,
        is_admin: subscription.is_admin,
        allow_forward_endpoint: subscription.allow_forward_endpoint,
    };

    info!(
        "Successfully retrieved subscription info for token_id: {}",
        token_id
    );
    Ok(warp::reply::json(&info))
}

pub async fn handle_get_ports_with_search(
    token_id: String,
    search_request: Option<PortSearchRequest>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    // Extract pagination and search parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let name_search = search_request.as_ref().and_then(|s| s.name.as_ref());
    let line_filter = search_request.as_ref().and_then(|s| s.line);
    let entry_point_search = search_request.as_ref().and_then(|s| s.entry_point.as_ref());
    let target_search = search_request.as_ref().and_then(|s| s.target.as_ref());

    info!(
        "Get ports with search for token_id: '{}', page: {}, page_size: {}, id: {:?}, name: {:?}, line: {:?}, entry_point: {:?}, target: {:?}",
        token_id, page, page_size, id_search, name_search, line_filter, entry_point_search, target_search
    );

    // Get the subscription to get the subscription ID
    let subscription_basic = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Build database query conditions
    let mut conditions = vec![prisma::port::subscription_id::equals(Some(
        subscription_basic.id,
    ))];

    // Add line filter if specified
    if let Some(line_id) = line_filter {
        conditions.push(prisma::port::outbound_endpoint_id::equals(Some(line_id)));
    }

    // For search functionality, we need to get all ports first and then filter
    // This is because regex filtering happens in application code, not database
    let ports_data = app_state
        .db
        .port()
        .find_many(conditions)
        .order_by(prisma::port::id::order(Direction::Desc))
        .select(prisma::port::select!({
            id
            display_name
            target_addr_list
            target_port_v_4
            target_address_v_4
            port_v_4
            outbound_endpoint_id
            select_mode
            test_method
            traffic_in
            forward_config
            related_forward_endpoint_ids
            related_tot_server_ids
            is_suspended
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // If ID search is specified, filter for exact ID match first (takes priority)
    let ports_data = if let Some(search_id) = id_search {
        ports_data
            .into_iter()
            .filter(|port| port.id == search_id)
            .collect()
    } else {
        ports_data
    };

    let mut ports: Vec<PortInfo> = vec![];
    for port in ports_data.into_iter() {
        let Some(outbound_endpoint_id) = port.outbound_endpoint_id else {
            continue;
        };
        let Ok(outbound_endpoint) = app_state
            .cache_outbound_endpoint_data
            .get(&outbound_endpoint_id)
            .await
        else {
            log::error!(
                "token_id: {} get ports but outbound_endpoint_id: {} not found",
                token_id,
                outbound_endpoint_id
            );
            continue;
        };

        let list = if port.target_addr_list.is_empty() {
            vec![format!(
                "{}:{}",
                port.target_address_v_4, port.target_port_v_4
            )]
        } else {
            port.target_addr_list
        };

        // Apply regex search filters on the processed data (skip if ID search is used)
        let entry_point = format!("{}:{}", outbound_endpoint.ingress_ipv_4, port.port_v_4);
        let target_addresses = list.join(", ");

        // If ID search is specified, skip other filters since exact ID match takes priority
        if id_search.is_none() {
            // Check name filter (regex)
            if let Some(name_pattern) = name_search {
                if !matches_regex_or_exact(&port.display_name, name_pattern) {
                    continue;
                }
            }

            // Check entry point filter (regex)
            if let Some(entry_pattern) = entry_point_search {
                if !matches_regex_or_exact(&entry_point, entry_pattern) {
                    continue;
                }
            }

            // Check target filter (regex)
            if let Some(target_pattern) = target_search {
                if !matches_regex_or_exact(&target_addresses, target_pattern) {
                    continue;
                }
            }
        }
        let tot_config = port
            .forward_config
            .as_ref()
            .map(|x| get_tot_config_from_str(x.as_str()))
            .flatten();

        let port_info = PortInfo {
            id: port.id,
            display_name: port.display_name,
            ip_addr: outbound_endpoint.ingress_ipv_4,
            port_v4: port.port_v_4,
            traffic_in: port.traffic_in,
            traffic_out: 0,
            outbound_endpoint_id: Some(outbound_endpoint_id),
            is_suspended: port.is_suspended,
            balance_strategy: get_blance_strategy_from_proxy_config(
                port.forward_config.as_ref().map(|x| x.as_str()),
            )
            .map_or(None, |x| x),
            forward_endpoints: Some(port.related_forward_endpoint_ids),
            target_address_list: list,
            target_select_mode: port.select_mode.map(|x| x as u32),
            test_method: port.test_method.map(|x| x as u32),
            tot_server_list: Some(port.related_tot_server_ids),
            tot_server_select_mode: tot_config.as_ref().map(|x| x.tot_server_select_mode.into()),
            tot_server_test_method: tot_config.as_ref().map(|x| x.tot_server_test_method.into()),
        };
        ports.push(port_info);
    }

    // Sort results to prioritize exact matches (skip if ID search is used)
    if id_search.is_none()
        && (name_search.is_some() || entry_point_search.is_some() || target_search.is_some())
    {
        ports.sort_by(|a, b| {
            let a_score = calculate_match_score(a, name_search, entry_point_search, target_search);
            let b_score = calculate_match_score(b, name_search, entry_point_search, target_search);
            b_score.cmp(&a_score) // Higher scores first
        });
    }

    // Calculate pagination info based on filtered results
    let total_filtered = ports.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        ((total_filtered as f64) / (page_size as f64)).ceil() as u32
    };

    // Apply pagination to filtered results
    let start_index = ((page - 1) * page_size) as usize;
    let end_index = (start_index + page_size as usize).min(ports.len());
    let paginated_ports = if start_index < ports.len() {
        ports[start_index..end_index].to_vec()
    } else {
        vec![]
    };

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedPortsResponse {
        ports: paginated_ports,
        pagination: pagination_info,
    };

    info!(
        "Successfully retrieved {} filtered ports for token_id: {} (page {}/{}, total: {})",
        response.ports.len(),
        token_id,
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

pub async fn handle_get_forward_endpoints_with_search(
    token_id: String,
    search_request: Option<ForwardEndpointSearchRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Extract pagination and search parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let name_search = search_request.as_ref().and_then(|s| s.name.as_ref());
    let ingress_address_search = search_request
        .as_ref()
        .and_then(|s| s.ingress_address.as_ref());

    info!(
        "Get forward endpoints with search for token_id: '{}', page: {}, page_size: {}, id: {:?}, name: {:?}, ingress_address: {:?}",
        token_id, page, page_size, id_search, name_search, ingress_address_search
    );

    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    // For search functionality, we need to get all forward endpoints first and then filter
    // This is because regex filtering happens in application code, not database
    let forward_endpoints_data = app_state
        .db
        .forward_endpoint()
        .find_many(vec![or(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::is_public::equals(true),
        ])])
        .order_by(prisma::forward_endpoint::id::order(Direction::Desc))
        .select(prisma::forward_endpoint::select!(
            {
                id
                name
                ingress_address
                protocol
                serve_port
                is_public
                token_id
                allow_ip_v_6
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?;

    // If ID search is specified, filter for exact ID match first (takes priority)
    let forward_endpoints_data = if let Some(search_id) = id_search {
        forward_endpoints_data
            .into_iter()
            .filter(|endpoint| endpoint.id == search_id)
            .collect()
    } else {
        forward_endpoints_data
    };

    let mut forward_endpoints: Vec<ForwardEndpoint> = vec![];
    for endpoint in forward_endpoints_data.into_iter() {
        // If ID search is specified, skip other filters since exact ID match takes priority
        if id_search.is_none() {
            // Check name filter (regex)
            if let Some(name_pattern) = name_search {
                if !matches_regex_or_exact(&endpoint.name, name_pattern) {
                    continue;
                }
            }

            // Check ingress address filter (regex)
            if let Some(ingress_pattern) = ingress_address_search {
                if !matches_regex_or_exact(&endpoint.ingress_address, ingress_pattern) {
                    continue;
                }
            }
        }

        // Convert to ForwardEndpoint struct
        if let Ok(protocol) = endpoint.protocol.parse() {
            forward_endpoints.push(ForwardEndpoint {
                id: endpoint.id,
                name: endpoint.name,
                ingress_address: endpoint.ingress_address,
                protocol,
                serve_port: endpoint.serve_port,
                is_public: endpoint.is_public,
                token_id: endpoint.token_id,
                allow_ipv6: endpoint.allow_ip_v_6,
            });
        }
    }

    // Sort results to prioritize exact matches (skip if ID search is used)
    if id_search.is_none() && (name_search.is_some() || ingress_address_search.is_some()) {
        forward_endpoints.sort_by(|a, b| {
            let a_score =
                calculate_forward_endpoint_match_score(a, name_search, ingress_address_search);
            let b_score =
                calculate_forward_endpoint_match_score(b, name_search, ingress_address_search);
            b_score.cmp(&a_score) // Higher scores first
        });
    }

    // Apply pagination to filtered results
    let total_filtered = forward_endpoints.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        (total_filtered + page_size - 1) / page_size
    };

    let start_index = ((page - 1) * page_size) as usize;
    let end_index = (start_index + page_size as usize).min(forward_endpoints.len());
    let paginated_endpoints = if start_index < forward_endpoints.len() {
        forward_endpoints[start_index..end_index].to_vec()
    } else {
        vec![]
    };

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedForwardEndpointResponse {
        forward_endpoints: paginated_endpoints,
        pagination: pagination_info,
    };

    info!(
        "Successfully retrieved {} filtered forward endpoints for token_id: {} (page {}/{}, total: {})",
        response.forward_endpoints.len(),
        token_id,
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

// Helper function to check if a string matches a regex pattern or exact match
fn matches_regex_or_exact(text: &str, pattern: &str) -> bool {
    // First check for exact match (case-insensitive)
    if text.to_lowercase().contains(&pattern.to_lowercase()) {
        return true;
    }

    // Then try regex match
    match regex::Regex::new(pattern) {
        Ok(re) => re.is_match(text),
        Err(_) => false, // If regex is invalid, fall back to substring match
    }
}

// Helper function to calculate match score for sorting (higher score = better match)
fn calculate_match_score(
    port: &PortInfo,
    name_pattern: Option<&String>,
    entry_point_pattern: Option<&String>,
    target_pattern: Option<&String>,
) -> i32 {
    let mut score = 0;

    if let Some(pattern) = name_pattern {
        if port.display_name.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if port
            .display_name
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = entry_point_pattern {
        let entry_point = format!("{}:{}", port.ip_addr, port.port_v4);
        if entry_point.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if entry_point.to_lowercase().contains(&pattern.to_lowercase()) {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = target_pattern {
        let target_addresses = port.target_address_list.join(", ");
        if target_addresses.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if target_addresses
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    score
}

// Helper function to calculate match score for forward endpoints (higher score = better match)
fn calculate_forward_endpoint_match_score(
    endpoint: &ForwardEndpoint,
    name_pattern: Option<&String>,
    ingress_address_pattern: Option<&String>,
) -> i32 {
    let mut score = 0;

    if let Some(pattern) = name_pattern {
        if endpoint.name.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if endpoint
            .name
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = ingress_address_pattern {
        if endpoint.ingress_address.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if endpoint
            .ingress_address
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    score
}

// Helper function to calculate match score for servers (higher score = better match)
fn calculate_server_match_score(
    server: &ServerInfo,
    name_pattern: Option<&String>,
    ip_addr_pattern: Option<&String>,
) -> i32 {
    let mut score = 0;

    if let Some(pattern) = name_pattern {
        if server.display_name.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if server
            .display_name
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    if let Some(pattern) = ip_addr_pattern {
        if server.ip_addr.to_lowercase() == pattern.to_lowercase() {
            score += 100; // Exact match
        } else if server
            .ip_addr
            .to_lowercase()
            .contains(&pattern.to_lowercase())
        {
            score += 50; // Substring match
        }
    }

    score
}

pub async fn generate_port_forward_data(
    token_id: &str,
    forward_endpoints: Option<Vec<i32>>,
    balance_strategy: Option<u32>,
    subscription: &SubscriptionCommonInfo::Data,
    tx: &prisma::PrismaClient,
) -> anyhow::Result<Option<(String, Vec<i32>)>> {
    let mut set_forward_config = None;
    if let Some(forward_endpoints) = forward_endpoints {
        let max_forward_endpoint_num = subscription.max_forward_endpoint_num as usize;
        if forward_endpoints.len() > max_forward_endpoint_num {
            error!(
                "Add port failed, current user exceed the max forward endpoint number: {}",
                max_forward_endpoint_num
            );
            return Err(anyhow::anyhow!(
                "Add port failed, current user exceed the max forward endpoint number: {}",
                max_forward_endpoint_num
            ));
        }
        if forward_endpoints.len() > 0 {
            let forward_endpoint_list = tx
                .forward_endpoint()
                .find_many(vec![or(vec![
                    // forward endpoint that belong to this subscription
                    and(vec![
                        prisma::forward_endpoint::id::in_vec(forward_endpoints.clone()),
                        prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
                    ]),
                    // forward endpoint that is public
                    and(vec![
                        prisma::forward_endpoint::id::in_vec(forward_endpoints.clone()),
                        prisma::forward_endpoint::is_public::equals(true),
                    ]),
                ])])
                .select(ForwardEndpointInfoForForwarder::select())
                .exec()
                .await
                .map_err(Error::Database)?
                .iter()
                .map(|x| (x.id, x.clone()))
                .collect::<HashMap<_, _>>();
            // 按照请求ID顺序的顺序排序数据
            let new_list = forward_endpoints
                .iter()
                .filter_map(|id| forward_endpoint_list.get(id).clone())
                .collect::<Vec<_>>();
            let forward_config = generate_port_forward_config(&new_list, balance_strategy)
                .map_err(Error::InternalError)?;
            info!("token_id: {}, forward config: {}", token_id, forward_config);
            let related_forward_endpoint_ids = new_list.iter().map(|x| x.id).collect::<Vec<_>>();
            info!(
                "token_id: {}, related_forward_endpoint_ids: {:?}",
                token_id, related_forward_endpoint_ids
            );
            set_forward_config = Some((forward_config, related_forward_endpoint_ids));
        }
    }
    Ok(set_forward_config)
}

prisma::subscription::select!(
    SubscriptionCommonInfo {
        id
        max_port_num_per_server
        max_forward_endpoint_num
        lines
        ports: select {
            id
            outbound_endpoint_id
            port_v_4
        }
    }
);

pub async fn generate_tot_config_or_forward_config(
    token_id: &str,
    forward_endpoints: Option<Vec<i32>>,
    fwd_balance_strategy: Option<u32>,
    mut tot_server_list: Option<Vec<i32>>,
    tot_server_select_mode: Option<u32>,
    tot_server_test_method: Option<u32>,
    subscription: &SubscriptionCommonInfo::Data,
    tx: &prisma::PrismaClient,
) -> anyhow::Result<Option<(String, (Vec<i32>, Option<Vec<i32>>))>> {
    let Some((forward_config, related_forward_endpoint_ids)) = generate_port_forward_data(
        token_id,
        forward_endpoints,
        fwd_balance_strategy,
        subscription,
        tx,
    )
    .await?
    else {
        return Ok(None);
    };

    let Some(tot_server_lists) = tot_server_list.take() else {
        // only forward endpoint
        return Ok(Some((forward_config, (related_forward_endpoint_ids, None))));
    };

    if tot_server_lists.is_empty() {
        return Ok(Some((forward_config, (related_forward_endpoint_ids, None))));
    }

    let max_forward_endpoint_num = subscription.max_forward_endpoint_num as usize;
    if tot_server_lists.len() > max_forward_endpoint_num {
        error!(
            "Add port failed, current user exceed the max tot server number: {}",
            max_forward_endpoint_num
        );
        return Err(anyhow::anyhow!(
            "Add port failed, current user exceed the max tot server number: {}",
            max_forward_endpoint_num
        ));
    }
    let forward_endpoint_list = tx
        .forward_endpoint()
        .find_many(vec![or(vec![
            // forward endpoint that belong to this subscription
            and(vec![
                prisma::forward_endpoint::id::in_vec(tot_server_lists.clone()),
                prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            ]),
            // forward endpoint that is public
            and(vec![
                prisma::forward_endpoint::id::in_vec(tot_server_lists.clone()),
                prisma::forward_endpoint::is_public::equals(true),
            ]),
        ])])
        .select(ForwardEndpointInfoForForwarder::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .iter()
        .map(|x| (x.id, x.clone()))
        .collect::<HashMap<_, _>>();
    // 按照请求ID顺序的顺序排序数据
    let new_list = tot_server_lists
        .iter()
        .filter_map(|id| forward_endpoint_list.get(id).clone())
        .collect::<Vec<_>>();
    let tot_fwd_config = generate_port_forward_config(&new_list, tot_server_select_mode.clone())
        .map_err(Error::InternalError)?;
    info!(
        "token_id: {}, tot forward config: {}",
        token_id, tot_fwd_config
    );
    let related_tot_endpoint_ids = new_list.iter().map(|x| x.id).collect::<Vec<_>>();
    info!(
        "token_id: {}, related_tot_endpoint_ids: {:?}",
        token_id, related_tot_endpoint_ids
    );
    let tot_total_config = TotConfig {
        fwd_config: serde_json::from_str(&forward_config)?,
        tot_server_list: serde_json::from_str(&tot_fwd_config)?,
        tot_server_select_mode: tot_server_select_mode
            .and_then(|x| TryFrom::<u32>::try_from(x as u32).ok())
            .unwrap_or(Mode::BestLatency),
        tot_server_test_method: tot_server_test_method
            .and_then(|x| TryFrom::<u32>::try_from(x as u32).ok())
            .unwrap_or(LatencyTestMethod::Tcpping),
        tot_config: TotGlobalConfig {
            enable_affinity: None,
            enable_1rtt: None,
            recv_queue_size: None,
            max_used_server: None,
        },
    };
    Ok(Some((
        serde_json::to_string(&tot_total_config)?,
        (related_forward_endpoint_ids, Some(related_tot_endpoint_ids)),
    )))
}

fn check_addr_list(addr_list: &Vec<String>) -> anyhow::Result<()> {
    if addr_list.is_empty() {
        return Err(anyhow::anyhow!("target address list is empty"));
    }
    if addr_list.len() > 10 {
        return Err(anyhow::anyhow!("target address list is too long max is 10"));
    }
    for addr in addr_list {
        let (addr, _comment) = match addr.split_once('#') {
            Some((addr, comment)) => (addr.to_string(), comment.to_string()),
            None => (addr.to_string(), "".to_string()),
        };
        let port_str = addr
            .rsplit(':')
            .next()
            .ok_or(anyhow::anyhow!("target: {} not contain port", addr))?;
        let port = port_str.parse::<u32>()?;
        if port < 1 || port > 65535 {
            return Err(anyhow::anyhow!(
                "target: {} port out of range: {}",
                addr,
                port
            ));
        }
        let _url = Url::parse(&format!("http://{addr}")).map_err(|e| {
            error!("parse addr faile: {e}");
            anyhow!("target: {addr} is not valid")
        })?;
    }
    Ok(())
}
pub async fn handle_create_port(
    token_id: String,
    port_request: CreatePortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Creating new port for token_id: {} with name: {}",
        token_id, port_request.display_name
    );
    check_addr_list(&port_request.target_address_list)
        .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    // check target select mode
    if let Some(mode) = port_request.target_select_mode {
        let _mode = TryInto::<common::app_message::Mode>::try_into(mode)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }

    if let Some(test_method) = port_request.test_method {
        let _test_method = TryInto::<common::app_message::LatencyTestMethod>::try_into(test_method)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }
    let token_id_clone = token_id.clone();

    let rst = match app_state
        .db
        ._transaction()
        .run(move |tx| {
            async move {
                let subscription = match tx
                    .subscription()
                    .find_unique(subscription::token_id::equals(token_id.clone()))
                    .select(SubscriptionCommonInfo::select())
                    .exec()
                    .await
                {
                    Ok(Some(sub)) => sub,
                    Ok(None) => {
                        warn!("Subscription not found for token_id: {}", token_id);
                        return Err(Error::NotFound("Subscription not found".to_string()));
                    }
                    Err(e) => {
                        error!("Database error while fetching subscription: {}", e);
                        return Err(Error::Database(e));
                    }
                };
                let line_id = port_request.outbound_endpoint_id;
                // check if line_id is in subscription.lines
                if !subscription.lines.iter().any(|line| *line == line_id) {
                    error!("Line not found for line_id: {}", line_id);
                    return Err(Error::InvalidInput("Line not found".to_string()));
                }
                let Ok(Some(line_info)) = tx
                    .outbound_endpoint()
                    .find_unique(prisma::outbound_endpoint::id::equals(line_id))
                    .select(prisma::outbound_endpoint::select!(
                        {
                            ingress_ipv_4
                            port_start
                            port_end
                        }
                    ))
                    .exec()
                    .await
                else {
                    error!("Line not found for line_id: {}", line_id);
                    return Err(Error::NotFound("Line not found".to_string()));
                };
                let current_ports_this_user_count = tx
                    .port()
                    .count(vec![
                        prisma::port::outbound_endpoint_id::equals(Some(line_id)),
                        prisma::port::subscription_id::equals(Some(subscription.id)),
                    ])
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                if current_ports_this_user_count >= subscription.max_port_num_per_server as i64 {
                    error!(
                        "Add port failed, current user exceed the max port number: {}",
                        subscription.max_port_num_per_server
                    );
                    return Err(Error::InvalidInput(format!(
                        "Add port failed, current user exceed the max port number: {}",
                        subscription.max_port_num_per_server
                    )));
                }
                // check ports available
                let current_ports: Vec<_> = tx
                    .port()
                    .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                        line_id,
                    ))])
                    .select(prisma::port::select!({ port_v_4 }))
                    .exec()
                    .await
                    .map_err(Error::Database)?
                    .into_iter()
                    .map(|x| x.port_v_4 as u16)
                    .collect();

                // add port
                // todo support limit port number for each subscription
                let port_start = line_info.port_start.map(|x| x as u16).unwrap_or(31000);
                let port_end = line_info.port_end.map(|x| x as u16).unwrap_or(34000);
                let selected_port = port_request
                    .expected_port
                    .or_else(|| (port_start..port_end).find(|x| !current_ports.contains(x)));
                let Some(selected_port) = selected_port else {
                    error!(
                        "Add port failed, can't find available port on server: {}",
                        line_id
                    );
                    return Err(Error::InvalidInput(format!(
                        "Add port failed, can't find available port on server: {}",
                        line_id
                    )));
                };
                // check port is in range
                if selected_port < port_start || selected_port > port_end {
                    error!(
                        "Port out of range for selected port: {} on server: {}",
                        selected_port, line_id
                    );
                    return Err(Error::InvalidInput(format!(
                        "expected port: {} out of range of port_start: {}, port_end: {}",
                        selected_port, port_start, port_end
                    )));
                }
                if current_ports.contains(&selected_port) {
                    error!("Port already used for selected port: {}", selected_port);
                    return Err(Error::InvalidInput(format!(
                        "Port already used for selected port: {}",
                        selected_port
                    )));
                }
                {
                    let _line_lock =
                        get_line_lock(&app_state.redis_pool, &app_state.redis_line_lock, line_id)
                            .await
                            .map_err(|e| {
                                Error::InternalError(anyhow::anyhow!(
                                    "Please try again later: {}",
                                    e
                                ))
                            })?;
                    // clear port usage
                    app_state
                        .redis_pool
                        .get()
                        .await
                        .map_err(Error::Redis)?
                        .set::<_, _, ()>(
                            format!(
                                "sub:line:port:used:{}:{}:{}",
                                subscription.id, line_id, selected_port
                            ),
                            0,
                        )
                        .await
                        .map_err(|e| {
                            Error::InternalError(anyhow::anyhow!("set port usage failed: {}", e))
                        })?;
                }
                let (first_target, port_str) = port_request
                    .target_address_list
                    .first()
                    .unwrap()
                    .rsplit_once(':')
                    .unwrap();
                let port = port_str.parse::<u16>().unwrap();
                let set_forward_config = generate_tot_config_or_forward_config(
                    &token_id,
                    port_request.forward_endpoints,
                    port_request.balance_strategy,
                    port_request.tot_server_list,
                    port_request.tot_server_select_mode,
                    port_request.tot_server_test_method,
                    &subscription,
                    &tx,
                )
                .await?;
                let mut set_list = vec![
                    prisma::port::subscription_id::set(Some(subscription.id)),
                    prisma::port::outbound_endpoint_id::set(Some(line_id)),
                    prisma::port::target_addr_list::set(port_request.target_address_list.clone()),
                    prisma::port::select_mode::set(
                        port_request.target_select_mode.map(|x| x as i32),
                    ),
                    prisma::port::test_method::set(port_request.test_method.map(|x| x as i32)),
                ];
                if let Some((
                    forward_config,
                    (related_forward_endpoint_ids, related_tot_endpoint_ids),
                )) = set_forward_config
                {
                    set_list.push(prisma::port::forward_config::set(Some(forward_config)));
                    set_list.push(prisma::port::related_forward_endpoint_ids::set(
                        related_forward_endpoint_ids,
                    ));
                    // TODO: current only support hammer

                    if let Some(related_tot_endpoint_ids) = related_tot_endpoint_ids {
                        set_list.push(prisma::port::related_tot_server_ids::set(
                            related_tot_endpoint_ids,
                        ));
                        set_list.push(prisma::port::forward_protocol::set(Some("Tot".to_string())));
                    } else {
                        set_list.push(prisma::port::forward_protocol::set(Some(
                            "Hammer".to_string(),
                        )));
                    }
                }

                let port = tx
                    .port()
                    .create(
                        port_request.display_name,
                        selected_port as i32,
                        first_target.to_string(),
                        port as i32,
                        0,
                        0,
                        set_list,
                    )
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                let list = if port.target_addr_list.is_empty() {
                    vec![format!(
                        "{}:{}",
                        port.target_address_v_4, port.target_port_v_4
                    )]
                } else {
                    port.target_addr_list
                };
                let tot_config = port
                    .forward_config
                    .as_ref()
                    .map(|x| get_tot_config_from_str(x.as_str()))
                    .flatten();
                let port_info = PortInfo {
                    id: port.id,
                    ip_addr: line_info.ingress_ipv_4,
                    display_name: port.display_name,
                    port_v4: port.port_v_4,
                    target_address_list: list,
                    target_select_mode: port.select_mode.map(|x| x as u32),
                    test_method: port.test_method.map(|x| x as u32),
                    traffic_in: port.traffic_in,
                    traffic_out: port.traffic_out,
                    outbound_endpoint_id: port.outbound_endpoint_id,
                    is_suspended: port.is_suspended,
                    balance_strategy: get_blance_strategy_from_proxy_config(
                        port.forward_config.as_ref().map(|x| x.as_str()),
                    )
                    .map_or(None, |x| x),
                    forward_endpoints: Some(port.related_forward_endpoint_ids),
                    tot_server_list: Some(port.related_tot_server_ids),
                    tot_server_select_mode: tot_config
                        .as_ref()
                        .map(|x| x.tot_server_select_mode.into()),
                    tot_server_test_method: tot_config
                        .as_ref()
                        .map(|x| x.tot_server_test_method.into()),
                };
                Ok(port_info)
            }
        })
        .await
    {
        Ok(rst) => rst,
        Err(e) => return Err(warp::reject::custom(e)),
    };

    info!(
        "Successfully created new port with id: {} for token_id: {}",
        rst.id, token_id_clone
    );
    Ok(warp::reply::json(&rst))
}

pub async fn handle_rmv_port(
    token_id: String,
    port_request: RmvPortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Creating new port for token_id: {} with name: {}",
        token_id, port_request.id
    );

    let subscription = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!(
            {
                ports: select {
                    id
                }
            }
        ))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };
    let ports = &subscription.ports;
    if !ports.iter().any(|x| x.id == port_request.id) {
        error!("Port not found for id: {}", port_request.id);
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    }
    let _port = app_state
        .db
        .port()
        .delete(prisma::port::id::equals(port_request.id))
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully removed port with id: {} for token_id: {}",
        port_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_modify_port(
    token_id: String,
    port_request: ModifyPortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Modifying port for token_id: {} with id: {}, request: {:?}",
        token_id, port_request.id, port_request
    );
    check_addr_list(&port_request.target_address_list)
        .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    // check target select mode
    if let Some(mode) = port_request.target_select_mode {
        let _mode = TryInto::<common::app_message::Mode>::try_into(mode)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }

    if let Some(test_method) = port_request.test_method {
        let _test_method = TryInto::<common::app_message::LatencyTestMethod>::try_into(test_method)
            .map_err(|e| warp::reject::custom(Error::InvalidInput(e.to_string())))?;
    }
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(SubscriptionCommonInfo::select())
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };
    let ports = &subscription.ports;
    let Some(port) = ports.iter().find(|x| x.id == port_request.id) else {
        error!("Port not found for id: {}", port_request.id);
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    };
    let Some(outbound_endpoint_id) = port.outbound_endpoint_id else {
        error!("Port has no outbound endpoint id");
        return Err(warp::reject::custom(Error::InvalidInput(
            "Port has no outbound endpoint id".to_string(),
        )));
    };
    // check if the port is already used
    let current_ports: Vec<_> = app_state
        .db
        .port()
        .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
            outbound_endpoint_id,
        ))])
        .select(prisma::port::select!({ port_v_4 }))
        .exec()
        .await
        .map_err(Error::Database)?;
    if let Some(expected_port) = port_request.expected_port {
        if current_ports
            .iter()
            .any(|x| x.port_v_4 == expected_port as i32)
        {
            error!("Port already used for expected port: {}", expected_port);
            return Err(warp::reject::custom(Error::InvalidInput(
                "Port already used".to_string(),
            )));
        }
        let Ok(Some(end_point)) = app_state
            .db
            .outbound_endpoint()
            .find_unique(prisma::outbound_endpoint::id::equals(outbound_endpoint_id))
            .select(prisma::outbound_endpoint::select!(
                {
                    port_start
                    port_end
                }
            ))
            .exec()
            .await
        else {
            error!(
                "Outbound endpoint not found for id: {}",
                outbound_endpoint_id
            );
            return Err(warp::reject::custom(Error::NotFound(
                "Outbound endpoint not found".to_string(),
            )));
        };
        let port_start = end_point.port_start.map(|x| x as u16).unwrap_or(31000);
        let port_end = end_point.port_end.map(|x| x as u16).unwrap_or(34000);
        if expected_port < port_start || expected_port > port_end {
            error!("Port out of range for expected port: {}", expected_port);
            return Err(warp::reject::custom(Error::InvalidInput(
                "Port out of range".to_string(),
            )));
        }
    }
    let (first_target, port_str) = port_request
        .target_address_list
        .first()
        .unwrap()
        .rsplit_once(':')
        .unwrap();
    let set_tot_config = generate_tot_config_or_forward_config(
        &token_id,
        port_request.forward_endpoints,
        port_request.balance_strategy,
        port_request.tot_server_list,
        port_request.tot_server_select_mode,
        port_request.tot_server_test_method,
        &subscription,
        &app_state.db,
    )
    .await
    .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    let mut set_list = vec![
        prisma::port::display_name::set(port_request.display_name),
        prisma::port::port_v_4::set(
            port_request.expected_port.unwrap_or(port.port_v_4 as u16) as i32
        ),
        prisma::port::target_address_v_4::set(first_target.to_string()),
        prisma::port::target_port_v_4::set(port_str.parse::<u16>().unwrap() as i32),
        prisma::port::target_addr_list::set(port_request.target_address_list.clone()),
        prisma::port::select_mode::set(port_request.target_select_mode.map(|x| x as i32)),
        prisma::port::test_method::set(port_request.test_method.map(|x| x as i32)),
    ];
    if let Some((forward_config, (related_forward_endpoint_ids, related_tot_endpoint_ids))) =
        set_tot_config
    {
        set_list.push(prisma::port::forward_config::set(Some(forward_config)));
        set_list.push(prisma::port::related_forward_endpoint_ids::set(
            related_forward_endpoint_ids,
        ));
        if let Some(related_tot_endpoint_ids) = related_tot_endpoint_ids {
            set_list.push(prisma::port::related_tot_server_ids::set(
                related_tot_endpoint_ids,
            ));
            set_list.push(prisma::port::forward_protocol::set(Some("Tot".to_string())));
        } else {
            set_list.push(prisma::port::forward_protocol::set(Some(
                "Hammer".to_string(),
            )));
            // don't forget to clear the related tot server ids
            set_list.push(prisma::port::related_tot_server_ids::set(vec![]));
        }
    } else {
        set_list.push(prisma::port::forward_protocol::set(None));
        set_list.push(prisma::port::forward_config::set(None));
        set_list.push(prisma::port::related_forward_endpoint_ids::set(vec![]));
        set_list.push(prisma::port::related_tot_server_ids::set(vec![]));
    }
    let _port = app_state
        .db
        .port()
        .update(prisma::port::id::equals(port_request.id), set_list)
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully modified port with id: {} for token_id: {}",
        port_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_suspend_port(
    token_id: String,
    suspend_request: SuspendPortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Suspending port for token_id: {} with id: {}",
        token_id, suspend_request.id
    );

    // Verify the port belongs to the user's subscription
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!(
            {
                ports: select {
                    id
                }
            }
        ))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Check if the port belongs to this subscription
    if !subscription
        .ports
        .iter()
        .any(|p| p.id == suspend_request.id)
    {
        warn!(
            "Port {} does not belong to subscription for token_id: {}",
            suspend_request.id, token_id
        );
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    }

    // Update the port to suspended status
    app_state
        .db
        .port()
        .update(
            prisma::port::id::equals(suspend_request.id),
            vec![prisma::port::is_suspended::set(true)],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully suspended port with id: {} for token_id: {}",
        suspend_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_resume_port(
    token_id: String,
    resume_request: ResumePortRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    info!(
        "Resuming port for token_id: {} with id: {}",
        token_id, resume_request.id
    );

    // Verify the port belongs to the user's subscription
    let subscription = match app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!(
            {
                ports: select {
                    id
                }
            }
        ))
        .exec()
        .await
    {
        Ok(Some(sub)) => sub,
        Ok(None) => {
            warn!("Subscription not found for token_id: {}", token_id);
            return Err(warp::reject::custom(Error::NotFound(
                "Subscription not found".to_string(),
            )));
        }
        Err(e) => {
            error!("Database error while fetching subscription: {}", e);
            return Err(warp::reject::custom(Error::Database(e)));
        }
    };

    // Check if the port belongs to this subscription
    if !subscription.ports.iter().any(|p| p.id == resume_request.id) {
        warn!(
            "Port {} does not belong to subscription for token_id: {}",
            resume_request.id, token_id
        );
        return Err(warp::reject::custom(Error::NotFound(
            "Port not found".to_string(),
        )));
    }

    // Update the port to active status
    app_state
        .db
        .port()
        .update(
            prisma::port::id::equals(resume_request.id),
            vec![prisma::port::is_suspended::set(false)],
        )
        .exec()
        .await
        .map_err(Error::Database)?;

    info!(
        "Successfully resumed port with id: {} for token_id: {}",
        resume_request.id, token_id
    );
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_get_line_stats(
    token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    log::info!("Get line stats for token_id: \'{}\'", token_id,);
    let lines = app_state
        .cache_sub_lines
        .get(&token_id)
        .await
        .map_err(|_| warp::reject::custom(Error::NotFound("Subscription not found".to_string())))?;
    let netcard_speed = get_line_netcard_speed(&lines, &app_state.influxdb_client)
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    let system_stats = get_line_system_stats(&lines, &app_state.influxdb_client)
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
    let result = GetLineStatsResponse {
        netcard_speed,
        system_stats,
    };
    Ok(warp::reply::json(&result))
}

pub async fn handle_add_forward_endpoint(
    token_id: String,
    request: AddForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!(
            {
                id
                max_forward_endpoint_num
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let max_forward_endpoint_num = subscription.max_forward_endpoint_num;
    let forward_endpoints = app_state
        .db
        .forward_endpoint()
        .count(vec![prisma::forward_endpoint::subscription_id::equals(
            Some(subscription.id),
        )])
        .exec()
        .await
        .map_err(Error::Database)?;
    if forward_endpoints >= max_forward_endpoint_num as i64 {
        return Err(warp::reject::custom(Error::InvalidInput(format!(
            "Max forward endpoint num reached: {}",
            max_forward_endpoint_num
        ))));
    }
    let (server_port, proxy_server_config) = if let Some(server_port) = request.serve_port {
        (
            server_port,
            generate_hammer_server_config(server_port as u16)
                .map_err(|e| warp::reject::custom(Error::InternalError(e)))?,
        )
    } else {
        let (server_port, proxy_server_config) = generate_random_hammer_server_config()
            .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
        (server_port as i32, proxy_server_config)
    };
    let forward_token_id = uuid::Uuid::new_v4().to_string();
    let setup_script = generate_setup_script(
        &app_state.opt.host_url,
        &proxy_server_config,
        &request.protocol.to_string(),
    )
    .await
    .unwrap_or_default();
    app_state
        .db
        .forward_endpoint()
        .create(
            request.name.clone(),
            request.name,
            request.ingress_address.trim().to_string(),
            server_port,
            request.protocol.to_string(),
            proxy_server_config,
            "".to_string(), // TODO: generate priv key current no used
            forward_token_id,
            vec![
                prisma::forward_endpoint::subscription_id::set(Some(subscription.id)),
                prisma::forward_endpoint::setup_script::set(Some(setup_script)),
                prisma::forward_endpoint::is_public::set(false),
                prisma::forward_endpoint::traffic_scale::set(Some(0.0)),
                prisma::forward_endpoint::allow_ip_v_6::set(request.allow_ipv6.unwrap_or(false)),
            ],
        )
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_get_forward_endpoint(
    token_id: String,
    pagination: Option<PaginationRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Check if fetch_all is requested
    let fetch_all = pagination
        .as_ref()
        .and_then(|p| p.fetch_all)
        .unwrap_or(false);

    // Extract pagination parameters with defaults
    let page = pagination.as_ref().and_then(|p| p.page).unwrap_or(1);
    let page_size = pagination.as_ref().and_then(|p| p.page_size).unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Calculate skip and take for pagination (ignored if fetch_all is true)
    let skip = if fetch_all {
        0
    } else {
        ((page - 1) * page_size) as i64
    };
    let take = if fetch_all { -1 } else { page_size as i64 }; // -1 means no limit in Prisma

    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    // Get total count of forward endpoints for this subscription
    let total_count = app_state
        .db
        .forward_endpoint()
        .count(vec![or(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::is_public::equals(true),
        ])])
        .exec()
        .await
        .map_err(Error::Database)? as u32;

    // Build the base query
    let mut query = app_state
        .db
        .forward_endpoint()
        .find_many(vec![or(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::is_public::equals(true),
        ])])
        .order_by(prisma::forward_endpoint::id::order(Direction::Desc));

    // Apply pagination only if not fetching all
    if !fetch_all {
        query = query.skip(skip).take(take);
    }

    let forward_endpoints = query
        .select(prisma::forward_endpoint::select!(
            {
                id
                name
                ingress_address
                protocol
                serve_port
                is_public
                token_id
                allow_ip_v_6
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?;
    let forward_endpoints = forward_endpoints
        .iter()
        .filter_map(|x| {
            Some(ForwardEndpoint {
                id: x.id,
                name: x.name.clone(),
                ingress_address: x.ingress_address.clone(),
                protocol: x
                    .protocol
                    .parse()
                    .map_err(|e| warp::reject::custom(Error::InternalError(e)))
                    .ok()?,
                serve_port: x.serve_port,
                is_public: x.is_public,
                token_id: x.token_id.clone(),
                allow_ipv6: x.allow_ip_v_6,
            })
        })
        .collect::<Vec<_>>();

    // Handle pagination info based on fetch_all flag
    let pagination_info = if fetch_all {
        // When fetching all, return pagination info indicating all items are on page 1
        PaginationInfo {
            current_page: 1,
            page_size: total_count,
            total_items: total_count,
            total_pages: 1,
        }
    } else {
        // Normal pagination
        let total_pages = if total_count == 0 {
            1
        } else {
            (total_count + page_size - 1) / page_size
        };

        PaginationInfo {
            current_page: page,
            page_size,
            total_items: total_count,
            total_pages,
        }
    };

    let response = PaginatedForwardEndpointResponse {
        forward_endpoints,
        pagination: pagination_info,
    };
    Ok(warp::reply::json(&response))
}

pub async fn handle_rmv_forward_endpoint(
    token_id: String,
    request: RmvForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id.clone()))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    // TODO: how to delete forward without subscription_id??
    // detect if any port is using this forward_endpoint
    let ports = app_state
        .db
        .port()
        .find_many(vec![prisma::port::related_forward_endpoint_ids::has(Some(
            request.id,
        ))])
        .exec()
        .await
        .map_err(Error::Database)?;
    for p in ports.iter() {
        if p.related_forward_endpoint_ids.len() == 1 {
            return Err(warp::reject::custom(Error::InvalidInput(format!(
                "Port: {}, id: {} is using this forward endpoint as the only forwarder server, please remove it first",
                p.display_name,
                p.id
            ))));
        }
    }
    let servers = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![
            prisma::outbound_endpoint::related_forward_endpoint_ids::has(Some(request.id)),
        ])
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            ingress_ipv_4
            related_forward_endpoint_ids }))
        .exec()
        .await
        .map_err(Error::Database)?;

    for s in servers.iter() {
        if s.related_forward_endpoint_ids.len() == 1 {
            return Err(warp::reject::custom(Error::InvalidInput(format!(
                "Worker: {} is using this forward endpoint as the only forwarder server, please remove it first",
                s.display_name
            ))));
        }
    }

    // remove forward endpoint from ports
    for p in ports.iter() {
        let new_forward_endpoints = p
            .related_forward_endpoint_ids
            .clone()
            .into_iter()
            .filter(|x| *x != request.id)
            .collect();
        let new_tot_server_list = if p.related_tot_server_ids.is_empty() {
            None
        } else {
            Some(
                p.related_tot_server_ids
                    .clone()
                    .into_iter()
                    .filter(|x| *x != request.id)
                    .collect::<Vec<_>>(),
            )
        };
        let target_address_list = if p.target_addr_list.is_empty() {
            if p.target_address_v_4.len() == 0 {
                vec![]
            } else {
                vec![format!("{}:{}", p.target_address_v_4, p.target_port_v_4)]
            }
        } else {
            p.target_addr_list.clone()
        };
        let modify_port_req = ModifyPortRequest {
            id: p.id,
            display_name: p.display_name.clone(),
            target_address_list,
            forward_endpoints: Some(new_forward_endpoints),
            tot_server_list: new_tot_server_list,
            target_select_mode: p.select_mode.map(|x| x as u32),
            test_method: p.test_method.map(|x| x as u32),
            ..Default::default()
        };
        handle_modify_port(token_id.clone(), modify_port_req, app_state.clone()).await?;
    }

    // remove forward endpoint from servers
    for s in servers.iter() {
        let new_forward_endpoints = s
            .related_forward_endpoint_ids
            .clone()
            .into_iter()
            .filter(|x| *x != request.id)
            .collect();
        let modify_server_req = ModifyServerRequest {
            server_id: s.id,
            display_name: s.display_name.clone(),
            ip_addr: s.ingress_ipv_4.clone(),
            forward_endpoints: Some(new_forward_endpoints),
            ..Default::default()
        };
        handle_modify_server(token_id.clone(), app_state.clone(), modify_server_req).await?;
    }

    let _forward_endpoint = app_state
        .db
        .forward_endpoint()
        .delete_many(vec![
            prisma::forward_endpoint::subscription_id::equals(Some(subscription.id)),
            prisma::forward_endpoint::id::equals(request.id),
        ])
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

prisma::port::select!(
    ToUpdatePortForwardConfig {
        id
        display_name
        forward_config
        forward_protocol
    }
);

prisma::forward_endpoint::select!(
    ToUpdateForwardEndpoint {
        id
        subscription_id
        ingress_address
        serve_port
        allow_ip_v_6
        protocol_config
    }
);

pub async fn update_port_proxy_config(
    port_data: &ToUpdatePortForwardConfig::Data,
    forward_endpoint: &ToUpdateForwardEndpoint::Data,
    ip_addr: &str,
    serve_port: u16,
    allow_ipv6: bool,
) -> Result<String, Error> {
    if let Some(forward_config) = &port_data.forward_config {
        if forward_config.is_empty() {
            return Err(Error::InternalError(anyhow!(
                "Forward config is empty for port: {}",
                port_data.display_name
            )));
        }
        let Some(forward_protocol) = &port_data.forward_protocol else {
            log::warn!(
                "Forward protocol is empty for port: {} id: {}",
                port_data.display_name,
                port_data.id,
            );
            return Err(Error::InternalError(anyhow!(
                "Forward protocol is empty for port: {}",
                port_data.display_name
            )));
        };
        let forward_protocol = forward_protocol.as_str().trim().to_lowercase();
        match forward_protocol.as_str() {
            "hammer" => {
                let Ok(forward_config) = serde_json::from_str::<ClientConfig>(&forward_config)
                else {
                    log::warn!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    );
                    return Err(Error::InternalError(anyhow!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    )));
                };
                let new_config = update_hammer_proxy_config(
                    forward_config,
                    forward_endpoint.id,
                    &ip_addr,
                    serve_port,
                    allow_ipv6,
                )
                .map_err(|e| {
                    log::error!(
                        "Failed to update hammer proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    );
                    Error::InternalError(anyhow!(
                        "Failed to update hammer proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    ))
                })?;
                return Ok(new_config);
            }
            "tot" => {
                let Ok(tot_config) = serde_json::from_str::<TotConfig>(&forward_config) else {
                    log::warn!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    );
                    return Err(Error::InternalError(anyhow!(
                        "Failed to decode forward config for port: {}, forward_id: {}",
                        port_data.display_name,
                        forward_endpoint.id
                    )));
                };
                let new_config = update_tot_proxy_config(
                    tot_config,
                    forward_endpoint.id,
                    &ip_addr,
                    serve_port,
                    allow_ipv6,
                )
                .map_err(|e| {
                    log::error!(
                        "Failed to update tot proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    );
                    Error::InternalError(anyhow!(
                        "Failed to update tot proxy config for port: {} id: {} error:{}",
                        port_data.display_name,
                        port_data.id,
                        e
                    ))
                })?;
                return Ok(new_config);
            }
            _ => {
                log::error!(
                    "Unsupported forward protocol: {:?} for port: {} id: {}",
                    &port_data.forward_protocol,
                    port_data.display_name,
                    port_data.id
                );
                return Err(Error::InternalError(anyhow!(
                    "Unsupported forward protocol: {:?} for port: {} id: {}",
                    &port_data.forward_protocol,
                    port_data.display_name,
                    port_data.id
                )));
            }
        }
    }
    return Err(Error::InternalError(anyhow!(
        "Forward config is empty for port: {}",
        port_data.display_name
    )));
}
pub async fn handle_modify_forward_endpoint(
    token_id: String,
    request: ModifyForwardEndpointRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let forward_endpoint = app_state
        .db
        .forward_endpoint()
        .find_unique(prisma::forward_endpoint::id::equals(request.id))
        .select(ToUpdateForwardEndpoint::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Forward endpoint not found".to_string()))?;

    if let Some(subscription_id) = forward_endpoint.subscription_id {
        if subscription_id != subscription.id {
            return Err(warp::reject::custom(Error::NotFound(
                "Forward endpoint not found".to_string(),
            )));
        }
    }
    // 检查是否需要更新配置
    if forward_endpoint.ingress_address != request.ingress_address
        || request
            .serve_port
            .as_ref()
            .map(|x| x != &forward_endpoint.serve_port)
            .unwrap_or_default()
        || request
            .allow_ipv6
            .map(|x| x != forward_endpoint.allow_ip_v_6)
            .unwrap_or_default()
    {
        // update config
        let ip_addr = request.ingress_address.trim().to_string();
        let serve_port = *request
            .serve_port
            .as_ref()
            .unwrap_or(&forward_endpoint.serve_port) as u16;
        let allow_ipv6 = request.allow_ipv6.unwrap_or(forward_endpoint.allow_ip_v_6);
        let old_proxy_server_config =
            serde_json::from_str::<ServerConfig>(&forward_endpoint.protocol_config)
                .map_err(|e| warp::reject::custom(Error::InternalError(anyhow!(e.to_string()))))?;
        let new_proxy_server_config =
            generate_hammer_server_config_update_port(old_proxy_server_config, serve_port)
                .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
        let protocol = request.protocol.to_string();
        app_state
            .db
            ._transaction()
            .run(move |tx| async move {
                let ports = tx
                    .port()
                    .find_many(vec![prisma::port::related_forward_endpoint_ids::has(Some(
                        forward_endpoint.id,
                    ))])
                    .select(ToUpdatePortForwardConfig::select())
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                for port in ports {
                    if let Ok(new_config) = update_port_proxy_config(
                        &port,
                        &forward_endpoint,
                        &ip_addr,
                        serve_port,
                        allow_ipv6,
                    )
                    .await
                    {
                        tx.port()
                            .update(
                                prisma::port::id::equals(port.id),
                                vec![prisma::port::forward_config::set(Some(new_config))],
                            )
                            .exec()
                            .await
                            .map_err(Error::Database)?;
                    }
                }

                // update worker's proxy config
                let servers = tx
                    .outbound_endpoint()
                    .find_many(vec![
                        prisma::outbound_endpoint::related_forward_endpoint_ids::has(Some(
                            forward_endpoint.id,
                        )),
                    ])
                    .select(prisma::outbound_endpoint::select!(
                        {
                            display_name
                            proxy_config
                        }
                    ))
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                for server in servers {
                    if let Some(proxy_config) = server.proxy_config {
                        let Ok(decoded) =
                            base64::engine::general_purpose::STANDARD.decode(&proxy_config)
                        else {
                            warn!(
                                "Failed to decode proxy config for server: {}",
                                server.display_name
                            );
                            continue;
                        };
                        let Ok(proxy_config) = serde_json::from_slice::<ClientConfig>(&decoded)
                        else {
                            warn!(
                                "Failed to decode proxy config for server: {}",
                                server.display_name
                            );
                            continue;
                        };
                        update_hammer_proxy_config(
                            proxy_config,
                            forward_endpoint.id,
                            &ip_addr,
                            serve_port,
                            allow_ipv6,
                        )
                        .map_err(|e| {
                            Error::InternalError(anyhow!(
                                "Failed to update worker's proxy config: {}",
                                e
                            ))
                        })?;
                    }
                }

                let setup_script = generate_setup_script(
                    &app_state.opt.host_url,
                    &new_proxy_server_config,
                    &protocol,
                )
                .await
                .unwrap_or_default();

                tx.forward_endpoint()
                    .update(
                        prisma::forward_endpoint::id::equals(request.id),
                        vec![
                            prisma::forward_endpoint::protocol_config::set(new_proxy_server_config),
                            prisma::forward_endpoint::setup_script::set(Some(setup_script)),
                        ],
                    )
                    .exec()
                    .await
                    .map_err(Error::Database)?;
                Ok::<(), Error>(())
            })
            .await
            .map_err(|e| warp::reject::custom(e))?;
    }
    let mut params = vec![
        prisma::forward_endpoint::name::set(request.name),
        prisma::forward_endpoint::ingress_address::set(request.ingress_address),
        prisma::forward_endpoint::protocol::set(request.protocol.to_string()),
    ];
    if let Some(serve_port) = request.serve_port {
        params.push(prisma::forward_endpoint::serve_port::set(serve_port));
    }
    if let Some(allow_ipv6) = request.allow_ipv6 {
        params.push(prisma::forward_endpoint::allow_ip_v_6::set(allow_ipv6));
    }
    let _forward_endpoint = app_state
        .db
        .forward_endpoint()
        .update(prisma::forward_endpoint::id::equals(request.id), params)
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_get_setup_script(
    forward_token_id: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let forward_endpoint = app_state
        .db
        .forward_endpoint()
        .find_unique(prisma::forward_endpoint::token_id::equals(forward_token_id))
        .select(prisma::forward_endpoint::select!({ setup_script }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Forward endpoint not found".to_string()))?;
    Ok(warp::reply::html(
        forward_endpoint.setup_script.unwrap_or_default(),
    ))
}
fn micros_to_string(micros: u64) -> String {
    format!("{:.2}ms", micros as f32 / 1000.0)
}
pub async fn handle_test_latency(
    token_id: String,
    request: TestLatencyRequest,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    log::info!(
        "Test latency for token_id: \'{}\' port_id: \'{}\'",
        token_id,
        request.port_id
    );
    let subscription = app_state
        .db
        .subscription()
        .find_unique(subscription::token_id::equals(token_id))
        .select(prisma::subscription::select!({ id }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let port = app_state
        .db
        .port()
        .find_first(vec![
            prisma::port::id::equals(request.port_id),
            prisma::port::subscription_id::equals(Some(subscription.id)),
        ])
        .select(prisma::port::select!(
            {
                id
                outbound_endpoint_id
                related_forward_endpoint_ids
                target_addr_list
                target_address_v_4
                target_port_v_4
                test_method
                forward_config
                display_name
            }
        ))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Port not found".to_string()))?;

    let outbound_endpoint_id =
        port.outbound_endpoint_id
            .ok_or(warp::reject::custom(Error::NotFound(format!(
                "Outbound data not found for port: {}",
                port.id
            ))))?;
    let outbound_endpoint = app_state
        .db
        .outbound_endpoint()
        .find_unique(prisma::outbound_endpoint::id::equals(outbound_endpoint_id))
        .select(prisma::outbound_endpoint::select!({ allow_latency_test
            use_forward_as_tun }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Outbound endpoint not found".to_string()))?;
    if port.related_forward_endpoint_ids.len() == 0
        || outbound_endpoint.use_forward_as_tun.unwrap_or(false)
    {
        // 只有一台机器
        // 直接测试ping延迟

        if outbound_endpoint.allow_latency_test.unwrap_or(false) {
            let mut remote = vec![];
            for target in port.target_addr_list.iter() {
                let target_addr = tokio::net::lookup_host(target)
                    .await
                    .map_err(|e| {
                        Error::InternalError(anyhow!("Failed to resolve target address: {}", e))
                    })?
                    .next()
                    .ok_or(Error::InternalError(anyhow::anyhow!(
                        "socket address is empty for target: {}",
                        target
                    )))?;
                remote.push(target_addr);
            }
            if remote.is_empty() {
                let target_addr = tokio::net::lookup_host((
                    port.target_address_v_4.as_str(),
                    port.target_port_v_4 as u16,
                ))
                .await
                .map_err(|e| {
                    Error::InternalError(anyhow!(
                        "Failed to resolve target: {} address: {}",
                        port.target_address_v_4,
                        e
                    ))
                })?
                .next()
                .ok_or(Error::InternalError(anyhow::anyhow!(
                    "socket address is empty for target: {}",
                    port.target_address_v_4
                )))?;
                remote.push(target_addr);
            }
            let latency = test_remote_latency(
                &app_state.web_client,
                &app_state.opt.mgmt_pubkey,
                app_state.opt.mgmt_url.clone(),
                remote,
                outbound_endpoint_id,
                port.test_method
                    .as_ref()
                    .and_then(|m| TryInto::<LatencyTestMethod>::try_into(*m as u32).ok())
                    .unwrap_or(LatencyTestMethod::Icmp),
            )
            .await
            .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;
            let mut result_html = format!("Test Result: \n");
            let mut remote_latency: HashMap<String, (Option<String>, String)> = HashMap::new();
            for (idx, latency) in latency.iter().enumerate() {
                result_html += &format!(
                    "    Entry -> {}: {}\n",
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4),
                    micros_to_string(*latency)
                );
                remote_latency.insert(
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4)
                        .clone(),
                    (None, micros_to_string(*latency)),
                );
            }
            Ok(warp::reply::json(&TestLatencyResponse {
                content: result_html,
            }))
        } else {
            return Err(warp::reject::custom(Error::NotFound(
                "Outbound endpoint not allow latency test".to_string(),
            )));
        }
    } else {
        // 多台机器测试反馈
        // 测试worker到forwarder的延迟
        let forward_config = port
            .forward_config
            .ok_or(warp::reject::custom(Error::NotFound(format!(
                "Forward config not found for port: {}",
                port.display_name
            ))))?;
        let mut remote = vec![];
        let forward_endpoint = app_state
            .db
            .forward_endpoint()
            .find_many(vec![prisma::forward_endpoint::id::in_vec(
                port.related_forward_endpoint_ids,
            )])
            .select(prisma::forward_endpoint::select!(
                {
                    id
                    display_name
                }
            ))
            .exec()
            .await
            .map_err(Error::Database)?;
        let id_to_name = forward_endpoint
            .iter()
            .map(|f| (f.id.to_string(), f.display_name.clone()))
            .collect::<HashMap<_, _>>();
        for target in port.target_addr_list.iter() {
            let target_addr = tokio::net::lookup_host(target)
                .await
                .map_err(|e| {
                    Error::InternalError(anyhow!("Failed to resolve target address: {}", e))
                })?
                .next()
                .ok_or(anyhow::anyhow!(
                    "socket address is empty for target: {}",
                    target
                ))
                .map_err(|e| {
                    Error::InternalError(anyhow!("Failed to resolve target address: {}", e))
                })?;
            remote.push(target_addr);
        }
        if remote.is_empty() {
            let target_addr = tokio::net::lookup_host((
                port.target_address_v_4.as_str(),
                port.target_port_v_4 as u16,
            ))
            .await
            .map_err(|e| {
                Error::InternalError(anyhow!(
                    "Failed to resolve target: {} address: {}",
                    port.target_address_v_4,
                    e
                ))
            })?
            .next()
            .ok_or(Error::InternalError(anyhow::anyhow!(
                "socket address is empty for target: {}",
                port.target_address_v_4
            )))?;
            remote.push(target_addr);
        }
        let test_result = test_fwd_and_remote_latency(
            &app_state.web_client,
            &app_state.opt.mgmt_pubkey,
            app_state.opt.mgmt_url.clone(),
            forward_config,
            outbound_endpoint_id,
            remote,
            port.test_method
                .as_ref()
                .and_then(|m| TryInto::<LatencyTestMethod>::try_into(*m as u32).ok())
                .unwrap_or(LatencyTestMethod::Icmp),
        )
        .await
        .map_err(|e| warp::reject::custom(Error::InternalError(e)))?;

        let mut result_html = format!("Test Result: \n");
        let mut fwd_server_latency = HashMap::new();
        let mut remote_latency = HashMap::new();
        for TestFwdAndRemoteLatencyResult {
            fwd_name,
            to_fwd_latency,
            fwd_to_remote_latency,
        } in test_result.iter()
        {
            fwd_server_latency.insert(
                id_to_name.get(fwd_name).unwrap_or(fwd_name).clone(),
                micros_to_string(*to_fwd_latency),
            );
            result_html += &format!(
                "    Entry -> {} Latency: {}\n",
                id_to_name.get(fwd_name).unwrap_or(fwd_name),
                micros_to_string(*to_fwd_latency)
            );
            for (idx, latency) in fwd_to_remote_latency.iter().enumerate() {
                result_html += &format!(
                    "        {} -> {}: {}\n",
                    id_to_name.get(fwd_name).unwrap_or(fwd_name),
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4),
                    micros_to_string(*latency)
                );
                remote_latency.insert(
                    port.target_addr_list
                        .get(idx)
                        .unwrap_or(&port.target_address_v_4)
                        .clone(),
                    (
                        Some(id_to_name.get(fwd_name).unwrap_or(fwd_name).clone()),
                        micros_to_string(*latency),
                    ),
                );
            }
        }
        Ok(warp::reply::json(&TestLatencyResponse {
            content: result_html,
        }))
    }
}

pub async fn handle_add_user(
    app_state: AppState,
    request: AddeUserInfo,
) -> Result<impl Reply, Rejection> {
    log::info!("Add user: {:?}", request);
    add_user_proc(vec![request], &app_state.db)
        .await
        .map_err(|e| {
            log::error!("Failed to add user: {}", e);
            warp::reject::custom(Error::InternalError(e))
        })?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_edit_user(
    app_state: AppState,
    request: EditUserInfo,
) -> Result<impl Reply, Rejection> {
    log::info!("Edit user: {:?}", request);
    edit_user_proc(request, &app_state.db).await.map_err(|e| {
        log::error!("Failed to edit user: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_rmv_user(
    app_state: AppState,
    rmv_user_request: RmvUserRequest,
) -> Result<impl Reply, Rejection> {
    let user_id = rmv_user_request.user_id;
    // remove all ports
    app_state
        .db
        .port()
        .delete_many(vec![prisma::port::subscription_id::equals(Some(user_id))])
        .exec()
        .await
        .map_err(Error::Database)?;
    // remove all forward endpoints
    app_state
        .db
        .forward_endpoint()
        .delete_many(vec![prisma::forward_endpoint::subscription_id::equals(
            Some(user_id),
        )])
        .exec()
        .await
        .map_err(Error::Database)?;

    // remove subscription
    app_state
        .db
        .subscription()
        .delete(prisma::subscription::id::equals(user_id))
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_extend_subscription_time(
    app_state: AppState,
    extend_subscription_time_request: ExtendSubscriptionTimeRequest,
) -> Result<impl Reply, Rejection> {
    let user_id = extend_subscription_time_request.user_id;
    let sub = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::id::equals(user_id))
        .select(prisma::subscription::select!({
            reset_days
            valid_until
        }))
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;
    let days = extend_subscription_time_request
        .days
        .unwrap_or(
            sub.reset_days
                .ok_or(warp::reject::custom(Error::InternalError(anyhow!(
                    "Reset days not set"
                ))))?,
        );
    let begin = if sub.valid_until < Utc::now() {
        Utc::now()
    } else {
        sub.valid_until.clone().into()
    };
    let new_valid_until = begin + chrono::Duration::days(days as i64);
    app_state
        .db
        .subscription()
        .update(
            prisma::subscription::id::equals(user_id),
            vec![prisma::subscription::valid_until::set(
                new_valid_until.into(),
            )],
        )
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_get_worker_setup_script(
    worker_pubkey: String,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    let setup_script = generate_worker_setup_script(
        app_state.db.clone(),
        &worker_pubkey,
        &app_state.opt.mgmt_pubkey,
        &app_state.opt.mgmt_url.to_string(),
    )
    .await
    .map_err(|e| {
        log::error!("Failed to generate worker setup script: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    Ok(warp::reply::html(setup_script))
}

fn get_balance_strategy_from_config(config: &str) -> Option<i32> {
    let decoded_config = base64::engine::general_purpose::STANDARD
        .decode(config)
        .ok()?;
    let config: ClientConfig = serde_json::from_slice(&decoded_config).ok()?;
    if let Some(multi_server_config) = config.multi_server_config {
        match multi_server_config.mode {
            private_tun::snell_impl_ver::server_provider::Mode::BestLatency => Some(0),
            private_tun::snell_impl_ver::server_provider::Mode::Fallback => Some(1),
            private_tun::snell_impl_ver::server_provider::Mode::Balance { balance_mode } => {
                match balance_mode {
                    private_tun::snell_impl_ver::server_provider::BalanceMode::DomainFollow => {
                        Some(2)
                    }
                    private_tun::snell_impl_ver::server_provider::BalanceMode::RoundRobin => {
                        Some(3)
                    }
                    private_tun::snell_impl_ver::server_provider::BalanceMode::Random => Some(4),
                }
            }
            _ => None,
        }
    } else {
        None
    }
}

pub async fn handle_get_subscription_list_with_search(
    search_request: Option<SubscriptionSearchRequest>,
    app_state: AppState,
) -> Result<impl Reply, Rejection> {
    use chrono::NaiveDate;
    use prisma::subscription::WhereParam;
    use prisma_client_rust::Direction;

    // Extract pagination parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Calculate skip and take for pagination
    let skip = ((page - 1) * page_size) as i64;
    let take = page_size as i64;

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let token_id_search = search_request.as_ref().and_then(|s| s.token_id.as_ref());
    let email_search = search_request.as_ref().and_then(|s| s.email.as_ref());
    let valid_until_start = search_request
        .as_ref()
        .and_then(|s| s.valid_until_start.as_ref());
    let valid_until_end = search_request
        .as_ref()
        .and_then(|s| s.valid_until_end.as_ref());
    let next_reset_start = search_request
        .as_ref()
        .and_then(|s| s.next_reset_start.as_ref());
    let next_reset_end = search_request
        .as_ref()
        .and_then(|s| s.next_reset_end.as_ref());
    let lines_search = search_request.as_ref().and_then(|s| s.lines.as_ref());

    info!(
        "Get subscription list with search - page: {}, page_size: {}, id: {:?}, token_id: {:?}, email: {:?}, valid_until: {:?}-{:?}, next_reset: {:?}-{:?}, lines: {:?}",
        page, page_size, id_search, token_id_search, email_search, valid_until_start, valid_until_end, next_reset_start, next_reset_end, lines_search
    );

    // Build where conditions
    let mut where_conditions: Vec<WhereParam> = vec![];

    // Exact ID search (highest priority)
    if let Some(id) = id_search {
        where_conditions.push(prisma::subscription::id::equals(id));
    }

    // Exact token ID search
    if let Some(token_id) = token_id_search {
        where_conditions.push(prisma::subscription::token_id::equals(token_id.clone()));
    }

    // Fuzzy email search (case-insensitive)
    if let Some(email) = email_search {
        // Convert to lowercase for case-insensitive search
        let email_lower = email.to_lowercase();
        where_conditions.push(prisma::subscription::owner_address::contains(email_lower));
    }

    // Valid until date range filtering
    if let Some(start_date_str) = valid_until_start {
        if let Ok(start_date) = NaiveDate::parse_from_str(start_date_str, "%Y-%m-%d") {
            let start_datetime = start_date.and_hms_opt(0, 0, 0).unwrap().and_utc();
            where_conditions.push(prisma::subscription::valid_until::gte(
                start_datetime.into(),
            ));
        }
    }
    if let Some(end_date_str) = valid_until_end {
        if let Ok(end_date) = NaiveDate::parse_from_str(end_date_str, "%Y-%m-%d") {
            let end_datetime = end_date.and_hms_opt(23, 59, 59).unwrap().and_utc();
            where_conditions.push(prisma::subscription::valid_until::lte(end_datetime.into()));
        }
    }

    // Lines filtering (ANY logic - subscription contains any of the selected lines)
    if let Some(lines) = lines_search {
        if !lines.is_empty() {
            // Use array overlap to check if subscription.lines contains any of the search lines
            where_conditions.push(prisma::subscription::lines::has_some(lines.clone()));
        }
    }

    // Note: Next reset date filtering is handled post-query since it's a calculated field
    // We'll filter the results after fetching from the database

    // Get total count with search filters
    // Note: For next reset date filtering, we need to adjust the count since it's post-processed
    let total_count = if next_reset_start.is_some() || next_reset_end.is_some() {
        // If we have next reset filters, we need to count after processing
        // For now, we'll use the database count and adjust later if needed
        // This is a trade-off between accuracy and performance
        app_state
            .db
            .subscription()
            .count(where_conditions.clone())
            .exec()
            .await
            .map_err(Error::Database)? as u32
    } else {
        // No next reset filters, use direct database count
        app_state
            .db
            .subscription()
            .count(where_conditions.clone())
            .exec()
            .await
            .map_err(Error::Database)? as u32
    };

    // Determine ordering - prioritize exact ID matches
    let order_by = if id_search.is_some() {
        // If searching by ID, order by ID ascending to put exact matches first
        prisma::subscription::id::order(Direction::Asc)
    } else {
        // Default ordering by ID descending (newest first)
        prisma::subscription::id::order(Direction::Desc)
    };

    // Query subscriptions with search filters and pagination
    let subscription_list = app_state
        .db
        .subscription()
        .find_many(where_conditions)
        .order_by(order_by)
        .skip(skip)
        .take(take)
        .select(prisma::subscription::select!({
           id
           token_id
           lines
           valid_until
           last_reset
           reset_days
           traffic
           owner_address
           activated
           allow_forward_endpoint
           max_port_num_per_server
           billing_type
           base_price
           recurring_price
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    let mut subscription_items = vec![];
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;

    // Parse next reset date filters for post-processing
    let next_reset_start_date = next_reset_start.and_then(|s| {
        NaiveDate::parse_from_str(s, "%Y-%m-%d")
            .ok()
            .and_then(|d| d.and_hms_opt(0, 0, 0))
            .map(|dt| dt.and_utc())
    });
    let next_reset_end_date = next_reset_end.and_then(|s| {
        NaiveDate::parse_from_str(s, "%Y-%m-%d")
            .ok()
            .and_then(|d| d.and_hms_opt(23, 59, 59))
            .map(|dt| dt.and_utc())
    });

    for subscription in subscription_list {
        info!(
            "Fetching subscription info for token_id: {}",
            subscription.token_id
        );

        // Get line information for this subscription
        let mut line_info: Vec<LineInfo> = app_state
            .db
            .outbound_endpoint()
            .find_many(vec![prisma::outbound_endpoint::id::in_vec(
                subscription.lines.clone(),
            )])
            .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
            .select(prisma::outbound_endpoint::select!(
             {
                    id
                    display_name
                    ingress_ipv_4
                    port_start
                    port_end
                    allow_forward
                }
            ))
            .exec()
            .await
            .map_err(Error::Database)?
            .into_iter()
            .map(|x| LineInfo {
                id: x.id,
                display_name: x.display_name,
                ip_addr: x.ingress_ipv_4,
                is_online: None,
                port_start: x.port_start,
                port_end: x.port_end,
                allow_forward: x.allow_forward,
            })
            .collect();

        // update line online status
        for line in line_info.iter_mut() {
            line.is_online = Some(get_number!(conn, format!("server:status:{}", line.id), 0) == 1);
        }

        // Get traffic usage from Redis
        let traffic_used = get_number!(conn, format!("sub:used:{}", subscription.id), 0);

        // Calculate next reset date
        let next_reset = subscription.last_reset.map(|last_reset| {
            (last_reset + chrono::Duration::days(subscription.reset_days.unwrap_or(30) as i64))
                .to_utc()
        });

        // Apply next reset date filtering (post-query filtering)
        let passes_next_reset_filter = {
            if let Some(start_date) = next_reset_start_date {
                if let Some(reset_date) = next_reset {
                    if reset_date < start_date {
                        false
                    } else {
                        true
                    }
                } else {
                    false // No next reset date, doesn't match filter
                }
            } else {
                true // No start filter
            }
        } && {
            if let Some(end_date) = next_reset_end_date {
                if let Some(reset_date) = next_reset {
                    if reset_date > end_date {
                        false
                    } else {
                        true
                    }
                } else {
                    false // No next reset date, doesn't match filter
                }
            } else {
                true // No end filter
            }
        };

        // Only include subscription if it passes all filters
        if passes_next_reset_filter {
            // Construct BillingType from database fields
            let bill_type = if subscription.billing_type == 0 {
                // Cycle billing
                BillingType::Cycle {
                    days: subscription.reset_days.unwrap_or(30) as usize,
                    price: subscription.base_price as usize,
                }
            } else {
                // OneTime billing
                BillingType::OneTime {
                    price: subscription.base_price as usize,
                    days: subscription.reset_days.unwrap_or(365) as usize,
                }
            };

            // Calculate total_days from reset_days or use a default
            let total_days = subscription.reset_days.unwrap_or(30) as u32;

            let info = SubscriptionItem {
                valid_until: subscription.valid_until.into(),
                next_reset,
                traffic_total: subscription.traffic * 1024 * 1024 * 1024,
                traffic_used,
                lines: line_info,
                id: subscription.id,
                token_id: subscription.token_id,
                email_address: subscription.owner_address,
                activated: subscription.activated,
                allow_forward_endpoint: subscription.allow_forward_endpoint,
                max_ports_per_server: subscription.max_port_num_per_server,
                bill_type,
                total_days,
            };
            subscription_items.push(info);
        }
    }

    // Calculate pagination info
    let total_pages = (total_count + page_size - 1) / page_size;

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_count,
        total_pages,
    };

    let response = PaginatedSubscriptionResponse {
        subscriptions: subscription_items,
        pagination: pagination_info,
    };

    Ok(warp::reply::json(&response))
}

pub async fn handle_get_server_list_with_search(
    search_request: Option<ServerSearchRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Extract pagination and search parameters with defaults
    let page = search_request.as_ref().and_then(|s| s.page).unwrap_or(1);
    let page_size = search_request
        .as_ref()
        .and_then(|s| s.page_size)
        .unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Extract search parameters
    let id_search = search_request.as_ref().and_then(|s| s.id);
    let name_search = search_request.as_ref().and_then(|s| s.name.as_ref());
    let ip_addr_search = search_request.as_ref().and_then(|s| s.ip_addr.as_ref());
    let version_filter = search_request.as_ref().and_then(|s| s.version.as_ref());
    let status_filter = search_request.as_ref().and_then(|s| s.status.as_ref());

    info!(
        "Get servers with search, page: {}, page_size: {}, id: {:?}, name: {:?}, ip_addr: {:?}, version: {:?}, status: {:?}",
        page, page_size, id_search, name_search, ip_addr_search, version_filter, status_filter
    );

    // For search functionality, we need to get all servers first and then filter
    // This is because regex filtering and status checking happens in application code
    let server_list = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            ingress_ipv_4
            port_start
            port_end
            traffic_scale
            allow_forward
            allow_latency_test
            use_forward_as_tun
            interface_name
            pubkey
            version
            proxy_config
            related_forward_endpoint_ids
            allow_ip_v_6
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // If ID search is specified, filter for exact ID match first (takes priority)
    let server_list = if let Some(search_id) = id_search {
        server_list
            .into_iter()
            .filter(|server| server.id == search_id)
            .collect()
    } else {
        server_list
    };

    let mut server_list_rst = vec![];
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;

    for server in server_list {
        let is_online = get_number!(conn, format!("server:status:{}", server.id), 0) == 1;
        let used_ports = app_state
            .db
            .port()
            .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                server.id,
            ))])
            .exec()
            .await
            .map_err(Error::Database)?;
        let mut used_ports_rst = vec![];
        for port in used_ports {
            used_ports_rst.push(port.port_v_4);
        }

        let server_info = ServerInfo {
            id: server.id,
            display_name: if server.display_name.is_empty() {
                format!("Server {}", server.id)
            } else {
                server.display_name.clone()
            },
            ip_addr: server.ingress_ipv_4.clone(),
            is_online: Some(is_online),
            port_start: server.port_start,
            port_end: server.port_end,
            used_ports: Some(used_ports_rst),
            interface_name: server.interface_name.clone(),
            server_pubkey: server.pubkey,
            version: server.version.clone(),
            balance_strategy: server
                .proxy_config
                .as_ref()
                .and_then(|v| get_balance_strategy_from_config(&v)),
            forward_endpoints: Some(server.related_forward_endpoint_ids),
            traffic_scale: server.traffic_scale.map(|v| v as f32),
            allow_forward: server.allow_forward,
            allow_latency_test: server.allow_latency_test,
            allow_ipv6: Some(server.allow_ip_v_6),
            use_forward_as_tun: server.use_forward_as_tun,
        };

        // Apply search filters (skip if ID search is used since exact ID match takes priority)
        if id_search.is_none() {
            // Check name filter (fuzzy/regex)
            if let Some(name_pattern) = name_search {
                if !matches_regex_or_exact(&server_info.display_name, name_pattern) {
                    continue;
                }
            }

            // Check IP address filter (fuzzy/regex)
            if let Some(ip_pattern) = ip_addr_search {
                if !matches_regex_or_exact(&server_info.ip_addr, ip_pattern) {
                    continue;
                }
            }

            // Check version filter (multi-select with ANY logic)
            if let Some(versions) = version_filter {
                if !versions.is_empty() {
                    let server_version = server_info.version.as_deref().unwrap_or("Unknown");
                    if !versions.iter().any(|v| v == server_version) {
                        continue;
                    }
                }
            }

            // Check status filter (multi-select with ANY logic)
            if let Some(statuses) = status_filter {
                if !statuses.is_empty() {
                    let server_status = if server_info.is_online.unwrap_or(false) {
                        "Online"
                    } else {
                        "Offline"
                    };
                    if !statuses.iter().any(|s| s == server_status) {
                        continue;
                    }
                }
            }
        }

        server_list_rst.push(server_info);
    }

    // Sort results to prioritize exact matches (skip if ID search is used)
    if id_search.is_none() && (name_search.is_some() || ip_addr_search.is_some()) {
        server_list_rst.sort_by(|a, b| {
            let a_score = calculate_server_match_score(a, name_search, ip_addr_search);
            let b_score = calculate_server_match_score(b, name_search, ip_addr_search);
            b_score.cmp(&a_score) // Higher scores first
        });
    }

    // Apply pagination to filtered results
    let total_filtered = server_list_rst.len() as u32;
    let total_pages = if total_filtered == 0 {
        1
    } else {
        (total_filtered + page_size - 1) / page_size
    };

    let skip = ((page - 1) * page_size) as usize;
    let take = page_size as usize;
    let paginated_servers: Vec<ServerInfo> =
        server_list_rst.into_iter().skip(skip).take(take).collect();

    let pagination_info = PaginationInfo {
        current_page: page,
        page_size,
        total_items: total_filtered,
        total_pages,
    };

    let response = PaginatedServerResponse {
        servers: paginated_servers,
        pagination: pagination_info,
    };

    info!(
        "Successfully retrieved {} filtered servers (page {}/{}, total: {})",
        response.servers.len(),
        page,
        total_pages,
        total_filtered
    );
    Ok(warp::reply::json(&response))
}

pub async fn handle_get_all_server_list(
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // This endpoint returns all servers without pagination for admin use
    // Used specifically for populating search dropdowns with complete data

    let server_list = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
        }))
        .exec()
        .await
        .map_err(Error::Database)?;

    // Transform to simplified format for dropdown use
    let server_list_rst: Vec<_> = server_list
        .into_iter()
        .map(|server| {
            serde_json::json!({
                "id": server.id,
                "display_name": if server.display_name.is_empty() {
                    format!("Server {}", server.id)
                } else {
                    server.display_name
                }
            })
        })
        .collect();

    Ok(warp::reply::json(&serde_json::json!({
        "servers": server_list_rst
    })))
}

pub async fn handle_get_server_list(
    pagination: Option<PaginationRequest>,
    app_state: AppState,
) -> Result<warp::reply::Json, Rejection> {
    // Extract pagination parameters with defaults
    let page = pagination.as_ref().and_then(|p| p.page).unwrap_or(1);
    let page_size = pagination.as_ref().and_then(|p| p.page_size).unwrap_or(20);

    // Validate pagination parameters
    let page = if page < 1 { 1 } else { page };
    let page_size = match page_size {
        20 | 50 | 100 | 200 => page_size,
        _ => 20, // Default to 20 if invalid page size
    };

    // Calculate skip and take for pagination
    let skip = ((page - 1) * page_size) as i64;
    let take = page_size as i64;

    // Get total count of servers
    let total_count = app_state
        .db
        .outbound_endpoint()
        .count(vec![])
        .exec()
        .await
        .map_err(Error::Database)? as u32;

    let server_list = app_state
        .db
        .outbound_endpoint()
        .find_many(vec![])
        .order_by(prisma::outbound_endpoint::id::order(Direction::Desc))
        .skip(skip)
        .take(take)
        .select(prisma::outbound_endpoint::select!({
            id
            display_name
            ingress_ipv_4
            port_start
            port_end
            traffic_scale
            allow_forward
            allow_latency_test
            use_forward_as_tun
            interface_name
            pubkey
            version
            proxy_config
            related_forward_endpoint_ids
            allow_ip_v_6
        }))
        .exec()
        .await
        .map_err(Error::Database)?;
    let mut server_list_rst = vec![];
    let mut conn = app_state.redis_pool.get().await.map_err(Error::Redis)?;
    for server in server_list {
        let is_online = get_number!(conn, format!("server:status:{}", server.id), 0) == 1;
        let used_ports = app_state
            .db
            .port()
            .find_many(vec![prisma::port::outbound_endpoint_id::equals(Some(
                server.id,
            ))])
            .exec()
            .await
            .map_err(Error::Database)?;
        let mut used_ports_rst = vec![];
        for port in used_ports {
            used_ports_rst.push(port.port_v_4);
        }
        server_list_rst.push(ServerInfo {
            id: server.id,
            display_name: if server.display_name.is_empty() {
                format!("Server {}", server.id)
            } else {
                server.display_name
            },
            ip_addr: server.ingress_ipv_4,
            is_online: Some(is_online),
            port_start: server.port_start,
            port_end: server.port_end,
            used_ports: Some(used_ports_rst),
            interface_name: server.interface_name,
            server_pubkey: server.pubkey,
            version: server.version,
            balance_strategy: server
                .proxy_config
                .as_ref()
                .and_then(|v| get_balance_strategy_from_config(&v)),
            forward_endpoints: Some(server.related_forward_endpoint_ids),
            traffic_scale: server.traffic_scale.map(|v| v as f32),
            allow_forward: server.allow_forward,
            allow_latency_test: server.allow_latency_test,
            allow_ipv6: Some(server.allow_ip_v_6),
            use_forward_as_tun: server.use_forward_as_tun,
        });
    }

    // Check if pagination was requested
    if pagination.is_some() {
        // Calculate pagination info
        let total_pages = if total_count == 0 {
            1
        } else {
            (total_count + page_size - 1) / page_size
        };

        let pagination_info = PaginationInfo {
            current_page: page,
            page_size,
            total_items: total_count,
            total_pages,
        };

        let response = PaginatedServerResponse {
            servers: server_list_rst,
            pagination: pagination_info,
        };
        Ok(warp::reply::json(&response))
    } else {
        // Return old format for backward compatibility
        Ok(warp::reply::json(&ServerListResponse {
            servers: server_list_rst,
        }))
    }
}

pub async fn handle_add_server(
    app_state: AppState,
    token_id: String,
    add_server_request: AddServerRequest,
) -> Result<impl Reply, Rejection> {
    log::info!("user: {}, Add server: {:?}", token_id, add_server_request);
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(SubscriptionCommonInfo::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    let generate_port_forward_data = generate_port_forward_data(
        &token_id,
        add_server_request.forward_endpoints,
        add_server_request
            .balance_strategy
            .as_ref()
            .map(|x| *x as u32),
        &subscription,
        &app_state.db,
    )
    .await
    .map_err(|e| {
        log::error!("Failed to generate port forward data: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;

    add_machine(
        app_state.db.clone(),
        &add_server_request.display_name,
        &add_server_request.ip_addr,
        add_server_request.port_start.unwrap_or(30000),
        add_server_request.port_end.unwrap_or(31000),
        add_server_request.traffic_scale.unwrap_or(1.0),
        &token_id,
        &add_server_request.interface_name,
        add_server_request.allow_forward.unwrap_or(false),
        add_server_request.allow_latency_test.unwrap_or(true),
        generate_port_forward_data,
        add_server_request.use_forward_as_tun.unwrap_or(false),
    )
    .await
    .map_err(|e| {
        log::error!("Failed to add server: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_rmv_server(
    app_state: AppState,
    rmv_server_request: RmvServerRequest,
) -> Result<impl Reply, Rejection> {
    remove_line(app_state.db.clone(), rmv_server_request.server_id)
        .await
        .map_err(|e| {
            log::error!("Failed to remove server: {}", e);
            warp::reject::custom(Error::InternalError(e))
        })?;
    Ok(warp::reply::html("Ok"))
}
pub async fn handle_modify_server(
    token_id: String,
    app_state: AppState,
    modify_server_request: ModifyServerRequest,
) -> Result<impl Reply, Rejection> {
    let subscription = app_state
        .db
        .subscription()
        .find_unique(prisma::subscription::token_id::equals(token_id.clone()))
        .select(SubscriptionCommonInfo::select())
        .exec()
        .await
        .map_err(Error::Database)?
        .ok_or(Error::NotFound("Subscription not found".to_string()))?;

    let generate_port_forward_data = generate_port_forward_data(
        &token_id,
        modify_server_request.forward_endpoints,
        modify_server_request
            .balance_strategy
            .as_ref()
            .map(|x| *x as u32),
        &subscription,
        &app_state.db,
    )
    .await
    .map_err(|e| {
        log::error!("Failed to generate port forward data: {}", e);
        warp::reject::custom(Error::InternalError(e))
    })?;

    let mut set_args = vec![
        prisma::outbound_endpoint::display_name::set(modify_server_request.display_name),
        prisma::outbound_endpoint::ingress_ipv_4::set(modify_server_request.ip_addr),
    ];
    if let Some(interface_name) = modify_server_request.interface_name {
        set_args.push(prisma::outbound_endpoint::interface_name::set(Some(
            interface_name,
        )));
    }
    if let Some(port_start) = modify_server_request.port_start {
        set_args.push(prisma::outbound_endpoint::port_start::set(Some(port_start)));
    }
    if let Some(port_end) = modify_server_request.port_end {
        set_args.push(prisma::outbound_endpoint::port_end::set(Some(port_end)));
    }
    if let Some(traffic_scale) = modify_server_request.traffic_scale {
        set_args.push(prisma::outbound_endpoint::traffic_scale::set(Some(
            traffic_scale as f64,
        )));
    }
    if let Some(allow_forward) = modify_server_request.allow_forward {
        set_args.push(prisma::outbound_endpoint::allow_forward::set(Some(
            allow_forward,
        )));
    }
    if let Some(allow_ipv6) = modify_server_request.allow_ipv6 {
        set_args.push(prisma::outbound_endpoint::allow_ip_v_6::set(allow_ipv6));
    }

    if let Some(allow_latency_test) = modify_server_request.allow_latency_test {
        set_args.push(prisma::outbound_endpoint::allow_latency_test::set(Some(
            allow_latency_test,
        )));
    }
    if let Some(use_forward_as_tun) = modify_server_request.use_forward_as_tun {
        set_args.push(prisma::outbound_endpoint::use_forward_as_tun::set(Some(
            use_forward_as_tun,
        )));
    }
    if let Some((proxy_config, related_forward_endpoint_ids)) = generate_port_forward_data {
        set_args.push(prisma::outbound_endpoint::proxy_config::set(Some(
            base64::engine::general_purpose::STANDARD.encode(proxy_config),
        )));
        set_args.push(
            prisma::outbound_endpoint::related_forward_endpoint_ids::set(
                related_forward_endpoint_ids,
            ),
        );
    }
    app_state
        .db
        .outbound_endpoint()
        .update(
            prisma::outbound_endpoint::id::equals(modify_server_request.server_id),
            set_args,
        )
        .exec()
        .await
        .map_err(Error::Database)?;
    Ok(warp::reply::html("Ok"))
}

pub async fn handle_reset_user_traffic(
    reset_user_traffic_request: ResetUserTrafficRequest,
    reset_traffic_ctx: ArcResetTrafficContext,
) -> Result<impl Reply, Rejection> {
    reset_traffic_ctx
        .traffic_retry_queue
        .send(Box::new(ResetTrafficTask::new(
            reset_traffic_ctx.clone(),
            reset_user_traffic_request.user_id,
        )));
    Ok(warp::reply::html("Ok"))
}
