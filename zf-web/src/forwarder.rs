use std::{
    collections::HashMap,
    net::{Ip<PERSON>ddr, SocketAddr},
};

use common::{
    app_message::{
        LatencyTestMethod, TestLatencyInfo, TestLatencyType, WorkerTestLatencyRequest,
        WorkerTestLatencyResponse,
    },
    into_socket_addr, replace_placeholders, TotConfig,
};
use itertools::Itertools;
use private_tun::{
    address::Address,
    snell_impl_ver::{
        config::{ClientConfig, MultiServerConfig, RelayMethod, ServerConfig, ServerEndPoint},
        server_provider::{BalanceMode, Mode},
    },
};
use rand::{distr::Alphanumeric, rng, Rng};
use reqwest::Url;

use crate::prisma;

fn generate_password(length: usize) -> String {
    // 使用 thread_rng 生成随机数生成器，Alphanumeric 包含大小写字母及数字
    let password: String = rng()
        .sample_iter(&Alphanumeric)
        .take(length)
        .map(char::from)
        .collect();
    password
}

pub fn generate_hammer_server_config(port: u16) -> anyhow::Result<String> {
    let config = ServerConfig {
        bind: format!("[::0]:{}", port).parse()?,
        dual_stack: Some(true),
        password: generate_password(16).into_boxed_str(),
        obfs: false,
        relay_method: Some(RelayMethod::AsyncStream {
            buff_size: Some(16384),
        }),
        local_addr: None,
        v4_first: None,
        v6_first: None,
        brutal: None,
        stat: None,
        mptcp: None,
        breaker: None,
        enable_affinity: None,
        traffic_limit: None,
        udp_batch_size: None,
        encrypt_udp: None,
        udp_buffer_size: None,
        default_runtime: None,
        single_runtime: None,
        metric: None,
    };
    Ok(serde_json::to_string(&config)?)
}

pub fn generate_hammer_server_config_update_port(
    config: ServerConfig,
    port: u16,
) -> anyhow::Result<String> {
    let config = ServerConfig {
        bind: format!("[::0]:{}", port).parse()?,
        ..config
    };
    Ok(serde_json::to_string(&config)?)
}

pub fn generate_random_hammer_server_config() -> anyhow::Result<(u16, String)> {
    let port = rand::rng().random_range(10000..65535);
    Ok((port, generate_hammer_server_config(port)?))
}

pub async fn generate_setup_script(
    host_url: &str,
    proxy_config: &str,
    protocol: &str,
) -> anyhow::Result<String> {
    let config_file_name = format!("{}_script_template.sh", protocol);
    let host = format!("{}/{}", host_url, "downloads");
    log::info!("host: {}", host);
    log::info!("proxy_config: {}", proxy_config);
    let script_content = tokio::fs::read_to_string(config_file_name).await?;
    let replacements = HashMap::from([("HOST", host.as_str()), ("CONFIG_CONTENT", proxy_config)]);
    let script = replace_placeholders(&script_content, &replacements);
    Ok(script)
}
fn is_main_server_matched(config: &ClientConfig, id: i32, ip_addr: &str) -> bool {
    // 先检查 name
    if let Some(name) = &config.name {
        if name.trim() == id.to_string().trim() {
            return true;
        }
    }
    // 再检查 server_addr
    if config.server_addr.trim() == ip_addr.trim() {
        return true;
    }
    false
}

fn matched_backend<'a>(
    config: &'a mut ClientConfig,
    id: i32,
    ip_addr: &str,
) -> Option<&'a mut ServerEndPoint> {
    let Some(backends) = config.multi_server_config.as_mut() else {
        return None;
    };
    let id_string = id.to_string();
    for backend in backends.back_servers.iter_mut() {
        if let Some(name) = &backend.name {
            if name.trim() == id_string {
                return Some(backend);
            }
        }
        match &backend.addr_port {
            Address::Domain(d, _p) => {
                if d.trim() == ip_addr.trim() {
                    return Some(backend);
                }
            }
            Address::Socket(s) => {
                if s.ip().to_string().trim() == ip_addr.trim() {
                    return Some(backend);
                }
            }
        }
    }
    None
}
pub fn update_hammer_proxy_config(
    mut old_config: ClientConfig,
    id: i32,
    ip_addr: &str,
    server_port: u16,
    allow_ipv6: bool,
) -> anyhow::Result<String> {
    if is_main_server_matched(&old_config, id, ip_addr) {
        // update
        old_config.server_port = server_port;
        old_config.server_addr = ip_addr.to_string().into_boxed_str();
        old_config.allow_ipv6 = Some(allow_ipv6);
    } else {
        if let Some(backend) = matched_backend(&mut old_config, id, ip_addr) {
            backend.addr_port =
                Address::Socket(SocketAddr::new(ip_addr.parse().unwrap(), server_port));
            backend.allow_ipv6 = Some(allow_ipv6);
        }
    }
    Ok(serde_json::to_string(&old_config)?)
}

pub fn update_tot_proxy_config(
    mut old_config: TotConfig,
    id: i32,
    ip_addr: &str,
    server_port: u16,
    allow_ipv6: bool,
) -> anyhow::Result<String> {
    let config_clone = old_config.fwd_config.clone();
    let new_config =
        update_hammer_proxy_config(config_clone, id, ip_addr, server_port, allow_ipv6)?;
    old_config.fwd_config = serde_json::from_str(&new_config)?;
    let tot_config_clone = old_config.tot_server_list.clone();
    let new_tot_config =
        update_hammer_proxy_config(tot_config_clone, id, ip_addr, server_port, allow_ipv6)?;
    old_config.tot_server_list = serde_json::from_str(&new_tot_config)?;
    Ok(serde_json::to_string(&old_config)?)
}

prisma::forward_endpoint::select!(
    ForwardEndpointInfoForForwarder {
        id
        protocol_config
        ingress_address
        allow_ip_v_6
    }
);

pub fn generate_port_forward_config(
    forward_endpoints: &Vec<&ForwardEndpointInfoForForwarder::Data>,
    balance_strategy: Option<u32>,
) -> anyhow::Result<String> {
    let first = forward_endpoints
        .first()
        .ok_or(anyhow::anyhow!("No forward endpoint found"))?;
    let server_config = serde_json::from_str::<ServerConfig>(&first.protocol_config)?;
    let server_addr = first.ingress_address.to_string();
    let port = server_config.bind.port();
    let password = server_config.password.clone();
    let mut backends = Vec::new();
    for backend in forward_endpoints.iter().skip(1) {
        let server_config = serde_json::from_str::<ServerConfig>(&backend.protocol_config)?;
        let server_addr = backend.ingress_address.to_string();
        let port = server_config.bind.port();
        let password = server_config.password.clone();
        let addr = match server_addr.parse::<IpAddr>() {
            Ok(socket_addr) => Address::Socket(SocketAddr::new(socket_addr, port)),
            Err(_e) => Address::Domain(server_addr.into_boxed_str(), port),
        };
        backends.push(ServerEndPoint {
            addr_port: addr,
            password: password.clone(),
            allow_ipv6: Some(backend.allow_ip_v_6),
            name: Some(backend.id.to_string().into_boxed_str()),
            ..Default::default()
        });
    }
    let test_http_url = "https://cp.cloudflare.com/generate_204";
    let mode = match balance_strategy {
        Some(0) => Mode::BestLatency,
        Some(1) => Mode::Fallback,
        Some(2) => Mode::Balance {
            balance_mode: BalanceMode::DomainFollow,
        },
        Some(3) => Mode::Balance {
            balance_mode: BalanceMode::RoundRobin,
        },
        Some(4) => Mode::Balance {
            balance_mode: BalanceMode::Random,
        },
        _ => Mode::BestLatency,
    };
    let multi_server_config = if backends.len() >= 1 {
        Some(MultiServerConfig {
            back_servers: backends,
            test_http_url: Some(test_http_url.to_string().into_boxed_str()),
            mode,
            ..Default::default()
        })
    } else {
        None
    };
    let config = ClientConfig {
        server_addr: server_addr.into_boxed_str(),
        server_port: port,
        allow_ipv6: Some(first.allow_ip_v_6),
        name: Some(first.id.to_string().into_boxed_str()),
        local_bind: "[::0]:9300".parse().unwrap(),
        password: password.clone(),
        relay_method: Some(RelayMethod::AsyncStream {
            buff_size: Some(16384),
        }),
        multi_server_config,
        ..Default::default()
    };
    Ok(serde_json::to_string(&config)?)
}

// pub async fn test_remote_latency(
//     remote_addr: &str,
//     remote_web_port: u16,
//     target_addr: &str,
// ) -> anyhow::Result<u64> {
//     let client = reqwest::Client::new();
//     let resp = client
//         .get(format!("http://{}:{}/ping", remote_addr, remote_web_port))
//         .timeout(std::time::Duration::from_secs(10))
//         .query(&[("target", target_addr)])
//         .send()
//         .await?;
//     if resp.status().is_success() {
//         let result: PingResult = resp.json().await?;
//         Ok(result.rtt)
//     } else {
//         Err(anyhow::anyhow!("Failed to test remote latency"))
//     }
// }
pub async fn test_remote_latency(
    client: &reqwest::Client,
    mgmt_pubkey_str: &str,
    mgmt_addr: Url,
    remote: Vec<SocketAddr>,
    worker_id: i32,
    test_method: LatencyTestMethod,
) -> anyhow::Result<Vec<u64>> {
    let mut test_info = Vec::new();
    for r in remote {
        test_info.push(TestLatencyInfo {
            test_type: TestLatencyType::DirectRemote { remote: r },
            test_method,
        });
    }

    let resp = client
        .post(mgmt_addr.join("/test_latency")?)
        .header("zfc-pubkey", mgmt_pubkey_str)
        .json(&WorkerTestLatencyRequest {
            worker_id,
            fwd_configs: vec![],
            info: test_info,
        })
        .timeout(std::time::Duration::from_secs(30))
        .send()
        .await?;

    if resp.status().is_success() {
        let result: WorkerTestLatencyResponse = resp.json().await?;
        Ok(result.latencies)
    } else {
        Err(anyhow::anyhow!(
            "Failed to test entrylatency error: {}",
            resp.text().await?
        ))
    }
}

pub async fn collect_servers(config: &ClientConfig) -> anyhow::Result<HashMap<String, SocketAddr>> {
    let mut servers = HashMap::new();

    servers.insert(
        config
            .name
            .clone()
            .map(|x| x.to_string())
            .unwrap_or("main".to_string()),
        into_socket_addr(&Address::Domain(
            config.server_addr.clone(),
            config.server_port,
        ))
        .await?,
    );
    if let Some(multi_server_config) = &config.multi_server_config {
        for (idx, backend) in multi_server_config.back_servers.iter().enumerate() {
            let Ok(socket_addr) = into_socket_addr(&backend.addr_port).await else {
                log::error!("into_socket_addr failed: {}", backend.addr_port);
                continue;
            };
            servers.insert(
                backend
                    .name
                    .as_ref()
                    .map(|x| x.to_string())
                    .unwrap_or(format!("backend_{idx}")),
                socket_addr,
            );
        }
    }
    Ok(servers)
}

pub struct TestFwdAndRemoteLatencyResult {
    pub fwd_name: String,
    pub to_fwd_latency: u64,
    pub fwd_to_remote_latency: Vec<u64>,
}
pub async fn test_fwd_and_remote_latency(
    client: &reqwest::Client,
    mgmt_pubkey_str: &str,
    mgmt_addr: Url,
    forward_config: String,
    worker_id: i32,
    remote_address: Vec<SocketAddr>,
    test_method: LatencyTestMethod,
) -> anyhow::Result<Vec<TestFwdAndRemoteLatencyResult>> {
    let client_config = serde_json::from_str::<ClientConfig>(&forward_config)?;
    let fwd_list = collect_servers(&client_config).await?;
    let mut test_info = Vec::new();
    let mut fwd_names_list = Vec::new();
    for (fwd_name, _fwd_addr) in fwd_list.iter() {
        test_info.push(TestLatencyInfo {
            test_type: TestLatencyType::ToFwdServer {
                fwd_name: fwd_name.clone(),
            },
            test_method,
        });
        fwd_names_list.push(fwd_name.clone());
        for r in remote_address.iter() {
            test_info.push(TestLatencyInfo {
                test_type: TestLatencyType::FwdServerToRemote {
                    fwd_name: fwd_name.clone(),
                    remote: r.clone(),
                },
                test_method,
            });
        }
    }

    let resp = client
        .post(mgmt_addr.join("/test_latency")?)
        .header("zfc-pubkey", mgmt_pubkey_str)
        .json(&WorkerTestLatencyRequest {
            worker_id,
            fwd_configs: vec![forward_config],
            info: test_info,
        })
        .timeout(std::time::Duration::from_secs(10))
        .send()
        .await?;

    if resp.status().is_success() {
        let result: WorkerTestLatencyResponse = resp.json().await?;
        Ok(result
            .latencies
            .into_iter()
            .enumerate()
            .group_by(|(idx, _l)| (idx / (remote_address.len() + 1)))
            .into_iter()
            .map(|(group, mut latencies)| {
                let to_fwd_latency = latencies.next().unwrap().1;
                let fwd_to_remote_latency = Vec::from_iter(latencies.map(|(_, l)| l));
                TestFwdAndRemoteLatencyResult {
                    fwd_name: fwd_names_list[group].clone(),
                    to_fwd_latency,
                    fwd_to_remote_latency,
                }
            })
            .collect())
    } else {
        Err(anyhow::anyhow!(
            "Failed to test entrylatency error: {}",
            resp.text().await?
        ))
    }
}

pub fn get_blance_strategy_from_proxy_config(
    proxy_config: Option<&str>,
) -> anyhow::Result<Option<i32>> {
    if let Some(proxy_config) = proxy_config {
        let server_config = serde_json::from_str::<ClientConfig>(&proxy_config)?;
        Ok(server_config
            .multi_server_config
            .as_ref()
            .and_then(|x| match &x.mode {
                Mode::BestLatency => Some(0),
                Mode::Fallback => Some(1),
                Mode::Balance { balance_mode } => match balance_mode {
                    BalanceMode::DomainFollow => Some(2),
                    BalanceMode::RoundRobin => Some(3),
                    BalanceMode::Random => Some(4),
                },
                _ => None,
            }))
    } else {
        Ok(None)
    }
}
