use std::fs;
use std::path::Path;
use std::process::Command;

fn main() {
    // Only build frontend when embed feature is enabled
    if cfg!(feature = "embed") {
        println!("cargo:rerun-if-changed=../zfc-web-ui/src");
        println!("cargo:rerun-if-changed=../zfc-web-ui/package.json");
        println!("cargo:rerun-if-changed=../zfc-web-ui/vite.config.js");
        
        let frontend_dir = Path::new("../zfc-web-ui");
        let dist_dir = frontend_dir.join("dist");
        
        // Check if frontend directory exists
        if !frontend_dir.exists() {
            panic!("Frontend directory not found: {}", frontend_dir.display());
        }
        
        // Build frontend if dist doesn't exist or if source files are newer
        let should_build = !dist_dir.exists() || 
            needs_rebuild(&frontend_dir, &dist_dir);
        
        if should_build {
            println!("Building frontend...");
            
            // Install dependencies if node_modules doesn't exist
            let node_modules = frontend_dir.join("node_modules");
            if !node_modules.exists() {
                println!("Installing frontend dependencies...");
                let output = Command::new("npm")
                    .args(&["install"])
                    .current_dir(&frontend_dir)
                    .output()
                    .expect("Failed to run npm install");
                
                if !output.status.success() {
                    panic!("npm install failed: {}", String::from_utf8_lossy(&output.stderr));
                }
            }
            
            // Build frontend
            let output = Command::new("npm")
                .args(&["run", "build"])
                .current_dir(&frontend_dir)
                .output()
                .expect("Failed to run npm run build");
            
            if !output.status.success() {
                panic!("Frontend build failed: {}", String::from_utf8_lossy(&output.stderr));
            }
            
            println!("Frontend build completed successfully");
        }
        
        // Verify dist directory exists after build
        if !dist_dir.exists() {
            panic!("Frontend dist directory not found after build: {}", dist_dir.display());
        }
        
        // Tell warp-embed where to find the assets
        println!("cargo:rustc-env=FRONTEND_DIST_DIR={}", dist_dir.display());
    }
}

fn needs_rebuild(frontend_dir: &Path, dist_dir: &Path) -> bool {
    // Get the modification time of the dist directory
    let dist_mtime = match fs::metadata(dist_dir) {
        Ok(metadata) => metadata.modified().unwrap_or(std::time::UNIX_EPOCH),
        Err(_) => return true, // If dist doesn't exist, we need to build
    };
    
    // Check if any source files are newer than dist
    let src_dir = frontend_dir.join("src");
    if let Ok(entries) = fs::read_dir(&src_dir) {
        for entry in entries.flatten() {
            if let Ok(metadata) = entry.metadata() {
                if let Ok(mtime) = metadata.modified() {
                    if mtime > dist_mtime {
                        return true;
                    }
                }
            }
        }
    }
    
    // Check package.json and vite.config.js
    for file in &["package.json", "vite.config.js"] {
        let file_path = frontend_dir.join(file);
        if let Ok(metadata) = fs::metadata(&file_path) {
            if let Ok(mtime) = metadata.modified() {
                if mtime > dist_mtime {
                    return true;
                }
            }
        }
    }
    
    false
}
