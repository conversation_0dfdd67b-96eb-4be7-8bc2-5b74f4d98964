[workspace]
members = ["xtask", "zf-worker", "zf-controler", "common", "zfc-util", "prisma-cli", "zfc-admin", "zf-web", "forwarder-server"]
resolver = "2"

[profile.release]
opt-level = 3
lto = "fat"
strip = true
#debug = true
panic = "abort"
codegen-units = 1
overflow-checks = false

[workspace.dependencies]
private_tun = { path = "../private_tun", default-features=false, feautres = ["udp_opt_zfc"] }
tcp_over_multi_tcp_client = { path = "../private_tun/tcp_over_multi_tcp_client" }
tcp_over_multi_tcp_server = { path = "../private_tun/tcp_over_multi_tcp_server" }
parking_lot = "0.12"
tokio = { version = "1", features = ["macros", "rt", "rt-multi-thread", "net", "signal", "time", "process", "sync"] }

[env]
OPENSSL_LIB_DIR="/usr/lib/x86_64-linux-gnu"
OPENSSL_DIR="/usr"  