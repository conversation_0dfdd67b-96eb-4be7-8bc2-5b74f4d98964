<template>
   <div>
    <!-- 当 authStore.isInitialized 为 false 时显示加载状态 -->
    <template v-if="!authStore.isInitialized">
      <div class="loading-container">
        <div class="loading-content">
          <el-icon class="loading-spinner"><Loading /></el-icon>
          <div class="loading-text">Loading...</div>
        </div>
      </div>
    </template>
    <!-- 初始化完成后显示路由内容 -->
    <template v-else>
      <router-view></router-view>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useAuthStore } from './stores/auth'
import { storeToRefs } from 'pinia'
import { Loading } from '@element-plus/icons-vue'

const authStore = useAuthStore()
const isReady = ref(false)

// Initialize auth state before mounting app
const init = async () => {
  try {
    await authStore.initialize()
  } catch (error) {
    console.error('Failed to initialize auth:', error)
  } finally {
    isReady.value = true
  }
}

onMounted(() => {
  init()
})
</script>

<style>
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: var(--theme-bg-secondary);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.loading-spinner {
  font-size: 48px;
  color: var(--theme-primary);
  animation: rotate 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.loading-text {
  font-size: 16px;
  color: var(--theme-text-regular);
  font-weight: 500;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>
