import axios from 'axios'
import { useAuthStore } from '../stores/auth'
import router from '../router'
import { config } from '../config'

const api = axios.create({
  baseURL: config.apiUrl,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true
})

// Request interceptor
api.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  response => response,
  error => {
    console.error('Response error:', error.response || error)
    
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.logout()
      router.push('/login')
    }

    return Promise.reject({
      ...error,
      message: error.response?.data?.message || error.response?.statusText || error.message || 'An error occurred',
      errorCode: error.response?.data?.error_code || 'UNKNOWN_ERROR',
      status: error.response?.status
    })
  }
)

// API endpoints
export const login = (token) => api.post('/login', { token })
export const getSubscriptionInfo = () => api.get('/subscription')
export const getPorts = (params = {}) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page)
  if (params.page_size) queryParams.append('page_size', params.page_size)
  if (params.id) queryParams.append('id', params.id)
  if (params.name) queryParams.append('name', params.name)
  if (params.line) queryParams.append('line', params.line)
  if (params.entry_point) queryParams.append('entry_point', params.entry_point)
  if (params.target) queryParams.append('target', params.target)

  const queryString = queryParams.toString()
  return api.get(`/ports${queryString ? `?${queryString}` : ''}`)
}
export const createPort = (data) => api.post('/ports', data)
export const deletePort = (portId) => api.delete('/ports', { 
  data: { id: portId },
  headers: {
    'Content-Type': 'application/json'
  }
})
export const modifyPort = (data) => api.post('/mod_port', data)
export const suspendPort = (portId) => api.post('/suspend_port', { id: portId })
export const resumePort = (portId) => api.post('/resume_port', { id: portId })
export const getLineStats = () => api.get('/line_stats')
export const testLatency = (portId) => api.get('/test_latency', { params: { port_id: portId } })

// Forward Endpoint APIs
export const getForwardEndpoints = (params = {}) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page)
  if (params.page_size) queryParams.append('page_size', params.page_size)
  if (params.fetch_all) queryParams.append('fetch_all', params.fetch_all)

  // Add search parameters
  if (params.id) queryParams.append('id', params.id)
  if (params.name) queryParams.append('name', params.name)
  if (params.ingress_address) queryParams.append('ingress_address', params.ingress_address)

  const queryString = queryParams.toString()
  return api({
    url: `/forward_endpoints${queryString ? `?${queryString}` : ''}`,
    method: 'get'
  })
}
export const createForwardEndpoint = (data) => api.post('/forward_endpoints', data)
export const deleteForwardEndpoint = (id) => api.delete('/forward_endpoints', {
  data: { id },
  headers: {
    'Content-Type': 'application/json'
  }
})
export const modifyForwardEndpoint = (data) => api.post('/mod_forward_endpoint', data)

// Subscription Management APIs
export const getSubscriptionList = (params = {}) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page)
  if (params.page_size) queryParams.append('page_size', params.page_size)

  // Search parameters
  if (params.id) queryParams.append('id', params.id)
  if (params.token_id) queryParams.append('token_id', params.token_id)
  if (params.email) queryParams.append('email', params.email)
  if (params.valid_until_start) queryParams.append('valid_until_start', params.valid_until_start)
  if (params.valid_until_end) queryParams.append('valid_until_end', params.valid_until_end)
  if (params.next_reset_start) queryParams.append('next_reset_start', params.next_reset_start)
  if (params.next_reset_end) queryParams.append('next_reset_end', params.next_reset_end)
  if (params.lines && params.lines.length > 0) {
    params.lines.forEach(lineId => queryParams.append('lines', lineId))
  }

  const queryString = queryParams.toString()
  return api.get(`/subscription_list${queryString ? `?${queryString}` : ''}`)
}

export const addUser = (data) => {
  return api.post('/add_user', data)
}

export const editUser = (data) => {
  return api.put('/edit_user', data)
}

export const removeUser = (userId) => {
  return api.delete(`/rmv_user?user_id=${userId}`)
}

export const extendSubscriptionTime = (userId, days = null) => {
  return api.post('/extend_subscription_time', {
    user_id: userId,
    days: days
  })
}

export const resetUserTraffic = (userId) => {
  return api.post('/reset_user_traffic', { user_id: userId })
}

// Server Management APIs
export const getServerList = (params = {}) => {
  const queryParams = new URLSearchParams()
  if (params.page) queryParams.append('page', params.page)
  if (params.page_size) queryParams.append('page_size', params.page_size)

  // Add search parameters
  if (params.id) queryParams.append('id', params.id)
  if (params.name) queryParams.append('name', params.name)
  if (params.ip_addr) queryParams.append('ip_addr', params.ip_addr)
  if (params.version && params.version.length > 0) {
    queryParams.append('version', params.version.join(','))
  }
  if (params.status && params.status.length > 0) {
    queryParams.append('status', params.status.join(','))
  }

  const queryString = queryParams.toString()
  return api.get(`/server_list${queryString ? `?${queryString}` : ''}`)
}

// Get all servers without pagination for dropdown use (admin only)
export const getAllServerList = () => {
  return api.get('/server_list_all')
}

export const addServer = (data) => {
  return api.post('/add_server', data)
}

export const removeServer = (serverId) => {
  return api.delete('/rmv_server', {
    params: { server_id: serverId }
  })
}

export const modifyServer = (serverData) => {
  return api.post('/mod_server', serverData)
}