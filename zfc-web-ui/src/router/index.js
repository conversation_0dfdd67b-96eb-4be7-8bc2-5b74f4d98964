import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '../stores/auth'
import { useSubscriptionStore } from '../stores/subscription'
import { ElMessage } from 'element-plus'
import 'element-plus/es/components/message/style/css'

const routes = [
  {
    path: '/',
    name: 'login',
    component: () => import('../views/Login.vue'),
    meta: { requiresGuest: true }
  },
  {
    path: '/theme-test',
    name: 'theme-test',
    component: () => import('../views/ThemeTest.vue')
  },
  {
    path: '/server-management-test',
    name: 'server-management-test',
    component: () => import('../views/ServerManagementTest.vue')
  },
  {
    path: '/loading-test',
    name: 'loading-test',
    component: () => import('../views/LoadingTest.vue')
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/Dashboard.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'subscription',
        component: () => import('../views/Subscription.vue')
      },
      {
        path: 'ports',
        name: 'ports',
        component: () => import('../views/Ports.vue')
      },
      {
        path: 'forward-endpoints',
        name: 'forward-endpoints',
        component: () => import('../views/ForwardEndpoints.vue')
      },
      {
        path: 'subscription-management',
        name: 'subscription-management',
        component: () => import('../views/SubscriptionManagement.vue'),
        meta: { requiresAdmin: true }
      },
      {
        path: 'server-management',
        name: 'server-management',
        component: () => import('../views/ServerManagement.vue'),
        meta: { requiresAdmin: true }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const subscriptionStore = useSubscriptionStore()
  
  // Wait for auth store to initialize
  if (!authStore.isInitialized) {
    await authStore.initialize()
  }

  // If route requires auth, ensure subscription data is loaded
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)
  const requiresGuest = to.matched.some(record => record.meta.requiresGuest)
  const requiresAdmin = to.matched.some(record => record.meta.requiresAdmin)

  if (requiresAuth && authStore.isAuthenticated) {
    await subscriptionStore.fetchData()
  }

  if (requiresGuest && authStore.isAuthenticated) {
    next('/dashboard')
  } else if (requiresAuth && !authStore.isAuthenticated) {
    next('/')
  } else if (requiresAdmin && !subscriptionStore.isAdmin) {
    ElMessage.warning('You do not have permission to access this page')
    next('/dashboard')
  } else {
    next()
  }
})

export default router