/* Global styles using theme variables */
:root {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Use theme variables */
  color: var(--theme-text-primary);
  background-color: var(--theme-bg-primary);
}

/* Reset and base styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

body {
  min-width: 320px;
  min-height: 100vh;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  background-color: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  color: var(--theme-text-primary);
  margin: 0;
}

h1 {
  font-size: 2.5em;
  line-height: 1.1;
}

h2 {
  font-size: 2em;
  line-height: 1.2;
}

/* Links */
a {
  font-weight: 500;
  color: var(--theme-primary);
  text-decoration: inherit;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--theme-primary-light);
}

/* Buttons (fallback for non-Element Plus buttons) */
button:not(.el-button) {
  border-radius: var(--border-radius-base);
  border: 1px solid var(--theme-border-base);
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: var(--theme-button-bg);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: all 0.3s ease;
}

button:not(.el-button):hover {
  border-color: var(--theme-primary);
  background-color: var(--theme-fill-light);
}

button:not(.el-button):focus,
button:not(.el-button):focus-visible {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

/* Cards */
.card {
  padding: 2em;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  border-radius: var(--border-radius-large);
  box-shadow: var(--theme-shadow-light);
}

/* Scrollbars */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--theme-fill-light);
}

::-webkit-scrollbar-thumb {
  background: var(--theme-fill-dark);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--theme-fill-darker);
}
