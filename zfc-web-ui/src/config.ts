// For embedded mode, use relative URLs to automatically connect to the same origin
// For separate deployment, use environment variable or default to localhost:3030
const getApiUrl = () => {
  // If VITE_API_URL is explicitly set, use it (for separate deployment)
  if (import.meta.env.VITE_API_URL) {
    return import.meta.env.VITE_API_URL;
  }

  // For embedded mode, use relative URL (same origin as the page)
  if (typeof window !== 'undefined') {
    return `${window.location.origin}/api`;
  }

  // Fallback for SSR or build-time
  return '/api';
};

export const config = {
  apiUrl: getApiUrl(),
}

export const API_HOST = config.apiUrl.split('/api')[0] || (typeof window !== 'undefined' ? window.location.origin : '')

export default config