<template>
  <div class="server-status-card" :class="{ 'offline': !isOnline }">
    <div class="card-content">
      <div class="card-header">
        <div class="server-name">{{ name }}</div>
        <el-tooltip :content="`在线时间: ${formatUptime(uptime)}`" placement="top">
          <div class="uptime">{{ formatUptime(uptime) }}</div>
        </el-tooltip>
      </div>
      <div class="stats-grid">
        <div class="stats-item">
          <span class="icon">
            <el-icon><cpu /></el-icon>
          </span>
          <div class="progress-container">
            <div class="label">CPU {{ cpuUsage.toFixed(2) }}%</div>
            <el-progress 
              :percentage="cpuUsage" 
              :show-text="false"
              :color="'#409EFF'"
              :stroke-width="8"
            />
          </div>
        </div>
        <div class="stats-item">
          <span class="icon">
            <el-icon><memo /></el-icon>
          </span>
          <div class="progress-container">
            <el-tooltip
              :content="`${formatTraffic(memoryUsed)} / ${formatTraffic(memoryTotal)}`"
              placement="top"
            >
              <div class="label">MEM {{ memoryUsage.toFixed(2) }}%</div>
            </el-tooltip>
            <el-progress 
              :percentage="memoryUsage" 
              :show-text="false"
              :color="'#F56C6C'"
              :stroke-width="8"
            />
          </div>
        </div>
      </div>
      <div class="network-section">
        <div class="speed-row">
          <div class="speed-label">
            <el-icon><download /></el-icon>
          </div>
          <div class="speed-values">
            <span class="speed download">{{ formatSpeed(downSpeed) }}</span>
            <span class="total-traffic download">{{ formatTraffic(totalDown) }}</span>
          </div>
        </div>
        <div class="speed-row">
          <div class="speed-label">
            <el-icon><upload /></el-icon>
          </div>
          <div class="speed-values">
            <span class="speed upload">{{ formatSpeed(upSpeed) }}</span>
            <span class="total-traffic upload">{{ formatTraffic(totalUp) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { Cpu, Memo, Download, Upload } from '@element-plus/icons-vue'

const props = defineProps({
  name: {
    type: String,
    required: true
  },
  uptime: {
    type: Number,
    default: 0
  },
  downSpeed: {
    type: Number,
    default: 0
  },
  upSpeed: {
    type: Number,
    default: 0
  },
  totalDown: {
    type: Number,
    default: 0
  },
  totalUp: {
    type: Number,
    default: 0
  },
  cpuUsage: {
    type: Number,
    default: 0
  },
  memoryTotal: {
    type: Number,
    default: 0
  },
  memoryUsed: {
    type: Number,
    default: 0
  },
  isOnline: {
    type: Boolean,
    default: true
  }
})

const memoryUsage = computed(() => {
  if (props.memoryTotal === 0) return 0
  return (props.memoryUsed / props.memoryTotal) * 100
})

const formatSpeed = (bytesPerSec) => {
  if (!bytesPerSec && bytesPerSec !== 0) return 'NaN'
  
  const units = ['B/s', 'KB/s', 'MB/s', 'GB/s']
  let value = bytesPerSec
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(2)} ${units[unitIndex]}`
}

const formatTraffic = (bytes) => {
  if (!bytes && bytes !== 0) return 'NaN'
  const units = ['B', 'KB', 'MB', 'GB', 'TB']
  let value = bytes
  let unitIndex = 0
  
  while (value >= 1024 && unitIndex < units.length - 1) {
    value /= 1024
    unitIndex++
  }
  
  return `${value.toFixed(2)}${units[unitIndex]}`
}

const formatUptime = (seconds) => {
  if (!seconds) return '0s'
  
  const days = Math.floor(seconds / (24 * 3600))
  const hours = Math.floor((seconds % (24 * 3600)) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  
  const parts = []
  if (days > 0) parts.push(`${days}d`)
  if (hours > 0) parts.push(`${hours}h`)
  if (minutes > 0) parts.push(`${minutes}m`)
  
  return parts.join(' ') || '< 1m'
}
</script>

<style scoped>
.server-status-card {
  background: var(--el-bg-color);
  border-radius: 8px;
  padding: 20px;
  box-shadow: var(--el-box-shadow-light);
  transition: all 0.3s ease;
}

.offline {
  opacity: 0.5;
  background: var(--el-fill-color-lighter);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.server-name {
  font-size: 18px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.uptime {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.stats-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stats-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon {
  font-size: 20px;
  color: var(--el-text-color-secondary);
  width: 24px;
  text-align: center;
}

.progress-container {
  flex: 1;
}

.label {
  font-size: 14px;
  margin-bottom: 4px;
  color: var(--el-text-color-regular);
}

.network-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.speed-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.speed-label {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.speed-values {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.speed {
  font-family: monospace;
  font-size: 16px;
}

.total-traffic {
  font-family: monospace;
  font-size: 14px;
  opacity: 0.8;
}

.download {
  color: #409EFF;
}

.upload {
  color: #F56C6C;
}

:deep(.el-icon) {
  vertical-align: middle;
}
</style>
