<template>
  <div class="theme-toggle">
    <!-- Simple toggle button (for header/compact spaces) -->
    <el-button
      v-if="variant === 'button'"
      :type="isDark ? 'primary' : 'default'"
      :icon="currentIcon"
      circle
      @click="toggleTheme"
      :title="`Switch to ${isDark ? 'light' : 'dark'} mode`"
      class="theme-toggle-btn"
    />
    
    <!-- Dropdown with all options (for settings/preferences) -->
    <el-dropdown
      v-else-if="variant === 'dropdown'"
      @command="setTheme"
      trigger="click"
      class="theme-dropdown"
    >
      <el-button :icon="currentIcon">
        {{ currentThemeLabel }}
        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </el-button>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="option in themeOptions"
            :key="option.value"
            :command="option.value"
            :class="{ 'is-active': themePreference === option.value }"
          >
            <el-icon><component :is="getIconComponent(option.icon)" /></el-icon>
            {{ option.label }}
            <el-icon v-if="themePreference === option.value" class="check-icon">
              <Check />
            </el-icon>
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    
    <!-- Switch style toggle -->
    <div v-else-if="variant === 'switch'" class="theme-switch">
      <el-icon class="theme-icon"><Sunny /></el-icon>
      <el-switch
        v-model="isDarkMode"
        @change="handleSwitchChange"
        :active-icon="Moon"
        :inactive-icon="Sunny"
        inline-prompt
      />
      <el-icon class="theme-icon"><Moon /></el-icon>
    </div>
    
    <!-- Icon-only toggle (minimal) -->
    <el-icon
      v-else
      :class="['theme-icon-toggle', { 'is-dark': isDark }]"
      @click="toggleTheme"
      :title="`Switch to ${isDark ? 'light' : 'dark'} mode`"
    >
      <component :is="currentIcon" />
    </el-icon>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useThemeStore } from '../stores/theme'
import {
  Sunny,
  Moon,
  Monitor,
  ArrowDown,
  Check
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  variant: {
    type: String,
    default: 'button', // 'button', 'dropdown', 'switch', 'icon'
    validator: (value) => ['button', 'dropdown', 'switch', 'icon'].includes(value)
  }
})

// Theme store
const themeStore = useThemeStore()
const {
  themePreference,
  currentTheme,
  isDark,
  isLight,
  themeOptions
} = storeToRefs(themeStore)

const { setTheme, toggleTheme } = themeStore

// Computed properties
const currentIcon = computed(() => {
  if (themePreference.value === 'auto') return Monitor
  return isDark.value ? Moon : Sunny
})

const currentThemeLabel = computed(() => {
  const option = themeOptions.find(opt => opt.value === themePreference.value)
  return option ? option.label : 'Theme'
})

// For switch variant
const isDarkMode = ref(isDark.value)

const handleSwitchChange = (value) => {
  setTheme(value ? 'dark' : 'light')
  isDarkMode.value = value
}

// Icon component helper
const getIconComponent = (iconName) => {
  const iconMap = {
    'Sunny': Sunny,
    'Moon': Moon,
    'Monitor': Monitor
  }
  return iconMap[iconName] || Monitor
}
</script>

<style scoped>
.theme-toggle {
  display: inline-flex;
  align-items: center;
}

.theme-toggle-btn {
  transition: all 0.3s ease;
}

.theme-toggle-btn:hover {
  transform: scale(1.05);
}

.theme-dropdown .el-dropdown-item.is-active {
  background-color: var(--el-color-primary-light-9);
  color: var(--el-color-primary);
}

.theme-dropdown .check-icon {
  margin-left: auto;
  color: var(--el-color-primary);
}

.theme-switch {
  display: flex;
  align-items: center;
  gap: 8px;
}

.theme-switch .theme-icon {
  color: var(--el-text-color-regular);
  transition: color 0.3s ease;
}

.theme-icon-toggle {
  font-size: 20px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  color: var(--el-text-color-regular);
}

.theme-icon-toggle:hover {
  background-color: var(--el-fill-color-light);
  color: var(--el-color-primary);
  transform: scale(1.1);
}

.theme-icon-toggle.is-dark {
  color: var(--el-color-warning);
}

/* Animation for icon changes */
.theme-toggle .el-icon {
  transition: all 0.3s ease;
}

/* Dark mode specific styles */
:global(.dark) .theme-toggle-btn {
  border-color: var(--el-border-color);
}

:global(.dark) .theme-icon-toggle:hover {
  background-color: var(--el-fill-color-darker);
}
</style>
