import { defineStore } from 'pinia'
import { login, getSubscriptionInfo } from '../api'
import { jwtDecode } from 'jwt-decode'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    token: null,
    tokenData: null,
    subscriptionData: null,
    isInitialized: false,
    isInitializing: false
  }),

  getters: {
    isAuthenticated: (state) => !!state.token && !state.isTokenExpired,
    tokenId: (state) => state.tokenData?.token_id,
    tokenExpiration: (state) => state.tokenData?.exp,
    isTokenExpired: (state) => {
      if (!state.tokenData?.exp) return true
      return Date.now() >= state.tokenData.exp * 1000
    }
  },
  
  actions: {
    async initialize() {
      if (this.isInitialized || this.isInitializing) return
      
      this.isInitializing = true
      try {
        const token = localStorage.getItem('token')
        if (token) {
          try {
            const decoded = jwtDecode(token)
            if (Date.now() >= decoded.exp * 1000) {
              this.clearState()
            } else {
              this.token = token
              this.tokenData = decoded
              await this.fetchSubscriptionData()
            }
          } catch {
            this.clearState()
          }
        }
      } finally {
        this.isInitializing = false
        this.isInitialized = true
      }
    },

    clearState() {
      this.token = null
      this.tokenData = null
      this.subscriptionData = null
      localStorage.removeItem('token')
    },

    async fetchSubscriptionData() {
      if (!this.isAuthenticated) {
        this.subscriptionData = null
        return
      }
      
      try {
        const { data } = await getSubscriptionInfo()
        this.subscriptionData = data
      } catch (error) {
        console.error('Failed to fetch subscription data:', error)
        this.subscriptionData = null
      }
    },

    async setToken(token) {
      if (token) {
        try {
          const decoded = jwtDecode(token)
          if (Date.now() >= decoded.exp * 1000) {
            this.clearState()
            return false
          }
          this.token = token
          this.tokenData = decoded
          localStorage.setItem('token', token)
          await this.fetchSubscriptionData()
          return true
        } catch {
          this.clearState()
          return false
        }
      } else {
        this.clearState()
        return false
      }
    },

    async login(subscriptionToken) {
      try {
        const { data } = await login(subscriptionToken)
        const success = await this.setToken(data.jwt)
        if (!success) {
          throw new Error('Invalid token received from server')
        }
        return true
      } catch (error) {
        console.error('Login failed:', error)
        throw error
      }
    },

    logout() {
      this.clearState()
    }
  }
})