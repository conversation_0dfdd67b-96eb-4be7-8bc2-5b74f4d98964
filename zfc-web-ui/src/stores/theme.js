import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'
import { usePreferredColorScheme, useStorage } from '@vueuse/core'

export const useThemeStore = defineStore('theme', () => {
  // Get system preference
  const systemPreference = usePreferredColorScheme()
  
  // Store theme preference in localStorage with 'auto' as default
  const themePreference = useStorage('theme-preference', 'auto')
  
  // Current active theme (computed based on preference and system)
  const currentTheme = computed(() => {
    if (themePreference.value === 'auto') {
      return systemPreference.value === 'dark' ? 'dark' : 'light'
    }
    return themePreference.value
  })
  
  // Boolean helpers for easier template usage
  const isDark = computed(() => currentTheme.value === 'dark')
  const isLight = computed(() => currentTheme.value === 'light')
  const isAuto = computed(() => themePreference.value === 'auto')
  
  // Theme options for UI
  const themeOptions = [
    { value: 'light', label: 'Light', icon: 'Sunny' },
    { value: 'dark', label: 'Dark', icon: 'Moon' },
    { value: 'auto', label: 'Auto', icon: 'Monitor' }
  ]
  
  // Apply theme to document
  const applyTheme = (theme) => {
    const root = document.documentElement
    
    // Remove existing theme classes
    root.classList.remove('light-theme', 'dark-theme')
    
    // Add current theme class
    root.classList.add(`${theme}-theme`)
    
    // Set data attribute for CSS selectors
    root.setAttribute('data-theme', theme)
    
    // Update Element Plus namespace if needed
    if (theme === 'dark') {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }
  
  // Set theme preference
  const setTheme = (theme) => {
    if (['light', 'dark', 'auto'].includes(theme)) {
      themePreference.value = theme
    }
  }
  
  // Toggle between light and dark (skips auto)
  const toggleTheme = () => {
    if (currentTheme.value === 'light') {
      setTheme('dark')
    } else {
      setTheme('light')
    }
  }
  
  // Cycle through all theme options
  const cycleTheme = () => {
    const currentIndex = themeOptions.findIndex(option => option.value === themePreference.value)
    const nextIndex = (currentIndex + 1) % themeOptions.length
    setTheme(themeOptions[nextIndex].value)
  }
  
  // Initialize theme on store creation
  const initializeTheme = () => {
    applyTheme(currentTheme.value)
  }
  
  // Watch for theme changes and apply them
  watch(currentTheme, (newTheme) => {
    applyTheme(newTheme)
  }, { immediate: true })
  
  // Watch for system preference changes when in auto mode
  watch(systemPreference, () => {
    if (themePreference.value === 'auto') {
      applyTheme(currentTheme.value)
    }
  })
  
  return {
    // State
    themePreference,
    currentTheme,
    systemPreference,
    
    // Computed
    isDark,
    isLight,
    isAuto,
    themeOptions,
    
    // Actions
    setTheme,
    toggleTheme,
    cycleTheme,
    initializeTheme,
    applyTheme
  }
})
