import { defineStore } from 'pinia'
import { getSubscriptionInfo } from '../api'

export const useSubscriptionStore = defineStore('subscription', {
  state: () => ({
    data: null,
    loading: false,
    error: null,
    lastFetchTime: null
  }),

  getters: {
    isAdmin: (state) => state.data?.is_admin ?? false,
    allowForwardEndpoint: (state) => state.data?.allow_forward_endpoint ?? false
  },

  actions: {
    async fetchData(force = false) {
      // If we already have data and it's not forced, skip fetching
      if (this.data && !force && Date.now() - this.lastFetchTime < 30000) {
        return
      }

      this.loading = true
      this.error = null

      try {
        const { data } = await getSubscriptionInfo()
        this.data = data
        this.lastFetchTime = Date.now()
      } catch (error) {
        console.error('Failed to fetch subscription info:', error)
        this.error = error
        this.data = null
      } finally {
        this.loading = false
      }
    },

    clearData() {
      this.data = null
      this.loading = false
      this.error = null
      this.lastFetchTime = null
    }
  }
})
