<template>
  <div class="server-management-test">
    <div class="header">
      <h2>Server Management Dark Mode Test</h2>
      <div class="theme-controls">
        <ThemeToggle variant="button" />
      </div>
    </div>
    
    <el-card class="table-card">
      <el-table
        :data="testServers"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        class="server-table"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="display_name"
          label="Name"
          min-width="120"
        />
        <el-table-column
          prop="ip_addr"
          label="IP Address"
          min-width="140"
        />
        <el-table-column
          prop="version"
          label="Version"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              size="small"
              :type="scope.row.version ? 'info' : 'danger'"
              effect="plain"
            >
              {{ scope.row.version || 'Unknown' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          prop="is_online"
          label="Status"
          width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag :type="scope.row.is_online ? 'success' : 'danger'">
              {{ scope.row.is_online ? 'Online' : 'Offline' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="Ports"
          min-width="180"
        >
          <template #default="scope">
            <div class="ports-info">
              <el-popover
                placement="top"
                :width="200"
                trigger="hover"
                popper-class="ports-popover"
              >
                <template #reference>
                  <el-tag
                    type="info"
                    class="ports-count"
                    effect="plain"
                  >
                    {{ scope.row.used_ports?.length || 0 }} / {{ getPortsTotal(scope.row) }} Ports
                  </el-tag>
                </template>
                <template #default>
                  <div class="ports-list">
                    <div class="ports-list-header">Port Details</div>
                    <el-scrollbar max-height="200px">
                      <div 
                        v-for="port in scope.row.used_ports" 
                        :key="port"
                        class="port-item"
                      >
                        Port {{ port }}
                      </div>
                      <div v-if="!scope.row.used_ports?.length" class="port-item">
                        No ports in use
                      </div>
                    </el-scrollbar>
                  </div>
                </template>
              </el-popover>
            </div>
          </template>
        </el-table-column>
        <el-table-column 
          label="Actions" 
          width="140"
          align="center"
        >
          <template #default="scope">
            <el-space>
              <el-tooltip 
                content="Copy Installation Command" 
                placement="top"
              >
                <el-button
                  size="small"
                  type="primary"
                  :icon="Document"
                  circle
                />
              </el-tooltip>
              <el-tooltip 
                content="Edit Server" 
                placement="top"
              >
                <el-button
                  size="small"
                  type="warning"
                  :icon="Edit"
                  circle
                />
              </el-tooltip>
              <el-button
                size="small"
                type="danger"
                :icon="Delete"
                circle
              />
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="demo-card" style="margin-top: 20px;">
      <template #header>
        <h3>Server Management Theme Demo</h3>
      </template>
      <div class="demo-content">
        <div class="demo-section">
          <h4>Status Tags</h4>
          <div class="tag-demo">
            <el-tag type="success">Online</el-tag>
            <el-tag type="danger">Offline</el-tag>
            <el-tag type="info">v1.2.3</el-tag>
            <el-tag type="warning">Unknown</el-tag>
          </div>
        </div>
        
        <div class="demo-section">
          <h4>Action Buttons</h4>
          <div class="button-demo">
            <el-button size="small" type="primary" :icon="Document" circle />
            <el-button size="small" type="warning" :icon="Edit" circle />
            <el-button size="small" type="danger" :icon="Delete" circle />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useThemeStore } from '../stores/theme'
import { Document, Edit, Delete } from '@element-plus/icons-vue'
import ThemeToggle from '../components/ThemeToggle.vue'

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Computed style for table headers - borderless design
const headerCellStyle = computed(() => ({
  background: isDark.value ? 'var(--theme-fill-dark)' : 'var(--theme-fill-light)',
  color: 'var(--theme-text-primary)',
  fontWeight: '600',
  border: 'none'
}))

const getPortsTotal = (server) => {
  if (!server.port_start || !server.port_end) return 0
  return server.port_end - server.port_start + 1
}

// Test data
const testServers = [
  {
    id: 1,
    display_name: 'Production Server',
    ip_addr: '*************',
    version: 'v1.2.3',
    is_online: true,
    port_start: 30000,
    port_end: 31000,
    used_ports: [30001, 30002, 30005, 30010]
  },
  {
    id: 2,
    display_name: 'Development Server',
    ip_addr: '*************',
    version: 'v1.2.2',
    is_online: true,
    port_start: 31000,
    port_end: 32000,
    used_ports: [31001, 31002]
  },
  {
    id: 3,
    display_name: 'Test Server',
    ip_addr: '*************',
    version: null,
    is_online: false,
    port_start: 32000,
    port_end: 33000,
    used_ports: []
  },
  {
    id: 4,
    display_name: 'Staging Server',
    ip_addr: '*************',
    version: 'v1.3.0-beta',
    is_online: true,
    port_start: 33000,
    port_end: 34000,
    used_ports: [33001, 33002, 33003, 33004, 33005]
  }
]
</script>

<style scoped>
.server-management-test {
  padding: 20px;
  background-color: var(--theme-bg-secondary);
  min-height: 100vh;
  transition: background-color 0.3s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

.demo-card {
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
}

.demo-content {
  padding: 20px;
}

.demo-section {
  margin-bottom: 20px;
}

.demo-section h4 {
  margin: 0 0 10px 0;
  color: var(--theme-text-primary);
  font-size: 16px;
  font-weight: 600;
}

.tag-demo, .button-demo {
  display: flex;
  gap: 10px;
  align-items: center;
}

.ports-info {
  display: flex;
  align-items: center;
}

.ports-count {
  cursor: pointer;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-regular) !important;
  border: 1px solid var(--theme-border-base) !important;
  transition: all 0.3s ease;
}

.ports-count:hover {
  background-color: var(--theme-fill-base) !important;
  border-color: var(--theme-primary) !important;
}

:global(.dark) .ports-count {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: 1px solid var(--theme-border-dark) !important;
}

:global(.dark) .ports-count:hover {
  background-color: var(--theme-fill-darker) !important;
  border-color: var(--theme-primary) !important;
}

/* Clean borderless table styling matching ForwardEndpointManagement */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
  transition: none;
}

:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

/* Popover styling */
:deep(.ports-popover) {
  padding: 0;
  background-color: var(--theme-bg-primary) !important;
  border: 1px solid var(--theme-border-base) !important;
  box-shadow: var(--theme-shadow-base) !important;
}

:global(.dark) :deep(.ports-popover) {
  background-color: var(--theme-bg-tertiary) !important;
  border: 1px solid var(--theme-border-dark) !important;
  box-shadow: var(--theme-shadow-dark) !important;
}

.ports-list-header {
  padding: 8px 12px;
  font-weight: 600;
  border-bottom: 1px solid var(--theme-border-base);
  background-color: var(--theme-fill-light);
  color: var(--theme-text-primary);
  border-radius: 4px 4px 0 0;
}

.port-item {
  padding: 8px 12px;
  font-size: 13px;
  color: var(--theme-text-regular);
  border-bottom: 1px solid var(--theme-border-light);
  background-color: var(--theme-bg-primary);
  transition: background-color 0.2s ease;
  font-weight: 500;
}

.port-item:hover {
  background-color: var(--theme-fill-extra-light);
}

.port-item:last-child {
  border-bottom: none;
}

/* Enhanced elements */
:deep(.el-tag) {
  transition: all 0.3s ease;
  font-weight: 500;
}

:deep(.el-tag:hover) {
  transform: translateY(-1px);
  box-shadow: var(--theme-shadow-light);
}

:deep(.el-button) {
  transition: all 0.3s ease;
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
}

:deep(.el-button.is-circle:hover) {
  box-shadow: var(--theme-shadow-base);
}
</style>
