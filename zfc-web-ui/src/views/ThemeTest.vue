<template>
  <div class="theme-test">
    <div class="header">
      <h2>Dark Mode Table Test</h2>
      <div class="theme-controls">
        <ThemeToggle variant="button" />
      </div>
    </div>
    
    <el-card class="table-card">
      <el-table
        :data="testData"
        style="width: 100%"
        :header-cell-style="headerCellStyle"
        class="subscription-table"
      >
        <el-table-column
          prop="id"
          label="ID"
          width="80"
        />
        <el-table-column
          prop="email"
          label="Email"
          min-width="180"
        />
        <el-table-column
          label="Traffic Usage"
          min-width="300"
          align="center"
        >
          <template #default="scope">
            <div class="traffic-progress">
              <div class="progress-wrapper">
                <div 
                  class="progress-bar" 
                  :style="{ width: `${scope.row.percentage}%` }"
                  :class="{ 'zero-progress': scope.row.percentage === 0 }"
                >
                </div>
                <span class="progress-text">
                  {{ scope.row.used }} / {{ scope.row.total }} ({{ scope.row.percentage }}%)
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="Status"
          width="120"
          align="center"
        >
          <template #default="scope">
            <el-tag
              :type="scope.row.status === 'Active' ? 'success' : 'danger'"
              class="status-tag"
            >
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="Actions"
          width="180"
          align="center"
        >
          <template #default="scope">
            <el-space>
              <el-button size="small" type="primary" circle>
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button size="small" type="warning" circle>
                <el-icon><Refresh /></el-icon>
              </el-button>
              <el-button size="small" type="danger" circle>
                <el-icon><Delete /></el-icon>
              </el-button>
            </el-space>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <el-card class="demo-card" style="margin-top: 20px;">
      <template #header>
        <h3>Theme Variables Demo</h3>
      </template>
      <div class="demo-content">
        <div class="color-demo">
          <div class="color-item">
            <div class="color-box primary"></div>
            <span>Primary</span>
          </div>
          <div class="color-item">
            <div class="color-box bg-primary"></div>
            <span>Background</span>
          </div>
          <div class="color-item">
            <div class="color-box text-primary"></div>
            <span>Text</span>
          </div>
          <div class="color-item">
            <div class="color-box border-base"></div>
            <span>Border</span>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useThemeStore } from '../stores/theme'
import { Edit, Refresh, Delete } from '@element-plus/icons-vue'
import ThemeToggle from '../components/ThemeToggle.vue'

// Theme store
const themeStore = useThemeStore()
const { isDark } = storeToRefs(themeStore)

// Computed style for table headers - borderless design
const headerCellStyle = computed(() => ({
  background: isDark.value ? 'var(--theme-fill-dark)' : 'var(--theme-fill-light)',
  color: 'var(--theme-text-primary)',
  fontWeight: '600',
  border: 'none'
}))

// Test data
const testData = [
  {
    id: 1,
    email: '<EMAIL>',
    used: '45.2 GB',
    total: '100 GB',
    percentage: 45.2,
    status: 'Active'
  },
  {
    id: 2,
    email: '<EMAIL>',
    used: '78.9 GB',
    total: '100 GB',
    percentage: 78.9,
    status: 'Active'
  },
  {
    id: 3,
    email: '<EMAIL>',
    used: '12.5 GB',
    total: '50 GB',
    percentage: 25.0,
    status: 'Inactive'
  },
  {
    id: 4,
    email: '<EMAIL>',
    used: '0 GB',
    total: '200 GB',
    percentage: 0,
    status: 'Active'
  }
]
</script>

<style scoped>
.theme-test {
  padding: 20px;
  background-color: var(--theme-bg-secondary);
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.table-card {
  border-radius: 8px;
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  box-shadow: var(--theme-shadow-light);
}

.demo-card {
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
}

.demo-content {
  padding: 20px;
}

.color-demo {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.color-box {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  border: 2px solid var(--theme-border-base);
}

.color-box.primary {
  background-color: var(--theme-primary);
}

.color-box.bg-primary {
  background-color: var(--theme-bg-primary);
}

.color-box.text-primary {
  background-color: var(--theme-text-primary);
}

.color-box.border-base {
  background-color: var(--theme-border-base);
}

.color-item span {
  font-size: 12px;
  color: var(--theme-text-regular);
  font-weight: 500;
}

/* Clean borderless table styling matching ForwardEndpointManagement */
:deep(.el-table) {
  border-radius: 8px;
  background-color: var(--theme-bg-primary);
  border: none;
}

:deep(.el-table th) {
  font-weight: 600;
  background-color: var(--theme-fill-light) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-extra-light) !important;
  transition: none;
}

:global(.dark) :deep(.el-table th) {
  background-color: var(--theme-fill-dark) !important;
  color: var(--theme-text-primary) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table td) {
  background-color: var(--theme-bg-primary) !important;
  color: var(--theme-text-regular) !important;
  border: none !important;
}

:global(.dark) :deep(.el-table tr:hover td) {
  background-color: var(--theme-fill-light) !important;
}

.traffic-progress {
  padding: 0 10px;
}

.traffic-progress .progress-wrapper {
  background-color: var(--theme-fill-light);
  border: 1px solid var(--theme-border-light);
  border-radius: 6px;
  height: 26px;
  width: 100%;
  position: relative;
  overflow: hidden;
  transition: var(--theme-transition);
}

.traffic-progress .progress-bar {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  transition: width 0.3s ease, background 0.3s ease;
  border-radius: 5px;
  box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.2);
}

.traffic-progress .progress-bar.zero-progress {
  width: 0 !important;
}

.traffic-progress .progress-text {
  position: absolute;
  left: 8px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--theme-text-regular);
  font-size: 12px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  font-weight: 500;
  white-space: nowrap;
  z-index: 2;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

:global(.dark) .traffic-progress .progress-wrapper {
  background-color: var(--theme-fill-darker);
  border-color: var(--theme-border-dark);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.3);
}

:global(.dark) .traffic-progress .progress-bar {
  background: linear-gradient(90deg, var(--theme-primary) 0%, var(--theme-primary-lighter) 100%);
  box-shadow: inset 0 1px 2px rgba(255, 255, 255, 0.1);
}

:global(.dark) .traffic-progress .progress-text {
  color: var(--theme-text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.status-tag {
  font-weight: 500;
}
</style>
