<template>
  <div class="loading-test">
    <div class="header">
      <h2>Loading Animations Test</h2>
      <div class="theme-controls">
        <ThemeToggle variant="button" />
      </div>
    </div>

    <div class="test-grid">
      <!-- Table Loading Test -->
      <el-card class="test-card">
        <template #header>
          <h3>Table Loading</h3>
        </template>
        <el-table
          :data="tableData"
          v-loading="tableLoading"
          style="width: 100%"
          element-loading-text="Loading table data..."
        >
          <el-table-column prop="id" label="ID" width="80" />
          <el-table-column prop="name" label="Name" />
          <el-table-column prop="status" label="Status" />
        </el-table>
        <div class="test-controls">
          <el-button @click="toggleTableLoading" type="primary">
            {{ tableLoading ? 'Stop Loading' : 'Start Loading' }}
          </el-button>
        </div>
      </el-card>

      <!-- Card Loading Test -->
      <el-card class="test-card" v-loading="cardLoading" element-loading-text="Loading card content...">
        <template #header>
          <h3>Card Loading</h3>
        </template>
        <div class="card-content">
          <p>This card demonstrates loading overlay on card components.</p>
          <p>The loading mask should have proper opacity and blur effects.</p>
        </div>
        <div class="test-controls">
          <el-button @click="toggleCardLoading" type="primary">
            {{ cardLoading ? 'Stop Loading' : 'Start Loading' }}
          </el-button>
        </div>
      </el-card>

      <!-- Button Loading Test -->
      <el-card class="test-card">
        <template #header>
          <h3>Button Loading States</h3>
        </template>
        <div class="button-grid">
          <el-button type="primary" :loading="buttonLoading" @click="toggleButtonLoading">
            Primary Button
          </el-button>
          <el-button type="success" :loading="buttonLoading" @click="toggleButtonLoading">
            Success Button
          </el-button>
          <el-button type="warning" :loading="buttonLoading" @click="toggleButtonLoading">
            Warning Button
          </el-button>
          <el-button type="danger" :loading="buttonLoading" @click="toggleButtonLoading">
            Danger Button
          </el-button>
          <el-button type="info" :loading="buttonLoading" @click="toggleButtonLoading">
            Info Button
          </el-button>
          <el-button :loading="buttonLoading" @click="toggleButtonLoading">
            Default Button
          </el-button>
        </div>
        <div class="test-controls">
          <el-button @click="toggleButtonLoading" type="primary">
            {{ buttonLoading ? 'Stop Button Loading' : 'Start Button Loading' }}
          </el-button>
        </div>
      </el-card>

      <!-- Form Loading Test -->
      <el-card class="test-card">
        <template #header>
          <h3>Form Loading</h3>
        </template>
        <el-form v-loading="formLoading" element-loading-text="Submitting form...">
          <el-form-item label="Name">
            <el-input v-model="formData.name" placeholder="Enter name" />
          </el-form-item>
          <el-form-item label="Email">
            <el-input v-model="formData.email" placeholder="Enter email" />
          </el-form-item>
          <el-form-item label="Message">
            <el-input
              v-model="formData.message"
              type="textarea"
              placeholder="Enter message"
              :rows="3"
            />
          </el-form-item>
        </el-form>
        <div class="test-controls">
          <el-button @click="toggleFormLoading" type="primary">
            {{ formLoading ? 'Stop Loading' : 'Start Loading' }}
          </el-button>
        </div>
      </el-card>

      <!-- Dialog Loading Test -->
      <el-card class="test-card">
        <template #header>
          <h3>Dialog Loading</h3>
        </template>
        <div class="test-controls">
          <el-button @click="showDialog = true" type="primary">
            Open Dialog with Loading
          </el-button>
        </div>
      </el-card>

      <!-- Page Loading Test -->
      <el-card class="test-card">
        <template #header>
          <h3>Page Loading</h3>
        </template>
        <div class="test-controls">
          <el-button @click="togglePageLoading" type="primary">
            {{ pageLoading ? 'Stop Page Loading' : 'Start Page Loading' }}
          </el-button>
        </div>
      </el-card>
    </div>

    <!-- Dialog with Loading -->
    <el-dialog
      v-model="showDialog"
      title="Dialog Loading Test"
      width="500px"
      v-loading="dialogLoading"
      element-loading-text="Loading dialog content..."
    >
      <div class="dialog-content">
        <p>This dialog demonstrates loading overlay on modal components.</p>
        <p>The loading should have proper dark mode styling.</p>
      </div>
      <template #footer>
        <el-button @click="showDialog = false">Cancel</el-button>
        <el-button @click="toggleDialogLoading" type="primary">
          {{ dialogLoading ? 'Stop Loading' : 'Start Loading' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- Page Loading Overlay -->
    <div v-if="pageLoading" class="page-loading-overlay">
      <div class="page-loading-content">
        <el-icon class="loading-spinner"><Loading /></el-icon>
        <div class="loading-text">Loading page content...</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { Loading } from '@element-plus/icons-vue'
import ThemeToggle from '../components/ThemeToggle.vue'

// Loading states
const tableLoading = ref(false)
const cardLoading = ref(false)
const buttonLoading = ref(false)
const formLoading = ref(false)
const dialogLoading = ref(false)
const pageLoading = ref(false)
const showDialog = ref(false)

// Test data
const tableData = ref([
  { id: 1, name: 'Test Item 1', status: 'Active' },
  { id: 2, name: 'Test Item 2', status: 'Inactive' },
  { id: 3, name: 'Test Item 3', status: 'Active' }
])

const formData = ref({
  name: '',
  email: '',
  message: ''
})

// Toggle functions
const toggleTableLoading = () => {
  tableLoading.value = !tableLoading.value
  if (tableLoading.value) {
    setTimeout(() => {
      tableLoading.value = false
    }, 3000)
  }
}

const toggleCardLoading = () => {
  cardLoading.value = !cardLoading.value
  if (cardLoading.value) {
    setTimeout(() => {
      cardLoading.value = false
    }, 3000)
  }
}

const toggleButtonLoading = () => {
  buttonLoading.value = !buttonLoading.value
  if (buttonLoading.value) {
    setTimeout(() => {
      buttonLoading.value = false
    }, 2000)
  }
}

const toggleFormLoading = () => {
  formLoading.value = !formLoading.value
  if (formLoading.value) {
    setTimeout(() => {
      formLoading.value = false
    }, 3000)
  }
}

const toggleDialogLoading = () => {
  dialogLoading.value = !dialogLoading.value
  if (dialogLoading.value) {
    setTimeout(() => {
      dialogLoading.value = false
    }, 3000)
  }
}

const togglePageLoading = () => {
  pageLoading.value = !pageLoading.value
  if (pageLoading.value) {
    setTimeout(() => {
      pageLoading.value = false
    }, 4000)
  }
}

// Auto-start dialog loading when dialog opens
watch(showDialog, (newVal) => {
  if (newVal) {
    setTimeout(() => {
      dialogLoading.value = true
      setTimeout(() => {
        dialogLoading.value = false
      }, 2000)
    }, 500)
  }
})
</script>

<style scoped>
.loading-test {
  padding: 20px;
  background-color: var(--theme-bg-secondary);
  min-height: 100vh;
  transition: background-color 0.3s ease;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h2 {
  margin: 0;
  color: var(--theme-text-primary);
}

.test-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.test-card {
  background-color: var(--theme-card-bg);
  border: 1px solid var(--theme-border-base);
  min-height: 300px;
}

.test-card h3 {
  margin: 0;
  color: var(--theme-text-primary);
  font-size: 16px;
  font-weight: 600;
}

.card-content {
  padding: 20px 0;
  color: var(--theme-text-regular);
  line-height: 1.6;
}

.button-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 12px;
  margin-bottom: 20px;
}

.test-controls {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid var(--theme-border-light);
}

.dialog-content {
  padding: 20px 0;
  color: var(--theme-text-regular);
  line-height: 1.6;
}

.page-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--loading-background);
  backdrop-filter: blur(4px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.page-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.page-loading-content .loading-spinner {
  font-size: 48px;
  color: var(--loading-spinner-color);
  animation: loading-rotate 1.2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

.page-loading-content .loading-text {
  font-size: 16px;
  color: var(--loading-text-color);
  font-weight: 500;
}

@keyframes loading-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
