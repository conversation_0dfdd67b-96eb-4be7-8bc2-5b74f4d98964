{"name": "zfc-web-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vueuse/core": "^12.0.0", "axios": "^1.7.8", "element-plus": "^2.9.0", "jwt-decode": "^4.0.0", "pinia": "^2.2.8", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuedraggable": "^4.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "vite": "^6.0.1"}}