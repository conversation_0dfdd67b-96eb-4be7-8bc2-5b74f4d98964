#!/bin/bash

# 设置变量
IMAGE_NAME="zfc-web-ui"
CONTAINER_NAME="zfc-web-ui"
# make port configurable from command line
PORT=${2:-80}
API_URL=${1:-"http://localhost:3030/api"}  # 使用第一个参数作为 API URL，默认为 http://localhost:3030/api

# 停止并删除旧容器
echo "Stopping and removing old container..."
docker stop $CONTAINER_NAME 2>/dev/null || true
docker rm $CONTAINER_NAME 2>/dev/null || true

# 构建新镜像
echo "Building new image..."
docker build -t $IMAGE_NAME \
  --build-arg VITE_API_URL=$API_URL \
  .

# 启动新容器
echo "Starting new container..."
docker run -d \
  --name $CONTAINER_NAME \
  -p $PORT:80 \
  --restart unless-stopped \
  $IMAGE_NAME

echo "Deployment completed!"
echo "The web UI is now available at http://localhost:$PORT"
echo "API URL is set to: $API_URL" 