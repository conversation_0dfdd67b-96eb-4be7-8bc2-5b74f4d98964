-- Database optimization recommendations for subscription search functionality
-- These indexes will improve performance for the new search features

-- 1. Composite index for common search combinations
-- This index will help when searching by multiple criteria together
CREATE INDEX IF NOT EXISTS idx_subscription_search_combo 
ON "Subscription" ("activated", "validUntil", "ownerAddress");

-- 2. Case-insensitive index for email search
-- This will improve performance for fuzzy email searches
CREATE INDEX IF NOT EXISTS idx_subscription_owner_address_lower 
ON "Subscription" (LOWER("ownerAddress"));

-- 3. Index for lines array operations
-- This will improve performance when filtering by lines (ANY logic)
CREATE INDEX IF NOT EXISTS idx_subscription_lines_gin 
ON "Subscription" USING GIN ("lines");

-- 4. Index for date range queries on validUntil
-- This will improve performance for date range filtering
CREATE INDEX IF NOT EXISTS idx_subscription_valid_until_range 
ON "Subscription" ("validUntil") WHERE "validUntil" IS NOT NULL;

-- 5. Index for lastReset date calculations
-- This will help with next reset date filtering calculations
CREATE INDEX IF NOT EXISTS idx_subscription_last_reset 
ON "Subscription" ("lastReset", "resetDays") WHERE "lastReset" IS NOT NULL;

-- 6. Partial index for active subscriptions
-- Most searches will be on active subscriptions
CREATE INDEX IF NOT EXISTS idx_subscription_active 
ON "Subscription" ("id", "tokenId", "ownerAddress") WHERE "activated" = true;

-- 7. Index for exact token ID searches (already exists as unique, but adding note)
-- The existing unique index on tokenId already provides optimal performance for exact token searches

-- Performance Notes:
-- 1. The GIN index on lines array will significantly improve performance for lines filtering
-- 2. The case-insensitive email index will make fuzzy email searches much faster
-- 3. Composite indexes help when multiple search criteria are used together
-- 4. Partial indexes reduce index size and improve performance for common query patterns

-- Query optimization tips:
-- 1. ID searches are already optimal due to primary key
-- 2. Token ID searches are optimal due to unique index
-- 3. Email searches will benefit from the case-insensitive index
-- 4. Date range queries will use the specialized date indexes
-- 5. Lines filtering will use the GIN index for array operations

-- Monitoring recommendations:
-- 1. Monitor query performance using EXPLAIN ANALYZE
-- 2. Consider adding more specific indexes based on actual usage patterns
-- 3. Regularly update table statistics for optimal query planning
