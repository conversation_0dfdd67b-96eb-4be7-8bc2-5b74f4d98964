[package]
name = "realm_core"
version = "0.4.0"
authors = ["Realm Contributors"]
description = "Realm's core facilities."
repository = "https://github.com/zhboner/realm"
readme = "README.md"
documentation = "https://docs.rs/realm_core"
keywords = ["network", "relay", "zero-copy", "websocket", "tls"]
edition = "2021"
license = "MIT"

[dependencies]
# realm
realm_io = { version = "0.5" }
realm_syscall = "0.1"
realm_hook = { version = "0.1", optional = true }
realm_lb = { version = "0.1", optional = true }
kaminari = { version = "0.12", features = ["ws", "tls", "mix"], optional = true }

# other
futures = "0.3"
log = "0.4"
bytes = { version = "1", optional = true }
once_cell = "1"
pin-project = "1"
hickory-resolver = "0.24"
tokio = { version = "1.9", features = ["rt", "net", "time"] }
proxy-protocol = { version = "0.5", optional = true }

[features]
default = []
hook = ["realm_hook"]
balance = ["realm_lb"]
brutal-shutdown = ["realm_io/brutal-shutdown"]
transport = ["kaminari"]
transport-boost = []
proxy = ["proxy-protocol", "bytes", "tokio/io-util"]
batched-udp = []
multi-thread = []

[dev-dependencies]
env_logger = "0.11"
tokio = { version = "1", features = ["macros"] }
