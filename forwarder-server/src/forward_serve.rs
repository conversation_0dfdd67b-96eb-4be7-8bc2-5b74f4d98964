use std::{path::Path, sync::Arc};

use common::{
    app_message::{LatencyTestMethod, Mode},
    calc_hash_id, get_bind_addr,
    hammer_message::{
        SetupTotServerReq, SetupTotServerRst, StopTotServerReq, StopTotServerRst,
        SETUP_TOT_SERVER_REQ, STOP_TOT_SERVER_REQ,
    },
    into_socket_addr,
    server_provider::{create_server_selector_direct, ServerSelector},
    tokio_util::sync::{CancellationToken, DropGuard},
    PingClient,
};
use flexi_logger::{FileSpec, Logger, WriteMode};
use private_tun::{
    address::Address,
    session_manager::TotServerBackend,
    snell_impl_ver::{
        relayer::EncryptIO,
        server_plugin::{MessageCodec, ResultFlow, ServerMessagePlugin},
        server_run::run_with_config_file,
    },
};
use socket2::TcpKeepalive;
use tcp_over_multi_tcp_server::{config::ServerConfig, run_tot_server};
use tokio::{
    net::TcpStream,
    sync::{Mutex, OnceCell, Semaphore},
    task::JoinHandle,
};

pub fn run_forward_serve<P: AsRef<Path>>(
    config_path: &P,
    ping_client: Arc<PingClient>,
    conn_sem: Arc<Semaphore>,
) -> anyhow::Result<()> {
    let _h = Logger::try_with_env_or_str("info")?
        .log_to_file(FileSpec::default().basename("server.log"))
        .rotate(
            flexi_logger::Criterion::Size(1024 * 1024 * 10),
            flexi_logger::Naming::Numbers,
            flexi_logger::Cleanup::KeepLogFiles(10),
        )
        .format_for_files(|buf, _now, record| {
            writeln!(
                buf,
                "{}.{:03} [T:{:?}-{:?}][{}] - {}",
                chrono::Local::now().format("%Y-%m-%d %H:%M:%S"),
                chrono::Local::now().timestamp_subsec_millis(),
                std::thread::current().id(),
                std::thread::current().name(),
                record.level(),
                record.args()
            )
        })
        .write_mode(WriteMode::Async)
        .start()?;
    let plugin = Arc::new(TotSetupPlugin::new(ping_client, conn_sem));
    run_with_config_file(
        config_path
            .as_ref()
            .to_str()
            .expect("config path is not valid"),
        Arc::new(vec![Box::new(plugin)]),
    )?;
    Ok(())
}

struct TotSession {
    bind_port: u16,
    _cancel_token: DropGuard,
}
impl TotSession {
    pub async fn new(
        target_list: Vec<Address>,
        mode: Mode,
        latency_test_method: LatencyTestMethod,
        ping_client: Arc<PingClient>,
        conn_sem: Arc<Semaphore>,
    ) -> anyhow::Result<Self> {
        let cancel_token = CancellationToken::new();
        let selector = create_server_selector_direct(
            target_list.clone(),
            Some(mode),
            Some(latency_test_method),
            cancel_token.clone(),
            ping_client,
            conn_sem,
        )
        .await?;
        let cancel_token_clone = cancel_token.clone();
        let selector_clone = selector.clone();
        let (bind_addr_tx, bind_addr_rx) = tokio::sync::oneshot::channel();
        let local_bind = get_bind_addr();
        tokio::spawn(async move {
            let config = ServerConfig {
                dual_stack: Some(true),
                local_bind,
                remote_bind: format!("127.0.0.1:100").parse().unwrap(),
                server_meta: None,
                send_mbps: None,
                brutal: None,
                enable_affinity: None,
                session_config: None,
            };
            let backend = TotBackend {
                selector: selector_clone,
                cancel_token: cancel_token_clone.clone(),
            };
            tokio::spawn(async move {
                match run_tot_server(config, backend, cancel_token_clone, bind_addr_tx, true).await
                {
                    Ok(_) => {}
                    Err(e) => {
                        log::error!("run tot server failed: {:?}", e);
                    }
                }
            });
        });
        let bind_addr = bind_addr_rx.await?;
        Ok(Self {
            bind_port: bind_addr.port(),
            _cancel_token: cancel_token.drop_guard(),
        })
    }
}

struct TotSetupPlugin {
    ping_client: Arc<PingClient>,
    session: Arc<Mutex<lru_time_cache::LruCache<String, TotSession>>>,
    monitor_task: OnceCell<JoinHandle<()>>,
    conn_sem: Arc<Semaphore>,
}

impl TotSetupPlugin {
    fn new(ping_client: Arc<PingClient>, conn_sem: Arc<Semaphore>) -> Self {
        let session = Arc::new(Mutex::new(lru_time_cache::LruCache::with_expiry_duration(
            std::time::Duration::from_secs(300), // 一个服务最多维持300秒，正常情况应该会删除，如果长时间不使用，会自动给删除
        )));
        Self {
            ping_client,
            session,
            monitor_task: OnceCell::new(),
            conn_sem,
        }
    }
}

#[async_trait::async_trait]
impl ServerMessagePlugin for TotSetupPlugin {
    async fn on_message(&self, msg_id: u8, io: &mut EncryptIO) -> anyhow::Result<ResultFlow> {
        if msg_id == SETUP_TOT_SERVER_REQ {
            let _ = self.monitor_task.get_or_init(|| {
                let session_clone = self.session.clone();
                async move {
                    tokio::spawn(async move {
                        loop {
                            for _ in session_clone.lock().await.iter() {
                                // log::info!("tot setup plugin session: {:?}", i);
                            }
                            log::info!(
                                "tot setup plugin monitor task current session count: {}",
                                session_clone.lock().await.len()
                            );
                            tokio::time::sleep(std::time::Duration::from_secs(600)).await;
                        }
                    })
                }
            });
            let req = SetupTotServerReq::decode(io).await?;
            let hash_id = calc_hash_id(&req.remote_list, &req.mode, &req.latency_test_method);
            log::info!("tot setup req: {:?} hash_id: {}", req, hash_id);
            let port = self.session.lock().await.get(&hash_id).map(|s| s.bind_port);
            let port = match port {
                Some(p) => p,
                None => {
                    log::info!(
                        "tot setup req session not found, create new session for hash_id: {}",
                        hash_id
                    );
                    let session = TotSession::new(
                        req.remote_list,
                        req.mode,
                        req.latency_test_method,
                        self.ping_client.clone(),
                        self.conn_sem.clone(),
                    )
                    .await?;
                    let port = session.bind_port;
                    self.session.lock().await.insert(hash_id, session);
                    port
                }
            };
            let rst = SetupTotServerRst { server_bind: port };
            SetupTotServerRst::encode(io, &rst).await?;
            Ok(ResultFlow::Handled)
        } else if msg_id == STOP_TOT_SERVER_REQ {
            let req = StopTotServerReq::decode(io).await?;
            log::info!("tot stop req: {:?}", req);
            let hash_id = calc_hash_id(&req.remote_list, &req.mode, &req.latency_test_method);
            let port = self.session.lock().await.get(&hash_id).map(|s| s.bind_port);
            let succ = if let Some(port) = port {
                if port == req.server_bind {
                    log::info!("tot stop session: {:?}", port);
                    self.session.lock().await.remove(&hash_id);
                    0
                } else {
                    log::error!("tot stop req server bind not match");
                    1
                }
            } else {
                log::error!("tot stop req session not found");
                2
            };
            StopTotServerRst::encode(io, &StopTotServerRst { error_code: succ }).await?;
            Ok(ResultFlow::Handled)
        } else {
            log::info!("tot setup plugin on message not handled msg_id: {}", msg_id);
            Ok(ResultFlow::Passthrough)
        }
    }
}

struct TotBackend {
    selector: ServerSelector,
    cancel_token: CancellationToken,
}

impl TotServerBackend for TotBackend {
    type IO = TcpStream;
    async fn create_io(&self) -> std::io::Result<Self::IO> {
        if self.cancel_token.is_cancelled() {
            return Err(std::io::Error::new(std::io::ErrorKind::Other, "canceled"));
        }
        let target = self
            .selector
            .get_target_info(None)
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?
            .target
            .clone();
        let socket_addr = into_socket_addr(&target)
            .await
            .map_err(|e| std::io::Error::new(std::io::ErrorKind::Other, e))?;
        let stream = TcpStream::connect(socket_addr).await?;
        stream.set_nodelay(true)?;
        let sock_ref = socket2::SockRef::from(&stream);
        #[cfg(target_os = "linux")]
        let _ = sock_ref.set_tcp_user_timeout(Some(std::time::Duration::from_secs(302)));
        sock_ref.set_tcp_keepalive(
            &TcpKeepalive::new()
                .with_time(std::time::Duration::from_secs(300))
                .with_interval(std::time::Duration::from_secs(2))
                .with_retries(1),
        )?;
        Ok(stream)
    }
}
