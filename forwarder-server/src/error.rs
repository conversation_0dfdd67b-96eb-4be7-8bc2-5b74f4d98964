use serde::Serialize;
use thiserror::Error;
use warp::{
    reject::{Reject, Rejection},
    reply::Reply,
};
#[derive(Error, Debug)]
pub enum WebError {
    #[error("Dns resolve error: {0} for {1}")]
    DnsResolveError(String, String),
    #[error("Dns resolve timeout for {0}")]
    DnsResolveTimeout(String),
    #[error("Empty ip address for {0}")]
    EmptyIpAddr(String),
    #[error("Internal error: {0}")]
    InternalError(String),
}

impl Reject for WebError {}

#[derive(Serialize)]
struct ErrorResponse {
    message: String,
    error_code: String,
}

pub async fn handle_rejection(err: Rejection) -> Result<impl Reply, Rejection> {
    let (code, message) = if err.is_not_found() {
        (warp::http::StatusCode::NOT_FOUND, "Not Found".to_string())
    } else if let Some(e) = err.find::<WebError>() {
        match e {
            WebError::DnsResolveError(err, ip) => (
                warp::http::StatusCode::BAD_REQUEST,
                format!("Dns resolve error: {}, for ip: {}", err, ip),
            ),
            WebError::DnsResolveTimeout(ip) => (
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                format!("Dns resolve timeout for ip: {}", ip),
            ),
            WebError::EmptyIpAddr(ip) => (
                warp::http::StatusCode::BAD_REQUEST,
                format!("Empty ip address for ip: {}", ip),
            ),
            WebError::InternalError(err) => (
                warp::http::StatusCode::INTERNAL_SERVER_ERROR,
                format!("Internal error: {}", err),
            ),
        }
    } else {
        (
            warp::http::StatusCode::INTERNAL_SERVER_ERROR,
            "Internal Server Error".to_string(),
        )
    };

    let json = warp::reply::json(&ErrorResponse {
        message,
        error_code: code.to_string(),
    });

    Ok(warp::reply::with_status(json, code))
}
