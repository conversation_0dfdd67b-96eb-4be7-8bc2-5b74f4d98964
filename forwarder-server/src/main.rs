use std::{convert::Infallible, net::IpAddr, sync::Arc, time::Duration};

use anyhow::Result;
use clap::Parser;
use common::{app_message::PingResult, constant::TCP_CONNECT_TIMEOUT, PingClient};
use error::{handle_rejection, WebError};
use forward_serve::run_forward_serve;
use futures_util::future::try_join_all;
use serde::{Deserialize, Serialize};
use tokio::sync::Semaphore;
use warp::Filter;
mod error;
mod forward_serve;

#[derive(Debug, Parser)]
pub struct Options {
    // #[arg(short, long, env)]
    // ws_endpoint: String,
    // #[arg(short, long, env)]
    // ws_port: u16,
    // #[arg(short, long, env)]
    // ws_privkey: String, // ws私钥，用来通信
    #[arg(short, long, env)]
    server_config_path: String, // 服务配置文件路径

    #[arg(short, long, env)]
    web_endpoint: Option<String>, // 前端服务的地址
    #[arg(short, long, env)]
    web_port: Option<u16>, // 前端服务的端口

    #[arg(short, long, env)]
    conn_sem_size: Option<usize>, // 连接信号量大小
}

async fn handle_ping(
    query: TargetForPing,
    ctx: AppContext,
) -> Result<impl warp::Reply, warp::Rejection> {
    log::info!("Ping request: {:?}", query);
    let query = query.target.clone();
    let remote_ip = if let Ok(ping_remote_addr) = query.parse::<IpAddr>() {
        ping_remote_addr
    } else {
        let ip_resolved = tokio::time::timeout(
            Duration::from_secs(TCP_CONNECT_TIMEOUT),
            tokio::net::lookup_host(format!("{}:80", query)),
        )
        .await
        .map_err(|_| warp::reject::custom(WebError::DnsResolveTimeout(query.clone())))?
        .map_err(|e| {
            warp::reject::custom(WebError::DnsResolveError(e.to_string(), query.clone()))
        })?;
        let ip = ip_resolved
            .into_iter()
            .next()
            .ok_or(warp::reject::custom(WebError::EmptyIpAddr(query.clone())))?;
        ip.ip()
    };
    let client = if remote_ip.is_ipv4() {
        ctx.ping_client.v4_client()
    } else {
        ctx.ping_client.v6_client()
    };
    let mut handles = Vec::with_capacity(3);
    for i in 0..3 {
        let remote_ip_clone = remote_ip.clone();
        let client_clone = client.clone();
        handles.push(tokio::spawn(tokio::time::timeout(
            Duration::from_secs(TCP_CONNECT_TIMEOUT),
            async move {
                let mut pingger = client_clone
                    .pinger(remote_ip_clone, surge_ping::PingIdentifier(i))
                    .await;
                pingger.ping(surge_ping::PingSequence(i), &[0; 64]).await
            },
        )));
    }

    let results = try_join_all(handles).await.map_err(|e| {
        log::error!("Failed to ping: {:?}", e);
        warp::reject::custom(WebError::InternalError(e.to_string()))
    })?;

    if results.iter().all(|r| r.is_err()) {
        return Err(warp::reject::custom(WebError::InternalError(format!(
            "Ping:{} all timeout",
            query
        ))));
    }
    let rtt_vec = results
        .into_iter()
        .filter_map(|r| match r {
            Ok(Ok(result)) => Some(result.1.as_micros() as u64),
            _ => None,
        })
        .collect::<Vec<_>>();
    if rtt_vec.is_empty() {
        return Err(warp::reject::custom(WebError::InternalError(format!(
            "Ping:{} result is empty",
            query
        ))));
    }
    let rtt = rtt_vec.iter().sum::<u64>() / rtt_vec.len() as u64;
    let result = serde_json::to_string(&PingResult { ip: query, rtt })
        .map_err(|e| warp::reject::custom(WebError::InternalError(e.to_string())))?;
    Ok(result)
}

#[derive(Debug, Serialize, Deserialize)]
struct TargetForPing {
    target: String,
}

struct Context {
    ping_client: Arc<PingClient>,
}

type AppContext = Arc<Context>;

fn with_app_ctx(
    app_ctx: AppContext,
) -> impl Filter<Extract = (AppContext,), Error = Infallible> + Clone {
    warp::any().map(move || app_ctx.clone())
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let opt = Options::parse();
    let server_config_path = opt.server_config_path.clone();
    let ping_client = Arc::new(PingClient::new());
    let ping_client_clone = ping_client.clone();
    let conn_sem = Arc::new(Semaphore::new(opt.conn_sem_size.unwrap_or(100)));
    let _h = std::thread::spawn(move || {
        let mut cnt = 0;
        loop {
            if let Err(e) = run_forward_serve(
                &server_config_path,
                ping_client_clone.clone(),
                conn_sem.clone(),
            ) {
                log::error!("run forward serve error: {e:?}");
            }
            cnt += 1;
            if cnt > 10 {
                std::process::exit(1);
            }
            std::thread::sleep(Duration::from_secs(10));
        }
    });
    if let (Some(web_endpoint), Some(web_port)) = (opt.web_endpoint, opt.web_port) {
        let ctx = Arc::new(Context {
            ping_client: ping_client.clone(),
        });
        let ping_route = warp::path("ping")
            .and(warp::query::<TargetForPing>())
            .and(with_app_ctx(ctx.clone()))
            .and_then(handle_ping);
        let test_route = warp::path("test").map(|| "Ok");
        let routes = ping_route
            .or(test_route)
            .recover(handle_rejection)
            .with(warp::cors().allow_any_origin())
            .with(warp::log("forwarder-server"));

        // let endpoint = format!("{}:{}", opt.web_endpoint, opt.web_port);

        warp::serve(routes)
            .run((web_endpoint.parse::<IpAddr>()?, web_port))
            .await;
    } else {
        let _ = tokio::signal::ctrl_c().await;
        log::info!("Ctrl+C received, shutting down...");
    }

    Ok(())
}
