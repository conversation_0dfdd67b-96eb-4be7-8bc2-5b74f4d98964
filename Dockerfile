FROM rust:bookworm AS builder
ARG DEBIAN_FRONTEND=noninteractive
env HTTP_PROXY=socks5://*********:10913
env HTTPS_PROXY=socks5://*********:10913
# 禁用签名验证以更新和安装必要的包
RUN apt-get clean && apt-get update
RUN apt-get install -y --no-install-recommends \
  build-essential \
  protobuf-compiler \
  llvm \
  clang \
  libclang-dev \
  libssl-dev \
  git-core \
  pkg-config \
  && apt-get clean \
  && rm -rf /tmp/* /var/tmp/*

WORKDIR /builder
COPY . .

RUN --mount=type=cache,target=/usr/local/cargo/registry cargo prisma generate
RUN --mount=type=cache,target=/usr/local/cargo/registry cargo install bpf-linker
RUN --mount=type=cache,target=/usr/local/cargo/registry cargo xtask build-ebpf --release
RUN --mount=type=cache,target=/usr/local/cargo/registry cargo build --release

FROM debian:bookworm-slim
env HTTP_PROXY=socks5://*********:10913
env HTTPS_PROXY=socks5://*********:10913
ARG DEBIAN_FRONTEND=noninteractive
RUN apt-get update
RUN apt-get install -y --no-install-recommends \
  libssl-dev iproute2 \
  ca-certificates \
  && apt-get clean \
  && rm -rf /tmp/* /var/tmp/*

WORKDIR /app
COPY --from=builder /builder/target/release/prisma-cli .
COPY --from=builder /builder/target/release/zf-controler .
COPY --from=builder /builder/target/release/zf-worker .
COPY --from=builder /builder/target/release/zfc-util .
