# Server Management Search Functionality Test Plan

## Overview
This document outlines the comprehensive testing plan for the Server Management search functionality that has been implemented following the exact patterns from the Port Management page.

## Features Implemented

### Backend Implementation
1. **ServerSearchRequest Structure** - Added to `message.rs` with fields:
   - `id` (Option<i32>) - Exact ID search
   - `name` (Option<String>) - Fuzzy name search
   - `ip_addr` (Option<String>) - Fuzzy IP address search
   - `version` (Option<Vec<String>>) - Multi-select version filter
   - `status` (Option<Vec<String>>) - Multi-select status filter

2. **Search Handler** - `handle_get_server_list_with_search` in `handlers.rs`:
   - Exact ID search takes priority over other filters
   - Fuzzy matching for name and IP address using regex
   - Multi-select filtering for version and status with ANY logic
   - AND logic between different search criteria
   - Relevance-based result sorting
   - Maintains pagination with filtered results

3. **API Route Updates** - Modified `/server_list` route in `main.rs`:
   - Backward compatibility with existing pagination-only requests
   - Automatic detection of search parameters
   - Proper parameter parsing for arrays (comma-separated)

### Frontend Implementation
1. **API Function** - Updated `getServerList` in `api/index.js`:
   - Accepts search parameters
   - Properly formats array parameters as comma-separated strings
   - Maintains backward compatibility

2. **Search Interface** - Added to `ServerManagement.vue`:
   - Collapsible search form (hidden by default)
   - State persistence in localStorage
   - Smooth animations with `el-collapse-transition`
   - Keyboard accessibility with proper ARIA labels

3. **Search Form Fields**:
   - **ID Field**: Exact search with integer validation
   - **Name Field**: Fuzzy search with regex support
   - **IP Address Field**: Fuzzy search with regex support
   - **Version Filter**: Multi-select dropdown with dynamic options
   - **Status Filter**: Multi-select dropdown (Online/Offline)

4. **Search Logic**:
   - Debounced input processing (500ms delay)
   - Immediate search for dropdown changes
   - Clear search functionality
   - Pagination reset on search
   - Dynamic version collection from server data

## Test Cases

### 1. Exact ID Search Priority
- **Test**: Search by ID should return only exact matches
- **Expected**: Other search criteria are ignored when ID is specified
- **Verification**: Search with ID=1 and name="test" should only return server with ID=1

### 2. Fuzzy Name Matching
- **Test**: Search by partial server name
- **Expected**: Returns servers containing the search term (case-insensitive)
- **Verification**: Search "test" should match "Test Server", "My Test", etc.

### 3. Fuzzy IP Address Matching
- **Test**: Search by partial IP address
- **Expected**: Returns servers with IPs containing the search term
- **Verification**: Search "192.168" should match "***********", "*************", etc.

### 4. Multi-select Version Filter (ANY Logic)
- **Test**: Select multiple versions
- **Expected**: Returns servers matching ANY of the selected versions
- **Verification**: Select ["v1.0", "v2.0"] should return servers with either version

### 5. Multi-select Status Filter (ANY Logic)
- **Test**: Select multiple statuses
- **Expected**: Returns servers matching ANY of the selected statuses
- **Verification**: Select ["Online", "Offline"] should return all servers

### 6. AND Logic Between Different Criteria
- **Test**: Combine name search with version filter
- **Expected**: Returns servers matching BOTH criteria
- **Verification**: Name="test" AND version="v1.0" should return only test servers with v1.0

### 7. Pagination with Search
- **Test**: Search results should be properly paginated
- **Expected**: Page size controls work with filtered results
- **Verification**: Search returning 50 results with page size 20 should show 3 pages

### 8. Search State Persistence
- **Test**: Search form state should persist across page reloads
- **Expected**: Expanded/collapsed state saved in localStorage
- **Verification**: Expand search, reload page, should remain expanded

### 9. Dark Mode Compatibility
- **Test**: Search interface should work properly in dark mode
- **Expected**: Proper contrast and theming in dark mode
- **Verification**: Toggle dark mode, search interface should be clearly visible

### 10. Responsive Design
- **Test**: Search interface should work on mobile devices
- **Expected**: Form fields stack vertically on small screens
- **Verification**: Resize browser to mobile width, form should adapt

## Edge Cases

### 1. Empty Search Results
- **Test**: Search for non-existent data
- **Expected**: Empty table with proper message
- **Verification**: Search for ID=99999 should show "No data"

### 2. Invalid ID Input
- **Test**: Enter non-numeric ID
- **Expected**: Input validation or graceful handling
- **Verification**: Enter "abc" in ID field should not crash

### 3. Special Characters in Search
- **Test**: Search with regex special characters
- **Expected**: Proper escaping or regex matching
- **Verification**: Search for "test." should work correctly

### 4. Large Result Sets
- **Test**: Search returning many results
- **Expected**: Proper pagination and performance
- **Verification**: Search should handle 1000+ results efficiently

### 5. Network Errors
- **Test**: API request failures
- **Expected**: Proper error handling and user feedback
- **Verification**: Disconnect network, search should show error message

## Performance Considerations

1. **Debounced Input**: 500ms delay prevents excessive API calls
2. **Efficient Filtering**: Backend filtering reduces data transfer
3. **Pagination**: Large result sets are properly paginated
4. **Caching**: Version options are cached and updated dynamically

## Accessibility Features

1. **ARIA Labels**: Search container has proper role and aria-label
2. **Keyboard Navigation**: All form elements are keyboard accessible
3. **Screen Reader Support**: Form labels are properly associated
4. **Focus Management**: Logical tab order through form fields

## Browser Compatibility

The implementation uses standard web technologies and should work in:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Conclusion

The Server Management search functionality has been implemented following the exact patterns from the Port Management page, ensuring consistency across the application. All search criteria work with proper priority (ID first), logic (AND between different criteria, ANY within multi-select), and user experience features (debouncing, persistence, responsiveness).
